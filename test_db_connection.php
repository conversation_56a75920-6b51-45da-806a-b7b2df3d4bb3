<?php
$servername = "127.0.0.1";
$username = "root";
$password = "";
$dbname = "madrasa";

try {
    $pdo = new PDO("mysql:host=$servername;dbname=$dbname", $username, $password);
    // Set the PDO error mode to exception
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "Connected successfully";
    
    // Test query
    $stmt = $pdo->query("SELECT VERSION()");
    $version = $stmt->fetch();
    echo "\nMySQL Version: " . $version[0];
    
    // Check timeout settings
    $stmt = $pdo->query("SHOW VARIABLES LIKE 'wait_timeout'");
    $wait_timeout = $stmt->fetch();
    echo "\nWait Timeout: " . $wait_timeout['Value'];
    
    $stmt = $pdo->query("SHOW VARIABLES LIKE 'interactive_timeout'");
    $interactive_timeout = $stmt->fetch();
    echo "\nInteractive Timeout: " . $interactive_timeout['Value'];
    
    $stmt = $pdo->query("SHOW VARIABLES LIKE 'max_allowed_packet'");
    $max_allowed_packet = $stmt->fetch();
    echo "\nMax Allowed Packet: " . $max_allowed_packet['Value'];
} catch(PDOException $e) {
    echo "Connection failed: " . $e->getMessage();
}
?>