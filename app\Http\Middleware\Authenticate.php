<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Auth\Middleware\Authenticate as Middleware;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class Authenticate extends Middleware
{
    /**
     * Get the path the user should be redirected to when they are not authenticated.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return string|null
     */
    protected function redirectTo($request)
    {
        if (! $request->expectsJson()) {
            return route('login');
        }
    }
    
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @param  ...$guards
     * @return mixed
     */
    public function handle($request, Closure $next, ...$guards)
    {
        try {
            // Test database connection before authentication
            DB::connection()->getPdo();
            return parent::handle($request, $next, ...$guards);
        } catch (\Exception $e) {
            Log::error('Database connection failed during authentication: ' . $e->getMessage());
            
            // If it's a login request, allow it to proceed so user can see the login page
            if ($request->is('login') || $request->is('login/*')) {
                return $next($request);
            }
            
            // For other requests, show maintenance page
            if (config('app.env') === 'production') {
                return response()->view('errors.maintenance', [], 503);
            }
            
            // In development, still throw the exception
            throw $e;
        }
    }
}