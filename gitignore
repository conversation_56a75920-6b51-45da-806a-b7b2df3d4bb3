/vendor/
node_modules/
npm-debug.log
yarn-error.log

# <PERSON>vel 4 specific
bootstrap/compiled.php
app/storage/

# <PERSON>vel 5 & <PERSON><PERSON> specific
public/storage
public/hot

# <PERSON>vel 5 & <PERSON><PERSON> specific with changed public path
public_html/storage
public_html/hot

storage/*.key
.env
Homestead.yaml
Homestead.json
/.vagrant
.phpunit.result.cache
/package-lock.json
/.idea
/public/teacher/
storage/framework/views/
.vscode/sftp.json
/storage/
public/source_code/
.vscode/settings.json
