<?php

namespace App\Http\Middleware;

use App\Models\Students;
use App\Models\Teacher;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class CheckRole
{
    /**
    * Handle an incoming request.
    *
    * @param  \Illuminate\Http\Request  $request
    * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
    * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
    */
    public function handle(Request $request, Closure $next)
    {
        if (Auth::user() && Auth::user()->hasRole('Super Admin')) 
        {
            return $next($request);
        }
        Auth::logout();
        return redirect()->route('login')->with('error', 'You are not authorized to access this page.');
    }
}
