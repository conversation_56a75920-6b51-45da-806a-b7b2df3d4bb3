# Frontend Routes Documentation

## Overview
This document outlines all the frontend routes and navigation flow of the Madrasa Admin Panel application.

## Main Routes

### Authentication Routes
- `/login` - Login page
- `/register` - Registration page (if enabled)
- `/logout` - Logout endpoint

### Dashboard Routes
- `/` - Main dashboard (home page)
- `/home` - Alternative dashboard route

### Academic Management
- `/medium` - Medium management
- `/section` - Section management
- `/subject` - Subject management
- `/class` - Class management
- `/class/subject` - Assign subjects to classes
- `/class/teacher` - Assign teachers to classes
- `/subject-teachers` - Subject teacher assignments
- `/stream` - Stream management
- `/shifts` - Shift management

### Student Management
- `/students` - Student list and creation
- `/students/create` - Student admission
- `/students/assign-class` - Assign class to new students
- `/students/create-bulk-data` - Bulk student data import
- `/category` - Student categories
- `/promote-student` - Student promotion between sessions

### Teacher Management
- `/teachers` - Teacher list and creation
- `/teacher/details` - Teacher details

### Parent Management
- `/parents` - Parent management

### Session Management
- `/session-years` - Academic session years

### Timetable Management
- `/timetable` - Timetable creation and management
- `/class-timetable` - Class-wise timetable view
- `/teacher-timetable` - Teacher-wise timetable view

### Attendance Management
- `/attendance` - Attendance recording
- `/attendance/view` - Attendance viewing
- `/attendance/add-bulk-data` - Bulk attendance import

### Lesson Management
- `/lesson` - Lesson creation
- `/lesson-topic` - Lesson topics

### Announcement Management
- `/announcement` - Announcements

### Holiday Management
- `/holiday` - Holidays

### Assignment Management
- `/assignment` - Assignments
- `/assignment/submission` - Assignment submissions

### Slider Management
- `/sliders` - Homepage sliders

### Exam Management
- `/exams` - Exam creation and management
- `/exam-timetable` - Exam timetable
- `/exams/get-result` - Exam results
- `/exams/upload-marks` - Upload exam marks
- `/grades` - Grade configuration

### Online Exam Management
- `/online-exam` - Online exams
- `/online-exam-question` - Online exam questions

### Fees Management
- `/fees-type` - Fees types
- `/fees/classes` - Class fees configuration
- `/fees/paid` - Paid fees tracking
- `/fees-config` - Fees configuration
- `/fees/transaction-logs` - Transaction logs

### Settings
- `/settings` - General settings
- `/fcm-settings` - FCM settings
- `/email-settings` - Email configuration
- `/app-settings` - App settings
- `/language` - Language management
- `/system-update` - System updates
- `/form-fields` - Custom form fields

### User Management
- `/roles` - Role management
- `/users` - User management
- `/resetpassword` - Password reset
- `/edit-profile` - Profile editing

### Notification Management
- `/notifications` - Notifications

## Navigation Flow

### Main Navigation (Sidebar)
1. Dashboard
2. Academics (dropdown)
   - Medium
   - Section
   - Stream
   - Shift
   - Subject
   - Class
   - Assign Class Subject
   - Assign Class Teacher
   - Assign Subject Teacher
   - Assign New Student Class
   - Promote Student
3. Students (dropdown)
   - Admission
   - Custom Fields
   - Assign Roll Number
   - Student Details
   - Student Category
   - Reset Password
   - Add Bulk Data
4. Teachers
   - Create Teacher
   - Teacher Details
5. Parents
6. Session Years
7. Timetable
   - Create Timetable
   - Class Timetable
   - Teacher Timetable
8. Lesson
   - Create Lesson
   - Create Topic
9. Assignment
   - Create Assignment
   - Assignment Submission
10. Attendance
    - Add Attendance
    - View Attendance
    - Add Bulk Attendance
11. Announcement
12. Holiday
13. Exam
    - Create Exam
    - Create Exam Timetable
    - Upload Exam Result
    - Exam Result
    - Grades
14. Online Exam
    - Terms & Conditions
    - Create Online Exam
    - Exam Questions
    - Exam Results
15. Fees
    - Fees Type
    - Class Fees
    - Paid Fees
    - Fees Configuration
    - Transaction Logs
16. Sliders
17. Settings (dropdown)
    - General Settings
    - FCM Settings
    - Email Settings
    - Privacy Policy
    - Terms & Conditions
    - Contact Us
    - About Us
    - App Settings
    - Language
    - System Update
    - Custom Fields
18. Role Permission
    - Show Roles
19. Users
20. Notifications

### Top Navigation (Header)
1. Language Switcher
2. User Profile
   - Update Profile
   - Change Password
   - Sign Out

## Routing Implementation

The application uses Laravel's routing system with Blade templates for server-side rendering. Routes are defined in `routes/web.php` and are protected by authentication and role-based middleware.

### Middleware
- `auth` - Authentication required
- `Role` - Role-based access control
- `language` - Language management
- `CheckRole` - Additional role checking
- `DemoMiddleware` - Demo mode restrictions

### Route Parameters
Most routes follow RESTful conventions with standard CRUD operations implemented through Laravel's resource controllers.

## SPA vs Server-Side Routing

This application primarily uses server-side routing with Laravel Blade templates rather than client-side SPA routing. Each navigation request results in a full page reload with new Blade template rendering.

Vue.js components are used for specific interactive elements within pages but do not implement client-side routing for page navigation.