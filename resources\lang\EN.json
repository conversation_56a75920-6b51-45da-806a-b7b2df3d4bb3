{"The :attribute must contain at least one letter.": "The :attribute must contain at least one letter.", "The :attribute must contain at least one number.": "The :attribute must contain at least one number.", "The :attribute must contain at least one symbol.": "The :attribute must contain at least one symbol.", "The :attribute must contain at least one uppercase and one lowercase letter.": "The :attribute must contain at least one uppercase and one lowercase letter.", "The given :attribute has appeared in a data leak. Please choose a different :attribute.": "The given :attribute has appeared in a data leak. Please choose a different :attribute.", "login": "<PERSON><PERSON>", "username": "Username", "password": "Password", "forgot_password": "Forgot password?", "invalid_credentials": "Invalid username or password", "dashboard": "Dashboard", "general_settings": "General Settings", "system_settings": "System Settings", "role_permission": "Role Permission", "copyright": "Copyright", "all_rights_reserved": "All rights reserved", "school_name": "School Name", "school_address": "Address", "school_phone": "Phone", "school_email": "Email", "time_zone": "Time Zone", "date_formate": "Date Format", "time_formate": "Time Format", "color": "Color", "medium": "Medium", "manage_medium": "Manage Medium", "create_medium": "Create Medium", "list_medium": "List Medium", "edit_medium": "Edit Medium", "teacher": "Teacher", "manage_teacher": "Manage Teacher", "create_teacher": "Create Teacher", "list_teacher": "List Teacher", "edit_teacher": "Edit Teacher", "no": "No", "id": "Id", "name": "Name", "email": "Email", "mobile": "Mobile", "dob": "Date of Birth", "image": "Image", "qualification": "Qualification", "action": "Action", "role_management": "Role Management", "show": "Show", "show_role": "Show Role", "back": "Back", "permission": "Permissions", "submit": "Submit", "cancel": "Cancel", "delete_title": "Are you sure?", "confirm_message": "You wont be able to revert this!", "yes_delete": "Yes, delete it", "yes_change_it_default": "Yes, reset it to default", "academics": "Academics", "manage": "Manage", "create": "Create", "edit": "Edit", "list": "List", "section": "Section", "class": "Class", "subject": "Subject", "subject_group": "Subject Group", "subject_code": "Subject Code", "bg_color": "Background Color", "upload": "Upload", "type": "Type", "data_fetch_successfully": "Data Fetch Successfully", "data_store_successfully": "Data Saved Successfully", "data_update_successfully": "Data Updated Successfully", "data_delete_successfully": "Data Deleted Successfully", "error_occurred": "Oops! Error occurred. Please Try again Later.", "created_at": "Created At", "updated_at": "Updated At", "no_permission_message": "You Don't have enough permissions.", "select_language": "Select Language", "assign_class_subject": "Assign Class Subject", "total_selectable_subjects": "Total Selectable Subjects", "assign_class_teacher": "Assign Class Teacher", "parents": "Parents", "session_years": "Session Year", "status": "Status", "admission_no": "Admission Number", "roll_no": "Roll Number", "caste": "<PERSON><PERSON>", "religion": "Religion", "admission_date": "Admission Date", "blood_group": "Blood Group", "height": "Height", "weight": "Weight", "father": "Father", "mother": "Mother", "occupation": "Occupation", "is_new_admission": "Is New Admission", "category": "Category", "user_id": "User Id", "core_subject": "Core Subjects", "students": "Students", "student_admission": "Students Admission", "student_category": "Students Category", "add_bulk_data": "Add Bulk Data", "assign": "Assign", "timetable": "Timetable", "create_timetable": "Create Timetable", "class_timetable": "Class Timetable", "teacher_timetable": "Teacher Timetable", "subject_lesson": "Subject Lesson", "create_lesson": "C<PERSON> <PERSON><PERSON>", "create_topic": "Create Topic", "lesson_alredy_exists": "Lesson is already exists", "topic_alredy_exists": "Topic is already exists", "student_assignment": "Student Assignment", "create_assignment": "Create Assignment", "assignment_submission": "Assignment Submission", "attendance": "Attendance", "add_attendance": "Add Attendance", "view_attendance": "View Attendance", "announcement": "Announcement", "exam": "Exam", "create_exam": "Create Exam", "create_exam_timetable": "Create Exam Timetable", "upload_exam_result": "Upload <PERSON>am <PERSON>t", "select": "Select", "select_class_section": "Select Class", "father_email": "Father <PERSON><PERSON>", "mother_email": "<PERSON> <PERSON><PERSON>", "no_data_found": "No Data Found.", "first_name": "First Name", "last_name": "Last Name", "current_address": "Current Address", "permanent_address": "Permanent Address", "guardian": "Guardian", "parent_guardian_details": "Parent Guardian Details", "guardian_details": "Guardian Details", "gender": "Gender", "male": "Male", "female": "Female", "date": "Date", "select_lesson": "Select Lesson", "topic_name": "Topic Name", "topic_description": "Topic Description", "select_subject": "Select Subject", "class_section": "Class Section", "description": "Description", "view": "View", "criteria": "Criteria", "lesson": "Lesson", "lesson_name": "Lesson Name", "lesson_description": "Lesson Description", "files": "Files", "file_name": "File Name", "file_upload": "File Upload", "video_upload": "Video Upload", "youtube_link": "Youtube Link", "other_link": "Other Link", "student_id": "Student Id", "holiday_list": "Holiday List", "assignment": "Assignment", "assignment_name": "Assignment Name", "assignment_instructions": "Assignment Instructions", "last_submission_date": "Last Submission Date", "points": "Points", "resubmission_allowed": "Resubmission Allowed", "extra_days_for_resubmission": "Extra Days for Resubmission", "thumbnail": "<PERSON><PERSON><PERSON><PERSON>", "holiday": "Holiday", "title": "Title", "note": "Note", "due_date": "Due Date", "resubmission": "Resubmission", "instructions": "Instructions", "student_name": "Student Name", "accept": "Accept", "reject": "Reject", "assign_to": "Assign To", "for_all": "For All", "sliders": "Sliders", "link": "Link", "start_date": "Start Date", "end_date": "End Date", "add_new_files": "Add New Files", "are_you_sure": "Are You Sure ?", "yes": "Yes", "fcm_key": "FCM Key", "fcm_server_key": "FCM Server Key", "select_class": "Select Class", "has_elective_subject": "Has Elective Subject", "elective_subject": "Elective Subjects", "group": "Group", "close": "Close", "general": "General", "upload_new_files": "Upload New Files", "old_files": "Old Files", "exam_name": "Exam Name", "exam_description": "Exam <PERSON>", "total_marks": "Total Marks", "passing_marks": "Passing Marks", "obtained_marks": "Obtained Marks", "start_time": "Start Time", "end_time": "End Time", "exam_result": "<PERSON><PERSON>", "teacher_review": "Teacher Review", "publish": "Publish", "or": "OR", "email_configuration": "Email Configuration", "mail_mailer": "Mailer", "mail_host": "Host", "mail_port": "Port", "mail_username": "Email", "mail_password": "Password", "mail_encryption": "Encryption", "mail_send_from": "Send From", "privacy_policy": "Privacy - Policy", "contact_us": "Contact Us", "student_details": "Student Details", "about_us": "About Us", "reset_password": "Reset Password", "assign_new_student_class": "Assign New Student Class", "terms_condition": "Terms & Conditions", "promote_student": "Promote Student", "promote_students_in_next_session": "Promote Student In Next Session", "promote_in": "Promote In", "promote_class": "Promote Class", "change_password": "Change Password", "old_password": "Old Password", "new_password": "New Password", "confirm_password": "Confirm Password", "language_settings": "Language Settings", "language_name": "Language Name", "language_code": "Language Code", "upload_file": "Language File", "download_sample": "Download Sample File", "gr_number": "GR Number", "horizontal_logo": "Horizontal Logo", "vertical_logo": "Vertical Logo", "favicon": "Favicon", "role": "Roles", "app_settings": "App Settings", "student_parent_app_settings": "Student/Parent App Settings", "teacher_app_settings": "Teacher App Settings", "app_link": "App Link", "ios_app_link": "iOS App Link", "app_version": "App Version (Android)", "ios_app_version": "App Version (iOS)", "force_app_update": "Force Update App", "app_maintenance": "App Maintenance", "school_tagline": "School Tagline", "noticeboard": "Noticeboard", "system_update": "System Update", "current_version": "Current Version", "system_update_successfully": "System Updated Successfully", "something_wrong_try_again": "Something wrong!. Please Try again later", "system_already_updated": "System is already upto date", "your_version_update_nearest": "Please update nearest version first", "invalid_zip_try_again": "Invalid file, please try again.!", "all": "All", "select_teacher": "Select Teacher", "student_bulk_import": "Student Bulk import", "grade": "Grade", "starting_range": "Starting Range", "ending_range": "Ending Range", "exam_timetable_does_not_exists": "Exam Timetable Does not Exists", "exam_marks": "<PERSON><PERSON>", "exam_published": "Exam have been published", "update_profile": "Update Profile", "default": "<PERSON><PERSON><PERSON>", "default_session_year_cannot_delete": "Default session year Cannot be deleted", "percentage": "Percentage", "update": "Update", "add_email_configuration": "Add Email Configuration", "email_configuration_verification": "Email Configuration Verification", "email_sent_successfully": "<PERSON><PERSON> Successfully", "invalid_email": "<PERSON><PERSON><PERSON>", "exam_not_completed_yet": "Exam not completed yet", "marks_are_not_submitted": "Marks are not submitted", "download_dummy_file": "Download Dummy File", "first_download_dummy_file_and_convert_to_csv_file_then_upload_it": "First download dummy file and convert to .csv file then upload it.", "grades_data_does_not_exists": "Grades data does not exists", "subject_already_exists": "Subject already exists", "click_here_to_remove_class_teacher": "Click here to remove class teacher", "fees": "Fees", "total": "Total", "enter": "Enter", "amount": "Amount", "paid": "Paid", "classes": "Classes", "mode": "Mode", "transaction_id": "Transaction ID", "cheque_no": "Cheque No.", "pay": "Pay", "cash": "Cash", "cheque": "Cheque", "choiceable": "Choiceable", "base": "Base", "payment_gateways": "Payment Gateways", "razorpay": "Razorpay", "enable": "Enable", "disable": "Disable", "configration": "Configuration", "currency_code": "Currency Code", "secret_key": "Secret Key", "api_key": "API Key", "stripe": "Stripe", "stripe_publishable_key": "Stripe Publishable Key", "stripe_webhook_secret": "Stripe Webhook Secret", "stripe_secret_key": "Stripe Secret Key", "change_every_time_when_session_year_is_changed": "Change every time when session year is changed", "due_charges": "Due Charges", "other_fees": "Other Fees", "currency_symbol": "Currency Symbol", "eg_currency_symbol_₹": "eg :- ₹", "eg_currency_code_inr": "eg :- inr", "cannot_delete_beacuse_data_is_associated_with_other_data": "cannot delete beacuse data is associated with other data", "delete_warning": "You will not be able to revert this!", "no.": "No.", "table": "Table", "grid": "Grid", "transactions": "Transactions", "logs": "Logs", "online": "Online", "payment_gateway": "Payment Gateway", "payment_status": "Payment Status", "order_id": "Order ID", "payment_intent_id": "Payment Intent ID", "payment_id": "Payment ID", "payment_signature": "Payment Signature", "success": "Success", "failed": "Failed", "pending": "Pending", "transaction_payment_id": "Transaction Payment ID", "fee_receipt": "Fee Receipt", "webhook_url": "Webhook URL", "razoray_webhook_secret": "Razorpay Webhook Secret", "save": "Save", "total_teachers": "Total Teachers", "total_students": "Total Students", "total_parents": "Total Parents", "sort_by": "Sort By", "please_fill_all_roll_numbers_data": "Please fill all the roll numbers data", "roll_number_already_exists_of_number": "Roll number already exists of number .", "in_minutes": "In minutes", "duration": "Duration", "exam_key": "<PERSON><PERSON>", "add_online_exam_questions": "Add Online Exam Questions", "question_type": "Question Type", "simple_question": "Simple question", "equation_based": "Equation Based", "add_questions": "Add Questions", "question": "Question", "add_option": "Add Option", "option": "Option", "questions": "Questions", "answer": "Answer", "question_is_required": "Question is required", "all_options_are_required": "All options are required", "add": "Add", "add_new_question": "Add new question", "marks_are_required": "Marks are required", "invalid_exam_key": "Invalid Exam Key", "student_already_attempted_exam": "Student already attempted exam", "invalid_online_exam_id": "Invalid online exam id", "invalid_question_id": "Invalid question id", "invalid_option_id": "Invalid option id", "exam_not_started_yet": "Exam not started yet", "online_payment_mode": "Online payment mode", "old_roll_no": "Old roll number", "new_roll_no": "New roll number", "yes_change_it": "Yes, change it", "grant_permission_to_manage_students_parents": "Grant permission to manage students and parents", "note_for_permission_of_student_manage": "Note :- By giving the permission of manage student and parent to teacher, teacher can manage student's and parent's data.", "yes_unckeck": "Yes! Uncheck", "marks": "Marks", "delete": "Delete", "clear": "Clear", "generate_pdf": "Generate PDF", "language": "Language", "code": "Code", "exams": "<PERSON><PERSON>", "boys": "Boys", "girls": "Girls", "are_you_sure_you_want_to_reset_password": "Are you sure you want to reset password ?", "confirm_reset_password": "Confirm Reset Password", "online_exam_based_on": "Online exam based on", "class_and_class_section_exam_info": "Class - Online Exam will Appear to all section of Class && Class Section - Online Exam will appear to specific class section", "class_required_when_online_exam_is_class_based": "Class is required when online exam is based on class", "class_section_required_when_online_exam_is_class_section_based": "Class section is required when online exam is based on class section", "subject_required": "Subject required", "title_required": "Title required", "exam_key_required": "Exam key required", "duration_required": "Duration required", "start_date_required": "Start date required", "end_date_required": "End date required", "exam_key_already_taken": "<PERSON>am key already taken", "duration_should_be_greater_than_or_equal_to_1": "Duration should be greater than or equal to 1", "end_date_should_be_date_after_start_date": "End date should be date after start date", "passing_marks_should_less_than_or_equal_to_total_marks": "Passing marks should be less than or equal to total marks", "end_time_should_be_greater_than_start_time": "End time should be greater than Start time", "error_mail_sending": "Error in Mail Sending", "include": "Include", "installment": "Installment", "in_percentage_%": "In Percentage (%)", "name_is_required_at_row": "Name is required at row :- ", "due_date_should_be_date_at_row": "Due date should be date at row :-", "due_date_should_be_after_or_equal_session_year_start_date_at_row": "Due date should be after or equal session year's start date at row :-", "due_date_should_be_before_or_equal_session_year_end_date_at_row": "Due date should be before or equal session year's end date at row :-", "due_charges_required_at_row": "Due charges required at row :-", "due_charges_should_be_number_at_row": "Due charges should be number at row :-", "add_new_data": "Add New Data", "partial_paid": "Partial Paid", "compulsory": "Compulsory", "optional": "Optional", "due_date_on": "Due date on", "charges": "Charges", "paid_on": "Paid on", "pay_in_installment": "Pay In Installment", "bg_color_only_hex_code": "Background color (Hex color only)", "shifts": "Shifts", "shift": "Shift", "shift_name": "Shift Name", "starting_time": "Starting Time", "ending_time": "Ending Time", "stream": "Stream", "streams": "Streams", "new": "New", "active": "Active", "inactive": "Inactive", "class_with_stream_already_exists": "Class With Stream Already Exists", "absent": "Absent", "present": "Present", "roll_number_not_available": "Roll Number Not Found", "first_download_dummy_file_and_add_attendance_then_upload_it": "First download dummy file and add attendance then upload it.", "add_attendance_on_type_column_0_for_(absent)_and_1_for_(present)": "Add attendance on type column 0 for (Absent) and 1 for (Present).", "top_rankers": "Top Rankers", "class_name": "Class Name", "roll_number_is_assigned_already": "Roll Number is Already Assigned", "login_image": "Login Background Image", "parents_details": "Parents Details", "teacher_create": "Add New Teacher", "teacher_details": "Teacher Details", "admission": "Admission", "custom_fields": "Custom Fields", "required": "Required", "form_fields": "Form Fields", "preview": "Preview", "rank": "Rank", "add_new_option": "Add New Option", "draggable_rows_notes": "Note :- you can change the rank of rows by dragging rows", "notifications": "Notifications", "custom_notification": "Custom Notifications", "all_users": "All Users", "specific_user": "Specific User", "url": "Url", "send_to": "Send To", "notification": "Notification", "user": "User", "message": "Message", "notification_sent_successfully": "Notification Sent Successfully", "session_year_id": "Session Year Id", "feedback": "<PERSON><PERSON><PERSON>", "edit_fees": "<PERSON>", "signout": "Signout", "branding_verification": "Branding Verification", "new_branding_assets_loaded_successfully": "New branding assets loaded successfully", "cover_image": "Cover Image", "application_logo": "Application Logo"}