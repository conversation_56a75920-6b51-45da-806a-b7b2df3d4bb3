<!-- partial:../../partials/_navbar.html -->
@if(Session::get('locale'))
{{app()->setLocale(Session::get('locale'))}}
@endif
<nav class="navbar default-layout-navbar col-lg-12 col-12 p-0 fixed-top d-flex flex-row">
    <div class="text-center navbar-brand-wrapper d-flex align-items-center justify-content-center">
        <a class="navbar-brand brand-logo" href="{{ URL::to('/') }}"> <img src="{{ asset('logo/madrasa-logo.png') }}" alt="logo"> </a> <a class="navbar-brand brand-logo-mini" href="{{ URL::to('/') }}"> <img src="{{ asset('logo/madrasa-favicon.png') }}" alt="logo"> </a>
    </div>
    <div class="navbar-menu-wrapper d-flex align-items-stretch">
        <button class="navbar-toggler navbar-toggler align-self-center" type="button" data-toggle="minimize">
            <span class="fa fa-bars"></span>
        </button>

        @php
        $email_config_verify_value = DB::table('settings')->select('message')->where('type','email_configration_verification')->first();
        if($email_config_verify_value){
        $message = $email_config_verify_value->message;
        }else{
        $message = 0;
        }
        @endphp
        @if($message == 0)
        @can('email-setting-create')
        <div class="mx-auto order-0">
            <div class="alert alert-fill-danger my-2" role="alert">
                <i class="fa fa-exclamation"></i> Email Configuration is not verified <a href="{{route('setting.email-config-index')}}" class="alert-link">Click here to configure email settings</a>.
            </div>
        </div>
        @endcan
        @endif
        <ul class="navbar-nav navbar-nav-right">
            <li class="nav-item dropdown">
                <a class="nav-link count-indicator dropdown-toggle" id="messageDropdown" href="#" data-toggle="dropdown" aria-expanded="false">
                    <i class="fa fa-language"></i>
                    <span class="d-none d-md-inline-block ml-2">{{ Session::get('language') ? Session::get('language')->name : 'Language' }}</span>
                </a>
                <div class="dropdown-menu dropdown-menu-right navbar-dropdown language-dropdown" aria-labelledby="messageDropdown">
                    <h6 class="dropdown-header">
                        {{ __('select_language') }}
                    </h6>
                    <div class="dropdown-divider"></div>
                    <a class="dropdown-item preview-item language-link" href="{{ url('set-language/en') }}">
                        <div class="preview-thumbnail">
                            <div class="preview-icon bg-light rounded-circle">
                                <i class="fa fa-flag text-primary"></i>
                            </div>
                        </div>
                        <div class="preview-item-content d-flex align-items-start flex-column justify-content-center">
                            <h6 class="preview-subject font-weight-normal mb-0">English</h6>
                        </div>
                    </a>
                    <div class="dropdown-divider"></div>
                    <a class="dropdown-item preview-item language-link" href="{{ url('set-language/ar') }}">
                        <div class="preview-thumbnail">
                            <div class="preview-icon bg-light rounded-circle">
                                <i class="fa fa-flag text-primary"></i>
                            </div>
                        </div>
                        <div class="preview-item-content d-flex align-items-start flex-column justify-content-center">
                            <h6 class="preview-subject font-weight-normal mb-0">العربية</h6>
                        </div>
                    </a>
                </div>
            </li>
            @auth
            <li class="nav-item nav-profile dropdown">
                <a class="nav-link dropdown-toggle" id="profileDropdown" href="#" data-toggle="dropdown" aria-expanded="true">
                    <div class="nav-profile-img">
                        <img src="{{ Auth::user()->image }}" alt="profile image" onerror="onErrorImage(event)">
                    </div>
                    <div class="nav-profile-text">
                        <p class="mb-1">Admin</p>
                    </div>
                </a>
                <div class="dropdown-menu navbar-dropdown" aria-labelledby="profileDropdown">
                    <div class="dropdown-header">
                        <div class="dropdown-profile-img">
                            <img src="{{ asset('logo/madrasa-logo.png') }}" alt="Madrasa">
                        </div>
                        <div class="dropdown-profile-info">
                            <p class="dropdown-profile-name">{{ Auth::user()->first_name }}</p>
                            <p class="dropdown-profile-role">{{ Auth::user()->hasRole('Super Admin') ? 'Super Admin' : 'Admin' }}</p>
                        </div>
                    </div>
                    <div class="dropdown-divider"></div>
                    @can('update-admin-profile')
                    <a class="dropdown-item" href="{{ route('edit-profile') }}">
                        <i class="fa fa-user mr-2"></i>{{ __('update_profile') }}</a>
                    <div class="dropdown-divider"></div>
                    @endcan
                    <a class="dropdown-item" href="{{ route('resetpassword') }}">
                        <i class="fa fa-refresh mr-2"></i>{{ __('change_password') }}</a>
                    <div class="dropdown-divider"></div>
                    <a class="dropdown-item" href="{{ route('logout') }}">
                        <i class="fa fa-sign-out mr-2"></i> {{ __('signout') }}
                    </a>
                </div>
            </li>
            @endauth
        </ul>
        <button class="navbar-toggler navbar-toggler-right d-lg-none align-self-center" type="button" data-toggle="offcanvas">
            <span class="fa fa-bars"></span>
        </button>
    </div>
</nav>