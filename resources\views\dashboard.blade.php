@extends('layouts.master')

@section('title')
    {{__('dashboard')}}
@endsection

@section('css')
<!-- Additional CSS for dashboard -->
<style>
    .dashboard-card {
        border-radius: 12px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.05);
        transition: all 0.3s ease;
        border: none;
        overflow: hidden;
        margin-bottom: 25px;
    }
    
    .dashboard-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 6px 16px rgba(0,0,0,0.1);
    }
    
    .stat-card {
        border-radius: 12px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.05);
        transition: all 0.3s ease;
        border: none;
        overflow: hidden;
        margin-bottom: 25px;
    }
    
    .stat-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 6px 16px rgba(0,0,0,0.1);
    }
    
    .stat-icon {
        width: 60px;
        height: 60px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 12px;
        font-size: 24px;
    }
    
    .chart-container {
        position: relative;
        height: 300px;
        width: 100%;
    }
    
    .recent-activity {
        max-height: 400px;
        overflow-y: auto;
    }
    
    .activity-item {
        padding: 12px 0;
        border-bottom: 1px solid #eee;
    }
    
    .activity-item:last-child {
        border-bottom: none;
    }
    
    .activity-icon {
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 10px;
        font-size: 16px;
    }
    
    .bg-theme-light {
        background: linear-gradient(45deg, rgba(23, 119, 117, 0.15), rgba(40, 210, 191, 0.15));
    }
    
    .text-theme {
        color: #177775 !important;
    }
    
    .welcome-banner {
        background: linear-gradient(45deg, #177775, #28D2BF);
        border-radius: 12px;
        color: white;
        padding: 25px;
        margin-bottom: 25px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    }
    
    .welcome-banner h2 {
        font-weight: 600;
        margin-bottom: 10px;
    }
    
    .welcome-banner p {
        opacity: 0.9;
        font-size: 16px;
    }
</style>
@endsection

@section('content')

<div class="content-wrapper">
    <div class="page-header">
        <h3 class="page-title">
            <span class="page-title-icon bg-theme text-white mr-2">
                <i class="fa fa-home"></i>
            </span> {{__('dashboard')}}
        </h3>
    </div>
    
    <!-- Welcome Banner -->
    <div class="welcome-banner">
        <h2>Welcome Back, Admin!</h2>
        <p>Here's what's happening with your Madrasa today.</p>
    </div>
    
    <!-- Statistics Row -->
    <div class="row">
        <div class="col-md-3">
            <div class="stat-card bg-gradient-danger text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="font-weight-normal mb-1">Total Teachers</h4>
                            <h2 class="mb-0">2</h2>
                        </div>
                        <div class="stat-icon bg-white text-danger">
                            <i class="mdi mdi-account mdi-24px"></i>
                        </div>
                    </div>
                    <div class="mt-3">
                        <small class="text-white"><i class="mdi mdi-arrow-up-bold"></i> 12% from last month</small>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="stat-card bg-gradient-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="font-weight-normal mb-1">Total Students</h4>
                            <h2 class="mb-0">3</h2>
                        </div>
                        <div class="stat-icon bg-white text-info">
                            <i class="mdi mdi-account-multiple mdi-24px"></i>
                        </div>
                    </div>
                    <div class="mt-3">
                        <small class="text-white"><i class="mdi mdi-arrow-down-bold"></i> 5% from last month</small>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="stat-card bg-gradient-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="font-weight-normal mb-1">Total Parents</h4>
                            <h2 class="mb-0">4</h2>
                        </div>
                        <div class="stat-icon bg-white text-success">
                            <i class="mdi mdi-account-group mdi-24px"></i>
                        </div>
                    </div>
                    <div class="mt-3">
                        <small class="text-white"><i class="mdi mdi-arrow-up-bold"></i> 8% from last month</small>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="stat-card bg-gradient-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="font-weight-normal mb-1">Classes</h4>
                            <h2 class="mb-0">5</h2>
                        </div>
                        <div class="stat-icon bg-white text-warning">
                            <i class="mdi mdi-school mdi-24px"></i>
                        </div>
                    </div>
                    <div class="mt-3">
                        <small class="text-white"><i class="mdi mdi-arrow-up-bold"></i> 3% from last month</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Charts Row -->
    <div class="row">
        <div class="col-md-8 grid-margin stretch-card">
            <div class="card dashboard-card">
                <div class="card-header bg-white">
                    <h4 class="card-title">Student Enrollment Trend</h4>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="enrollmentChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-4 grid-margin stretch-card">
            <div class="card dashboard-card">
                <div class="card-header bg-white">
                    <h4 class="card-title">User Distribution</h4>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="userDistributionChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Recent Activity and Quick Stats Row -->
    <div class="row">
        <div class="col-md-6 grid-margin stretch-card">
            <div class="card dashboard-card">
                <div class="card-header bg-white">
                    <h4 class="card-title">Recent Activity</h4>
                </div>
                <div class="card-body">
                    <div class="recent-activity">
                        <div class="activity-item d-flex">
                            <div class="activity-icon bg-theme-light text-theme mr-3">
                                <i class="mdi mdi-account-plus"></i>
                            </div>
                            <div>
                                <p class="mb-0 font-weight-medium">New teacher registered</p>
                                <p class="mb-0 text-muted">Ahmed Hassan - 2 hours ago</p>
                            </div>
                        </div>
                        <div class="activity-item d-flex">
                            <div class="activity-icon bg-theme-light text-theme mr-3">
                                <i class="mdi mdi-book-plus"></i>
                            </div>
                            <div>
                                <p class="mb-0 font-weight-medium">New course added</p>
                                <p class="mb-0 text-muted">Islamic Studies - 5 hours ago</p>
                            </div>
                        </div>
                        <div class="activity-item d-flex">
                            <div class="activity-icon bg-theme-light text-theme mr-3">
                                <i class="mdi mdi-calendar-check"></i>
                            </div>
                            <div>
                                <p class="mb-0 font-weight-medium">Exam scheduled</p>
                                <p class="mb-0 text-muted">Quran Reading - Yesterday</p>
                            </div>
                        </div>
                        <div class="activity-item d-flex">
                            <div class="activity-icon bg-theme-light text-theme mr-3">
                                <i class="mdi mdi-cash-multiple"></i>
                            </div>
                            <div>
                                <p class="mb-0 font-weight-medium">Fee payment received</p>
                                <p class="mb-0 text-muted">Ali Mahmoud - 2 days ago</p>
                            </div>
                        </div>
                        <div class="activity-item d-flex">
                            <div class="activity-icon bg-theme-light text-theme mr-3">
                                <i class="mdi mdi-account-edit"></i>
                            </div>
                            <div>
                                <p class="mb-0 font-weight-medium">Student profile updated</p>
                                <p class="mb-0 text-muted">Fatima Ali - 3 days ago</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-6 grid-margin stretch-card">
            <div class="card dashboard-card">
                <div class="card-header bg-white">
                    <h4 class="card-title">Quick Stats</h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6 mb-4">
                            <div class="d-flex align-items-center">
                                <div class="stat-icon bg-theme-light text-theme mr-3">
                                    <i class="mdi mdi-book-open-page-variant"></i>
                                </div>
                                <div>
                                    <p class="mb-0 font-weight-medium">Courses</p>
                                    <h3 class="mb-0 text-theme">12</h3>
                                </div>
                            </div>
                        </div>
                        <div class="col-6 mb-4">
                            <div class="d-flex align-items-center">
                                <div class="stat-icon bg-theme-light text-theme mr-3">
                                    <i class="mdi mdi-calendar-clock"></i>
                                </div>
                                <div>
                                    <p class="mb-0 font-weight-medium">Events</p>
                                    <h3 class="mb-0 text-theme">8</h3>
                                </div>
                            </div>
                        </div>
                        <div class="col-6 mb-4">
                            <div class="d-flex align-items-center">
                                <div class="stat-icon bg-theme-light text-theme mr-3">
                                    <i class="mdi mdi-cash"></i>
                                </div>
                                <div>
                                    <p class="mb-0 font-weight-medium">Payments</p>
                                    <h3 class="mb-0 text-theme">24</h3>
                                </div>
                            </div>
                        </div>
                        <div class="col-6 mb-4">
                            <div class="d-flex align-items-center">
                                <div class="stat-icon bg-theme-light text-theme mr-3">
                                    <i class="mdi mdi-file-document"></i>
                                </div>
                                <div>
                                    <p class="mb-0 font-weight-medium">Reports</p>
                                    <h3 class="mb-0 text-theme">15</h3>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mt-4">
                        <h4 class="font-weight-medium">System Status</h4>
                        <div class="d-flex justify-content-between mt-3">
                            <div>
                                <p class="mb-1">Database</p>
                                <div class="progress progress-sm">
                                    <div class="progress-bar bg-success" role="progressbar" style="width: 95%" aria-valuenow="95" aria-valuemin="0" aria-valuemax="100"></div>
                                </div>
                            </div>
                            <p class="mb-0 font-weight-medium">95%</p>
                        </div>
                        <div class="d-flex justify-content-between mt-3">
                            <div>
                                <p class="mb-1">Storage</p>
                                <div class="progress progress-sm">
                                    <div class="progress-bar bg-info" role="progressbar" style="width: 70%" aria-valuenow="70" aria-valuemin="0" aria-valuemax="100"></div>
                                </div>
                            </div>
                            <p class="mb-0 font-weight-medium">70%</p>
                        </div>
                        <div class="d-flex justify-content-between mt-3">
                            <div>
                                <p class="mb-1">Bandwidth</p>
                                <div class="progress progress-sm">
                                    <div class="progress-bar bg-warning" role="progressbar" style="width: 45%" aria-valuenow="45" aria-valuemin="0" aria-valuemax="100"></div>
                                </div>
                            </div>
                            <p class="mb-0 font-weight-medium">45%</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@endsection

@section('js')
<script src="{{ asset('/assets/js/Chart.min.js') }}"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Enrollment Chart
        var enrollmentCtx = document.getElementById('enrollmentChart').getContext('2d');
        var enrollmentChart = new Chart(enrollmentCtx, {
            type: 'line',
            data: {
                labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep'],
                datasets: [{
                    label: 'Student Enrollment',
                    data: [12, 19, 15, 17, 22, 25, 28, 30, 35],
                    borderColor: '#177775',
                    backgroundColor: 'rgba(23, 119, 117, 0.1)',
                    borderWidth: 3,
                    pointBackgroundColor: '#28D2BF',
                    pointRadius: 5,
                    pointHoverRadius: 7,
                    fill: true,
                    tension: 0.3
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0, 0, 0, 0.05)'
                        }
                    },
                    x: {
                        grid: {
                            color: 'rgba(0, 0, 0, 0.05)'
                        }
                    }
                }
            }
        });
        
        // User Distribution Chart
        var userDistributionCtx = document.getElementById('userDistributionChart').getContext('2d');
        var userDistributionChart = new Chart(userDistributionCtx, {
            type: 'doughnut',
            data: {
                labels: ['Teachers', 'Students', 'Parents'],
                datasets: [{
                    data: [2, 3, 4],
                    backgroundColor: [
                        '#dc3545',
                        '#17a2b8',
                        '#28a745'
                    ],
                    borderWidth: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                },
                cutout: '70%'
            }
        });
    });
</script>
@endsection