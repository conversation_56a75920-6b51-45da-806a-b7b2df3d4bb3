﻿/*
Copyright (c) 2003-2022, CKSource Holding sp. z o.o. All rights reserved.
For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-oss-license/
*/
(function(){if(window.CKEDITOR&&window.CKEDITOR.dom)return;window.CKEDITOR||(window.CKEDITOR=function(){var a=/(^|.*[\\\/])ckeditor\.js(?:\?.*|;.*)?$/i,d={timestamp:"MAUD",version:"4.20.1",revision:"b520aa2b40",rnd:Math.floor(900*Math.random())+100,_:{pending:[],basePathSrcPattern:a},status:"unloaded",basePath:function(){var b=window.CKEDITOR_BASEPATH||"";if(!b)for(var c=document.getElementsByTagName("script"),d=0;d<c.length;d++){var h=c[d].src.match(a);if(h){b=h[1];break}}-1==b.indexOf(":/")&&"//"!=b.slice(0,2)&&(b=0===b.indexOf("/")?location.href.match(/^.*?:\/\/[^\/]*/)[0]+
b:location.href.match(/^[^\?]*\/(?:)/)[0]+b);if(!b)throw'The CKEditor installation path could not be automatically detected. Please set the global variable "CKEDITOR_BASEPATH" before creating editor instances.';return b}(),getUrl:function(a){-1==a.indexOf(":/")&&0!==a.indexOf("/")&&(a=this.basePath+a);return a=this.appendTimestamp(a)},appendTimestamp:function(a){if(!this.timestamp||"/"===a.charAt(a.length-1)||/[&?]t=/.test(a))return a;var b=0<=a.indexOf("?")?"\x26":"?";return a+b+"t\x3d"+this.timestamp},
domReady:function(){function a(){try{document.addEventListener?(document.removeEventListener("DOMContentLoaded",a,!1),window.removeEventListener("load",a,!1),b()):document.attachEvent&&"complete"===document.readyState&&(document.detachEvent("onreadystatechange",a),window.detachEvent("onload",a),b())}catch(c){}}function b(){for(var a;a=c.shift();)a()}var c=[];return function(b){function e(){try{document.documentElement.doScroll("left")}catch(b){setTimeout(e,1);return}a()}c.push(b);"complete"===document.readyState&&
setTimeout(a,1);if(1==c.length)if(document.addEventListener)document.addEventListener("DOMContentLoaded",a,!1),window.addEventListener("load",a,!1);else if(document.attachEvent){document.attachEvent("onreadystatechange",a);window.attachEvent("onload",a);b=!1;try{b=!window.frameElement}catch(d){}document.documentElement.doScroll&&b&&e()}}}()},b=window.CKEDITOR_GETURL;if(b){var c=d.getUrl;d.getUrl=function(a){return b.call(d,a)||c.call(d,a)}}return d}());
(function(){var a={};CKEDITOR.event||(CKEDITOR.event=function(){},CKEDITOR.event.implementOn=function(a){var b=CKEDITOR.event.prototype,c;for(c in b)null==a[c]&&(a[c]=b[c])},CKEDITOR.event.prototype=function(){function d(a){var e=b(this);return e[a]||(e[a]=new c(a))}var b=function(a){a=a.getPrivate&&a.getPrivate()||a._||(a._={});return a.events||(a.events={})},c=function(a){this.name=a;this.listeners=[]};c.prototype={getListenerIndex:function(a){for(var b=0,c=this.listeners;b<c.length;b++)if(c[b].fn==
a)return b;return-1}};return{define:function(a,b){var c=d.call(this,a);CKEDITOR.tools.extend(c,b,!0)},on:function(b,c,k,h,p){function m(x,y,A,p){x={name:b,sender:this,editor:x,data:y,listenerData:h,stop:A,cancel:p,removeListener:g};return!1===c.call(k,x)?a:x.data}function g(){x.removeListener(b,c)}var x=this,y=d.call(this,b);if(0>y.getListenerIndex(c)){y=y.listeners;k||(k=this);isNaN(p)&&(p=10);m.fn=c;m.priority=p;for(var A=y.length-1;0<=A;A--)if(y[A].priority<=p)return y.splice(A+1,0,m),{removeListener:g};
y.unshift(m)}return{removeListener:g}},once:function(){var a=Array.prototype.slice.call(arguments),b=a[1];a[1]=function(a){a.removeListener();return b.apply(this,arguments)};return this.on.apply(this,a)},capture:function(){CKEDITOR.event.useCapture=1;var a=this.on.apply(this,arguments);CKEDITOR.event.useCapture=0;return a},fire:function(){var c=0,e=function(){c=1},d=0,h=function(){d=1};return function(p,m,g){var x=b(this)[p];p=c;var y=d;c=d=0;if(x){var A=x.listeners;if(A.length)for(var A=A.slice(0),
F,v=0;v<A.length;v++){if(x.errorProof)try{F=A[v].call(this,g,m,e,h)}catch(l){}else F=A[v].call(this,g,m,e,h);F===a?d=1:"undefined"!=typeof F&&(m=F);if(c||d)break}}m=d?!1:"undefined"==typeof m?!0:m;c=p;d=y;return m}}(),fireOnce:function(a,c,d){c=this.fire(a,c,d);delete b(this)[a];return c},removeListener:function(a,c){var d=b(this)[a];if(d){var h=d.getListenerIndex(c);0<=h&&d.listeners.splice(h,1)}},removeAllListeners:function(){var a=b(this),c;for(c in a)delete a[c]},hasListeners:function(a){return(a=
b(this)[a])&&0<a.listeners.length}}}())})();CKEDITOR.editor||(CKEDITOR.editor=function(){CKEDITOR._.pending.push([this,arguments]);CKEDITOR.event.call(this)},CKEDITOR.editor.prototype.fire=function(a,d){a in{instanceReady:1,loaded:1}&&(this[a]=!0);return CKEDITOR.event.prototype.fire.call(this,a,d,this)},CKEDITOR.editor.prototype.fireOnce=function(a,d){a in{instanceReady:1,loaded:1}&&(this[a]=!0);return CKEDITOR.event.prototype.fireOnce.call(this,a,d,this)},CKEDITOR.event.implementOn(CKEDITOR.editor.prototype));
CKEDITOR.env||(CKEDITOR.env=function(){var a=navigator.userAgent.toLowerCase(),d=a.match(/edge[ \/](\d+.?\d*)/),b=-1<a.indexOf("trident/"),b=!(!d&&!b),b={ie:b,edge:!!d,webkit:!b&&-1<a.indexOf(" applewebkit/"),air:-1<a.indexOf(" adobeair/"),mac:-1<a.indexOf("macintosh"),quirks:"BackCompat"==document.compatMode&&(!document.documentMode||10>document.documentMode),mobile:-1<a.indexOf("mobile"),iOS:/(ipad|iphone|ipod)/.test(a),isCustomDomain:function(){if(!this.ie)return!1;var a=document.domain,b=window.location.hostname;
return a!=b&&a!="["+b+"]"},secure:"https:"==location.protocol};b.gecko="Gecko"==navigator.product&&!b.webkit&&!b.ie;b.webkit&&(-1<a.indexOf("chrome")?b.chrome=!0:b.safari=!0);var c=0;b.ie&&(c=d?parseFloat(d[1]):b.quirks||!document.documentMode?parseFloat(a.match(/msie (\d+)/)[1]):document.documentMode,b.ie9Compat=9==c,b.ie8Compat=8==c,b.ie7Compat=7==c,b.ie6Compat=7>c||b.quirks);b.gecko&&(d=a.match(/rv:([\d\.]+)/))&&(d=d[1].split("."),c=1E4*d[0]+100*(d[1]||0)+1*(d[2]||0));b.air&&(c=parseFloat(a.match(/ adobeair\/(\d+)/)[1]));
b.webkit&&(c=parseFloat(a.match(/ applewebkit\/(\d+)/)[1]));b.version=c;b.isCompatible=!(b.ie&&7>c)&&!(b.gecko&&4E4>c)&&!(b.webkit&&534>c);b.hidpi=2<=window.devicePixelRatio;b.needsBrFiller=b.gecko||b.webkit||b.ie&&10<c;b.needsNbspFiller=b.ie&&11>c;b.cssClass="cke_browser_"+(b.ie?"ie":b.gecko?"gecko":b.webkit?"webkit":"unknown");b.quirks&&(b.cssClass+=" cke_browser_quirks");b.ie&&(b.cssClass+=" cke_browser_ie"+(b.quirks?"6 cke_browser_iequirks":b.version));b.air&&(b.cssClass+=" cke_browser_air");
b.iOS&&(b.cssClass+=" cke_browser_ios");b.hidpi&&(b.cssClass+=" cke_hidpi");return b}());
"unloaded"==CKEDITOR.status&&function(){CKEDITOR.event.implementOn(CKEDITOR);CKEDITOR.loadFullCore=function(){if("basic_ready"!=CKEDITOR.status)CKEDITOR.loadFullCore._load=1;else{delete CKEDITOR.loadFullCore;var a=document.createElement("script");a.type="text/javascript";a.src=CKEDITOR.basePath+"ckeditor.js";document.getElementsByTagName("head")[0].appendChild(a)}};CKEDITOR.loadFullCoreTimeout=0;CKEDITOR.add=function(a){(this._.pending||(this._.pending=[])).push(a)};(function(){CKEDITOR.domReady(function(){var a=
CKEDITOR.loadFullCore,d=CKEDITOR.loadFullCoreTimeout;a&&(CKEDITOR.status="basic_ready",a&&a._load?a():d&&setTimeout(function(){CKEDITOR.loadFullCore&&CKEDITOR.loadFullCore()},1E3*d))})})();CKEDITOR.status="basic_loaded"}();"use strict";CKEDITOR.VERBOSITY_WARN=1;CKEDITOR.VERBOSITY_ERROR=2;CKEDITOR.verbosity=CKEDITOR.VERBOSITY_WARN|CKEDITOR.VERBOSITY_ERROR;CKEDITOR.warn=function(a,d){CKEDITOR.verbosity&CKEDITOR.VERBOSITY_WARN&&CKEDITOR.fire("log",{type:"warn",errorCode:a,additionalData:d})};
CKEDITOR.error=function(a,d){CKEDITOR.verbosity&CKEDITOR.VERBOSITY_ERROR&&CKEDITOR.fire("log",{type:"error",errorCode:a,additionalData:d})};
CKEDITOR.on("log",function(a){if(window.console&&window.console.log){var d=console[a.data.type]?a.data.type:"log",b=a.data.errorCode;if(a=a.data.additionalData)console[d]("[CKEDITOR] Error code: "+b+".",a);else console[d]("[CKEDITOR] Error code: "+b+".");console[d]("[CKEDITOR] For more information about this error go to https://ckeditor.com/docs/ckeditor4/latest/guide/dev_errors.html#"+b)}},null,null,999);CKEDITOR.dom={};
(function(){function a(a,y,b){this._minInterval=a;this._context=b;this._lastOutput=this._scheduledTimer=0;this._output=CKEDITOR.tools.bind(y,b||{});var c=this;this.input=function(){function a(){c._lastOutput=(new Date).getTime();c._scheduledTimer=0;c._call()}if(!c._scheduledTimer||!1!==c._reschedule()){var x=(new Date).getTime()-c._lastOutput;x<c._minInterval?c._scheduledTimer=setTimeout(a,c._minInterval-x):a()}}}function d(x,y,b){a.call(this,x,y,b);this._args=[];var c=this;this.input=CKEDITOR.tools.override(this.input,
function(a){return function(){c._args=Array.prototype.slice.call(arguments);a.call(this)}})}var b=[],c=CKEDITOR.env.gecko?"-moz-":CKEDITOR.env.webkit?"-webkit-":CKEDITOR.env.ie?"-ms-":"",f=/&/g,e=/>/g,k=/</g,h=/"/g,p=/&(lt|gt|amp|quot|nbsp|shy|#\d{1,5});/g,m={lt:"\x3c",gt:"\x3e",amp:"\x26",quot:'"',nbsp:" ",shy:"­"},g=function(a,y){return"#"==y[0]?String.fromCharCode(parseInt(y.slice(1),10)):m[y]};CKEDITOR.on("reset",function(){b=[]});CKEDITOR.tools={arrayCompare:function(a,y){if(!a&&!y)return!0;
if(!a||!y||a.length!=y.length)return!1;for(var b=0;b<a.length;b++)if(a[b]!=y[b])return!1;return!0},getIndex:function(a,b){for(var c=0;c<a.length;++c)if(b(a[c]))return c;return-1},clone:function(a){var b;if(a&&a instanceof Array){b=[];for(var c=0;c<a.length;c++)b[c]=CKEDITOR.tools.clone(a[c]);return b}if(null===a||"object"!=typeof a||a instanceof String||a instanceof Number||a instanceof Boolean||a instanceof Date||a instanceof RegExp||a.nodeType||a.window===a)return a;b=new a.constructor;for(c in a)b[c]=
CKEDITOR.tools.clone(a[c]);return b},capitalize:function(a,b){return a.charAt(0).toUpperCase()+(b?a.slice(1):a.slice(1).toLowerCase())},extend:function(a){var b=arguments.length,c,g;"boolean"==typeof(c=arguments[b-1])?b--:"boolean"==typeof(c=arguments[b-2])&&(g=arguments[b-1],b-=2);for(var h=1;h<b;h++){var l=arguments[h]||{};CKEDITOR.tools.array.forEach(CKEDITOR.tools.object.keys(l),function(b){if(!0===c||null==a[b])if(!g||b in g)a[b]=l[b]})}return a},prototypedCopy:function(a){var b=function(){};
b.prototype=a;return new b},copy:function(a){var b={},c;for(c in a)b[c]=a[c];return b},isArray:function(a){return"[object Array]"==Object.prototype.toString.call(a)},isEmpty:function(a){for(var b in a)if(a.hasOwnProperty(b))return!1;return!0},cssVendorPrefix:function(a,b,g){if(g)return c+a+":"+b+";"+a+":"+b;g={};g[a]=b;g[c+a]=b;return g},cssStyleToDomStyle:function(){var a=document.createElement("div").style,b="undefined"!=typeof a.cssFloat?"cssFloat":"undefined"!=typeof a.styleFloat?"styleFloat":
"float";return function(a){return"float"==a?b:a.replace(/-./g,function(a){return a.substr(1).toUpperCase()})}}(),buildStyleHtml:function(a){a=[].concat(a);for(var b,c=[],g=0;g<a.length;g++)if(b=a[g])/@import|[{}]/.test(b)?c.push("\x3cstyle\x3e"+b+"\x3c/style\x3e"):(b=CKEDITOR.appendTimestamp(b),c.push('\x3clink type\x3d"text/css" rel\x3dstylesheet href\x3d"'+b+'"\x3e'));return c.join("")},htmlEncode:function(a){return void 0===a||null===a?"":String(a).replace(f,"\x26amp;").replace(e,"\x26gt;").replace(k,
"\x26lt;")},htmlDecode:function(a){return a.replace(p,g)},htmlEncodeAttr:function(a){return CKEDITOR.tools.htmlEncode(a).replace(h,"\x26quot;")},htmlDecodeAttr:function(a){return CKEDITOR.tools.htmlDecode(a)},transformPlainTextToHtml:function(a,b){var c=b==CKEDITOR.ENTER_BR,g=this.htmlEncode(a.replace(/\r\n/g,"\n")),g=g.replace(/\t/g,"\x26nbsp;\x26nbsp; \x26nbsp;"),h=b==CKEDITOR.ENTER_P?"p":"div";if(!c){var l=/\n{2}/g;if(l.test(g))var e="\x3c"+h+"\x3e",d="\x3c/"+h+"\x3e",g=e+g.replace(l,function(){return d+
e})+d}g=g.replace(/\n/g,"\x3cbr\x3e");c||(g=g.replace(new RegExp("\x3cbr\x3e(?\x3d\x3c/"+h+"\x3e)"),function(a){return CKEDITOR.tools.repeat(a,2)}));g=g.replace(/^ | $/g,"\x26nbsp;");return g=g.replace(/(>|\s) /g,function(a,b){return b+"\x26nbsp;"}).replace(/ (?=<)/g,"\x26nbsp;")},getNextNumber:function(){var a=0;return function(){return++a}}(),getNextId:function(){return"cke_"+this.getNextNumber()},getUniqueId:function(){for(var a="e",b=0;8>b;b++)a+=Math.floor(65536*(1+Math.random())).toString(16).substring(1);
return a},override:function(a,b){var c=b(a);c.prototype=a.prototype;return c},setTimeout:function(a,b,c,g,h){h||(h=window);c||(c=h);return h.setTimeout(function(){g?a.apply(c,[].concat(g)):a.apply(c)},b||0)},debounce:function(a,b){var c;return function(){var g=this,h=arguments;clearTimeout(c);c=setTimeout(function(){c=null;a.apply(g,h)},b)}},throttle:function(a,b,c){return new this.buffers.throttle(a,b,c)},trim:function(){var a=/(?:^[ \t\n\r]+)|(?:[ \t\n\r]+$)/g;return function(b){return b.replace(a,
"")}}(),ltrim:function(){var a=/^[ \t\n\r]+/g;return function(b){return b.replace(a,"")}}(),rtrim:function(){var a=/[ \t\n\r]+$/g;return function(b){return b.replace(a,"")}}(),indexOf:function(a,b){if("function"==typeof b)for(var c=0,g=a.length;c<g;c++){if(b(a[c]))return c}else{if(a.indexOf)return a.indexOf(b);c=0;for(g=a.length;c<g;c++)if(a[c]===b)return c}return-1},search:function(a,b){var c=CKEDITOR.tools.indexOf(a,b);return 0<=c?a[c]:null},bind:function(a,b){var c=Array.prototype.slice.call(arguments,
2);return function(){return a.apply(b,c.concat(Array.prototype.slice.call(arguments)))}},createClass:function(a){var b=a.$,c=a.base,g=a.privates||a._,h=a.proto;a=a.statics;!b&&(b=function(){c&&this.base.apply(this,arguments)});if(g)var l=b,b=function(){var a=this._||(this._={}),b;for(b in g){var c=g[b];a[b]="function"==typeof c?CKEDITOR.tools.bind(c,this):c}l.apply(this,arguments)};c&&(b.prototype=this.prototypedCopy(c.prototype),b.prototype.constructor=b,b.base=c,b.baseProto=c.prototype,b.prototype.base=
function w(){this.base=c.prototype.base;c.apply(this,arguments);this.base=w});h&&this.extend(b.prototype,h,!0);a&&this.extend(b,a,!0);return b},addFunction:function(a,c){return b.push(function(){return a.apply(c||this,arguments)})-1},removeFunction:function(a){b[a]=null},callFunction:function(a){var c=b[a];return c&&c.apply(window,Array.prototype.slice.call(arguments,1))},cssLength:function(){var a=/^-?\d+\.?\d*px$/,b;return function(c){b=CKEDITOR.tools.trim(c+"")+"px";return a.test(b)?b:c||""}}(),
convertToPx:function(){var a,b;return function(c){if(!a||a.isDetached())a=CKEDITOR.dom.element.createFromHtml('\x3cdiv style\x3d"position:absolute;left:-9999px;top:-9999px;margin:0px;padding:0px;border:0px;"\x3e\x3c/div\x3e',CKEDITOR.document),CKEDITOR.document.getBody().append(a);if(!/%$/.test(c)){var g=0>parseFloat(c);g&&(c=c.replace("-",""));a.setStyle("width",c);b=a.getClientRect();c=Math.round(b.width);return g?-c:c}return c}}(),repeat:function(a,b){return Array(b+1).join(a)},tryThese:function(){for(var a,
b=0,c=arguments.length;b<c;b++){var g=arguments[b];try{a=g();break}catch(h){}}return a},genKey:function(){return Array.prototype.slice.call(arguments).join("-")},defer:function(a){return function(){var b=arguments,c=this;window.setTimeout(function(){a.apply(c,b)},0)}},normalizeCssText:function(a,b){var c=[],g,h=CKEDITOR.tools.parseCssText(a,!0,b);for(g in h)c.push(g+":"+h[g]);c.sort();return c.length?c.join(";")+";":""},convertRgbToHex:function(a){return a.replace(/(?:rgb\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*\))/gi,
function(a,b,c,g){a=[b,c,g];for(b=0;3>b;b++)a[b]=("0"+parseInt(a[b],10).toString(16)).slice(-2);return"#"+a.join("")})},normalizeHex:function(a){return a.replace(/#(([0-9a-f]{3}){1,2})($|;|\s+)/gi,function(a,b,c,g){a=b.toLowerCase();3==a.length&&(a=a.split(""),a=[a[0],a[0],a[1],a[1],a[2],a[2]].join(""));return"#"+a+g})},_isValidColorFormat:function(a){if(!a)return!1;a=a.replace(/\s+/g,"");return/^[a-z0-9()#%,./]+$/i.test(a)},parseCssText:function(a,b,c){var g={};c&&(a=(new CKEDITOR.dom.element("span")).setAttribute("style",
a).getAttribute("style")||"");a&&(a=CKEDITOR.tools.normalizeHex(CKEDITOR.tools.convertRgbToHex(a)));if(!a||";"==a)return g;a.replace(/&quot;/g,'"').replace(/\s*([^:;\s]+)\s*:\s*([^;]+)\s*(?=;|$)/g,function(a,c,x){b&&(c=c.toLowerCase(),"font-family"==c&&(x=x.replace(/\s*,\s*/g,",")),x=CKEDITOR.tools.trim(x));g[c]=x});return g},writeCssText:function(a,b){var c,g=[];for(c in a)g.push(c+":"+a[c]);b&&g.sort();return g.join("; ")},objectCompare:function(a,b,c){var g;if(!a&&!b)return!0;if(!a||!b)return!1;
for(g in a)if(a[g]!=b[g])return!1;if(!c)for(g in b)if(a[g]!=b[g])return!1;return!0},objectKeys:function(a){return CKEDITOR.tools.object.keys(a)},convertArrayToObject:function(a,b){var c={};1==arguments.length&&(b=!0);for(var g=0,h=a.length;g<h;++g)c[a[g]]=b;return c},getStyledSpans:function(a,b){var c=CKEDITOR.env.ie&&8==CKEDITOR.env.version?a.toUpperCase():a,c=b.find("span[style*\x3d"+c+"]").toArray();return CKEDITOR.tools.array.filter(c,function(b){return!!b.getStyle(a)})},fixDomain:function(){for(var a;;)try{a=
window.parent.document.domain;break}catch(b){a=a?a.replace(/.+?(?:\.|$)/,""):document.domain;if(!a)break;document.domain=a}return!!a},eventsBuffer:function(a,b,c){return new this.buffers.event(a,b,c)},enableHtml5Elements:function(a,b){for(var c="abbr article aside audio bdi canvas data datalist details figcaption figure footer header hgroup main mark meter nav output progress section summary time video".split(" "),g=c.length,h;g--;)h=a.createElement(c[g]),b&&a.appendChild(h)},checkIfAnyArrayItemMatches:function(a,
b){for(var c=0,g=a.length;c<g;++c)if(a[c].match(b))return!0;return!1},checkIfAnyObjectPropertyMatches:function(a,b){for(var c in a)if(c.match(b))return!0;return!1},keystrokeToString:function(a,b){var c=this.keystrokeToArray(a,b);c.display=c.display.join("+");c.aria=c.aria.join("+");return c},keystrokeToArray:function(a,b){var c=b&16711680,g=b&65535,h=CKEDITOR.env.mac,l=[],e=[];c&CKEDITOR.CTRL&&(l.push(h?"⌘":a[17]),e.push(h?a[224]:a[17]));c&CKEDITOR.ALT&&(l.push(h?"⌥":a[18]),e.push(a[18]));c&CKEDITOR.SHIFT&&
(l.push(h?"⇧":a[16]),e.push(a[16]));g&&(a[g]?(l.push(a[g]),e.push(a[g])):(l.push(String.fromCharCode(g)),e.push(String.fromCharCode(g))));return{display:l,aria:e}},transparentImageData:"data:image/gif;base64,R0lGODlhAQABAPABAP///wAAACH5BAEKAAAALAAAAAABAAEAAAICRAEAOw\x3d\x3d",getCookie:function(a){a=a.toLowerCase();for(var b=document.cookie.split(";"),c,g,h=0;h<b.length;h++)if(c=b[h].split("\x3d"),g=decodeURIComponent(CKEDITOR.tools.trim(c[0]).toLowerCase()),g===a)return decodeURIComponent(1<c.length?
c[1]:"");return null},setCookie:function(a,b){document.cookie=encodeURIComponent(a)+"\x3d"+encodeURIComponent(b)+";path\x3d/"},getCsrfToken:function(){var a=CKEDITOR.tools.getCookie("ckCsrfToken");if(!a||40!=a.length){var a=[],b="";if(window.crypto&&window.crypto.getRandomValues)a=new Uint8Array(40),window.crypto.getRandomValues(a);else for(var c=0;40>c;c++)a.push(Math.floor(256*Math.random()));for(c=0;c<a.length;c++)var g="abcdefghijklmnopqrstuvwxyz0123456789".charAt(a[c]%36),b=b+(.5<Math.random()?
g.toUpperCase():g);a=b;CKEDITOR.tools.setCookie("ckCsrfToken",a)}return a},escapeCss:function(a){if(a)if(window.CSS&&CSS.escape)a=CSS.escape(a);else{a=String(a);for(var b=a.length,c=-1,g,h="",l=a.charCodeAt(0);++c<b;)g=a.charCodeAt(c),h=0==g?h+"�":127==g||1<=g&&31>=g||0==c&&48<=g&&57>=g||1==c&&48<=g&&57>=g&&45==l?h+("\\"+g.toString(16)+" "):0==c&&1==b&&45==g?h+("\\"+a.charAt(c)):128<=g||45==g||95==g||48<=g&&57>=g||65<=g&&90>=g||97<=g&&122>=g?h+a.charAt(c):h+("\\"+a.charAt(c));a=h}else a="";return a},
getMouseButton:function(a){return(a=a&&a.data?a.data.$:a)?CKEDITOR.tools.normalizeMouseButton(a.button):!1},normalizeMouseButton:function(a,b){if(!CKEDITOR.env.ie||9<=CKEDITOR.env.version&&!CKEDITOR.env.ie6Compat)return a;for(var c=[[CKEDITOR.MOUSE_BUTTON_LEFT,1],[CKEDITOR.MOUSE_BUTTON_MIDDLE,4],[CKEDITOR.MOUSE_BUTTON_RIGHT,2]],g=0;g<c.length;g++){var h=c[g];if(h[0]===a&&b)return h[1];if(!b&&h[1]===a)return h[0]}},convertHexStringToBytes:function(a){var b=[],c=a.length/2,g;for(g=0;g<c;g++)b.push(parseInt(a.substr(2*
g,2),16));return b},convertBytesToBase64:function(a){var b="",c=a.length,g;for(g=0;g<c;g+=3){var h=a.slice(g,g+3),l=h.length,e=[],d;if(3>l)for(d=l;3>d;d++)h[d]=0;e[0]=(h[0]&252)>>2;e[1]=(h[0]&3)<<4|h[1]>>4;e[2]=(h[1]&15)<<2|(h[2]&192)>>6;e[3]=h[2]&63;for(d=0;4>d;d++)b=d<=l?b+"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".charAt(e[d]):b+"\x3d"}return b},style:{parse:{_borderStyle:"none hidden dotted dashed solid double groove ridge inset outset".split(" "),_widthRegExp:/^(thin|medium|thick|[\+-]?\d+(\.\d+)?[a-z%]+|[\+-]?0+(\.0+)?|\.\d+[a-z%]+)$/,
_rgbaRegExp:/rgba?\(\s*\d+%?\s*,\s*\d+%?\s*,\s*\d+%?\s*(?:,\s*[0-9.]+\s*)?\)/gi,_hslaRegExp:/hsla?\(\s*[0-9.]+\s*,\s*\d+%\s*,\s*\d+%\s*(?:,\s*[0-9.]+\s*)?\)/gi,background:function(a){var b={},c=this._findColor(a);c.length&&(b.color=c[0],CKEDITOR.tools.array.forEach(c,function(b){a=a.replace(b,"")}));if(a=CKEDITOR.tools.trim(a))b.unprocessed=a;return b},margin:function(a){return CKEDITOR.tools.style.parse.sideShorthand(a,function(a){return a.match(/(?:\-?[\.\d]+(?:%|\w*)|auto|inherit|initial|unset|revert)/g)||
["0px"]})},sideShorthand:function(a,b){function c(a){g.top=h[a[0]];g.right=h[a[1]];g.bottom=h[a[2]];g.left=h[a[3]]}var g={},h=b?b(a):a.split(/\s+/);switch(h.length){case 1:c([0,0,0,0]);break;case 2:c([0,1,0,1]);break;case 3:c([0,1,2,1]);break;case 4:c([0,1,2,3])}return g},border:function(a){return CKEDITOR.tools.style.border.fromCssRule(a)},_findColor:function(a){var b=[],c=CKEDITOR.tools.array,b=b.concat(a.match(this._rgbaRegExp)||[]),b=b.concat(a.match(this._hslaRegExp)||[]);return b=b.concat(c.filter(a.split(/\s+/),
function(a){return a.match(/^\#[a-f0-9]{3}(?:[a-f0-9]{3})?$/gi)?!0:a.toLowerCase()in CKEDITOR.tools.style.parse._colors}))}}},array:{filter:function(a,b,c){var g=[];this.forEach(a,function(h,l){b.call(c,h,l,a)&&g.push(h)});return g},find:function(a,b,c){for(var g=a.length,h=0;h<g;){if(b.call(c,a[h],h,a))return a[h];h++}},forEach:function(a,b,c){var g=a.length,h;for(h=0;h<g;h++)b.call(c,a[h],h,a)},map:function(a,b,c){for(var g=[],h=0;h<a.length;h++)g.push(b.call(c,a[h],h,a));return g},reduce:function(a,
b,c,g){for(var h=0;h<a.length;h++)c=b.call(g,c,a[h],h,a);return c},every:function(a,b,c){if(!a.length)return!0;b=this.filter(a,b,c);return a.length===b.length},some:function(a,b,c){for(var g=0;g<a.length;g++)if(b.call(c,a[g],g,a))return!0;return!1},zip:function(a,b){return CKEDITOR.tools.array.map(a,function(a,c){return[a,b[c]]})},unique:function(a){return this.filter(a,function(b,c){return c===CKEDITOR.tools.array.indexOf(a,b)})}},object:{DONT_ENUMS:"toString toLocaleString valueOf hasOwnProperty isPrototypeOf propertyIsEnumerable constructor".split(" "),
entries:function(a){return CKEDITOR.tools.array.map(CKEDITOR.tools.object.keys(a),function(b){return[b,a[b]]})},values:function(a){return CKEDITOR.tools.array.map(CKEDITOR.tools.object.keys(a),function(b){return a[b]})},keys:function(a){var b=Object.prototype.hasOwnProperty,c=[],g=CKEDITOR.tools.object.DONT_ENUMS;if(CKEDITOR.env.ie&&9>CKEDITOR.env.version&&(!a||"object"!==typeof a)){b=[];if("string"===typeof a)for(c=0;c<a.length;c++)b.push(String(c));return b}for(var h in a)c.push(h);if(CKEDITOR.env.ie&&
9>CKEDITOR.env.version)for(h=0;h<g.length;h++)b.call(a,g[h])&&c.push(g[h]);return c},findKey:function(a,b){if("object"!==typeof a)return null;for(var c in a)if(a[c]===b)return c;return null},merge:function(a,b){var c=CKEDITOR.tools,g=c.clone(a),h=c.clone(b);c.array.forEach(c.object.keys(h),function(a){g[a]="object"===typeof h[a]&&"object"===typeof g[a]?c.object.merge(g[a],h[a]):h[a]});return g}},getAbsoluteRectPosition:function(a,b){function c(a){if(a){var b=a.getClientRect();g.top+=b.top;g.left+=
b.left;"x"in g&&"y"in g&&(g.x+=b.x,g.y+=b.y);c(a.getWindow().getFrame())}}var g=CKEDITOR.tools.copy(b);c(a.getFrame());var h=CKEDITOR.document.getWindow().getScrollPosition();g.top+=h.y;g.left+=h.x;"x"in g&&"y"in g&&(g.y+=h.y,g.x+=h.x);g.right=g.left+g.width;g.bottom=g.top+g.height;return g}};a.prototype={reset:function(){this._lastOutput=0;this._clearTimer()},_reschedule:function(){return!1},_call:function(){this._output()},_clearTimer:function(){this._scheduledTimer&&clearTimeout(this._scheduledTimer);
this._scheduledTimer=0}};d.prototype=CKEDITOR.tools.prototypedCopy(a.prototype);d.prototype._reschedule=function(){this._scheduledTimer&&this._clearTimer()};d.prototype._call=function(){this._output.apply(this._context,this._args)};CKEDITOR.tools.buffers={};CKEDITOR.tools.buffers.event=a;CKEDITOR.tools.buffers.throttle=d;CKEDITOR.tools.style.border=CKEDITOR.tools.createClass({$:function(a){a=a||{};this.width=a.width;this.style=a.style;this.color=a.color;this._.normalize()},_:{normalizeMap:{color:[[/windowtext/g,
"black"]]},normalize:function(){for(var a in this._.normalizeMap){var b=this[a];b&&(this[a]=CKEDITOR.tools.array.reduce(this._.normalizeMap[a],function(a,b){return a.replace(b[0],b[1])},b))}}},proto:{toString:function(){return CKEDITOR.tools.array.filter([this.width,this.style,this.color],function(a){return!!a}).join(" ")}},statics:{fromCssRule:function(a){var b={},c=a.split(/\s+/g);a=CKEDITOR.tools.style.parse._findColor(a);a.length&&(b.color=a[0]);CKEDITOR.tools.array.forEach(c,function(a){b.style||
-1===CKEDITOR.tools.indexOf(CKEDITOR.tools.style.parse._borderStyle,a)?!b.width&&CKEDITOR.tools.style.parse._widthRegExp.test(a)&&(b.width=a):b.style=a});return new CKEDITOR.tools.style.border(b)},splitCssValues:function(a,b){b=b||{};var c=CKEDITOR.tools.array.reduce(["width","style","color"],function(c,g){var h=a["border-"+g]||b[g];c[g]=h?CKEDITOR.tools.style.parse.sideShorthand(h):null;return c},{});return CKEDITOR.tools.array.reduce(["top","right","bottom","left"],function(b,g){var h={},e;for(e in c){var d=
a["border-"+g+"-"+e];h[e]=d?d:c[e]&&c[e][g]}b["border-"+g]=new CKEDITOR.tools.style.border(h);return b},{})}}});CKEDITOR.tools.array.indexOf=CKEDITOR.tools.indexOf;CKEDITOR.tools.array.isArray=CKEDITOR.tools.isArray;CKEDITOR.MOUSE_BUTTON_LEFT=0;CKEDITOR.MOUSE_BUTTON_MIDDLE=1;CKEDITOR.MOUSE_BUTTON_RIGHT=2})();
CKEDITOR.dtd=function(){var a=CKEDITOR.tools.extend,d=function(a,b){for(var c=CKEDITOR.tools.clone(a),h=1;h<arguments.length;h++){b=arguments[h];for(var e in b)delete c[e]}return c},b={},c={},f={address:1,article:1,aside:1,blockquote:1,details:1,div:1,dl:1,fieldset:1,figure:1,footer:1,form:1,h1:1,h2:1,h3:1,h4:1,h5:1,h6:1,header:1,hgroup:1,hr:1,main:1,menu:1,nav:1,ol:1,p:1,pre:1,section:1,table:1,ul:1},e={command:1,link:1,meta:1,noscript:1,script:1,style:1},k={},h={"#":1},p={center:1,dir:1,noframes:1};
a(b,{a:1,abbr:1,area:1,audio:1,b:1,bdi:1,bdo:1,br:1,button:1,canvas:1,cite:1,code:1,command:1,datalist:1,del:1,dfn:1,em:1,embed:1,i:1,iframe:1,img:1,input:1,ins:1,kbd:1,keygen:1,label:1,map:1,mark:1,meter:1,noscript:1,object:1,output:1,progress:1,q:1,ruby:1,s:1,samp:1,script:1,select:1,small:1,span:1,strong:1,sub:1,sup:1,textarea:1,time:1,u:1,"var":1,video:1,wbr:1},h,{acronym:1,applet:1,basefont:1,big:1,font:1,isindex:1,strike:1,style:1,tt:1});a(c,f,b,p);d={a:d(b,{a:1,button:1}),abbr:b,address:c,
area:k,article:c,aside:c,audio:a({source:1,track:1},c),b:b,base:k,bdi:b,bdo:b,blockquote:c,body:c,br:k,button:d(b,{a:1,button:1}),canvas:b,caption:c,cite:b,code:b,col:k,colgroup:{col:1},command:k,datalist:a({option:1},b),dd:c,del:b,details:a({summary:1},c),dfn:b,div:c,dl:{dt:1,dd:1},dt:c,em:b,embed:k,fieldset:a({legend:1},c),figcaption:c,figure:a({figcaption:1},c),footer:c,form:c,h1:b,h2:b,h3:b,h4:b,h5:b,h6:b,head:a({title:1,base:1},e),header:c,hgroup:{h1:1,h2:1,h3:1,h4:1,h5:1,h6:1},hr:k,html:a({head:1,
body:1},c,e),i:b,iframe:h,img:k,input:k,ins:b,kbd:b,keygen:k,label:b,legend:b,li:c,link:k,main:c,map:c,mark:b,menu:a({li:1},c),meta:k,meter:d(b,{meter:1}),nav:c,noscript:a({link:1,meta:1,style:1},b),object:a({param:1},b),ol:{li:1},optgroup:{option:1},option:h,output:b,p:b,param:k,pre:b,progress:d(b,{progress:1}),q:b,rp:b,rt:b,ruby:a({rp:1,rt:1},b),s:b,samp:b,script:h,section:c,select:{optgroup:1,option:1},small:b,source:k,span:b,strong:b,style:h,sub:b,summary:a({h1:1,h2:1,h3:1,h4:1,h5:1,h6:1},b),
sup:b,table:{caption:1,colgroup:1,thead:1,tfoot:1,tbody:1,tr:1},tbody:{tr:1},td:c,textarea:h,tfoot:{tr:1},th:c,thead:{tr:1},time:d(b,{time:1}),title:h,tr:{th:1,td:1},track:k,u:b,ul:{li:1},"var":b,video:a({source:1,track:1},c),wbr:k,acronym:b,applet:a({param:1},c),basefont:k,big:b,center:c,dialog:k,dir:{li:1},font:b,isindex:k,noframes:c,strike:b,tt:b};a(d,{$block:a({audio:1,dd:1,dt:1,figcaption:1,li:1,video:1},f,p),$blockLimit:{article:1,aside:1,audio:1,body:1,caption:1,details:1,dir:1,div:1,dl:1,
fieldset:1,figcaption:1,figure:1,footer:1,form:1,header:1,hgroup:1,main:1,menu:1,nav:1,ol:1,section:1,table:1,td:1,th:1,tr:1,ul:1,video:1},$cdata:{script:1,style:1},$editable:{address:1,article:1,aside:1,blockquote:1,body:1,details:1,div:1,fieldset:1,figcaption:1,footer:1,form:1,h1:1,h2:1,h3:1,h4:1,h5:1,h6:1,header:1,hgroup:1,main:1,nav:1,p:1,pre:1,section:1},$empty:{area:1,base:1,basefont:1,br:1,col:1,command:1,dialog:1,embed:1,hr:1,img:1,input:1,isindex:1,keygen:1,link:1,meta:1,param:1,source:1,
track:1,wbr:1},$inline:b,$list:{dl:1,ol:1,ul:1},$listItem:{dd:1,dt:1,li:1},$nonBodyContent:a({body:1,head:1,html:1},d.head),$nonEditable:{applet:1,audio:1,button:1,embed:1,iframe:1,map:1,object:1,option:1,param:1,script:1,textarea:1,video:1},$object:{applet:1,audio:1,button:1,hr:1,iframe:1,img:1,input:1,object:1,select:1,table:1,textarea:1,video:1},$removeEmpty:{abbr:1,acronym:1,b:1,bdi:1,bdo:1,big:1,cite:1,code:1,del:1,dfn:1,em:1,font:1,i:1,ins:1,label:1,kbd:1,mark:1,meter:1,output:1,q:1,ruby:1,
s:1,samp:1,small:1,span:1,strike:1,strong:1,sub:1,sup:1,time:1,tt:1,u:1,"var":1},$tabIndex:{a:1,area:1,button:1,input:1,object:1,select:1,textarea:1},$tableContent:{caption:1,col:1,colgroup:1,tbody:1,td:1,tfoot:1,th:1,thead:1,tr:1},$transparent:{a:1,audio:1,canvas:1,del:1,ins:1,map:1,noscript:1,object:1,video:1},$intermediate:{caption:1,colgroup:1,dd:1,dt:1,figcaption:1,legend:1,li:1,optgroup:1,option:1,rp:1,rt:1,summary:1,tbody:1,td:1,tfoot:1,th:1,thead:1,tr:1}});return d}();
CKEDITOR.dom.event=function(a){this.$=a};
CKEDITOR.dom.event.prototype={getKey:function(){return this.$.keyCode||this.$.which},getKeystroke:function(){var a=this.getKey();if(this.$.ctrlKey||this.$.metaKey)a+=CKEDITOR.CTRL;this.$.shiftKey&&(a+=CKEDITOR.SHIFT);this.$.altKey&&(a+=CKEDITOR.ALT);return a},preventDefault:function(a){var d=this.$;d.preventDefault?d.preventDefault():d.returnValue=!1;a&&this.stopPropagation()},stopPropagation:function(){var a=this.$;a.stopPropagation?a.stopPropagation():a.cancelBubble=!0},getTarget:function(){var a=
this.$.target||this.$.srcElement;return a?new CKEDITOR.dom.node(a):null},getPhase:function(){return this.$.eventPhase||2},getPageOffset:function(){var a=this.getTarget().getDocument().$;return{x:this.$.pageX||this.$.clientX+(a.documentElement.scrollLeft||a.body.scrollLeft),y:this.$.pageY||this.$.clientY+(a.documentElement.scrollTop||a.body.scrollTop)}}};CKEDITOR.CTRL=1114112;CKEDITOR.SHIFT=2228224;CKEDITOR.ALT=4456448;CKEDITOR.EVENT_PHASE_CAPTURING=1;CKEDITOR.EVENT_PHASE_AT_TARGET=2;
CKEDITOR.EVENT_PHASE_BUBBLING=3;CKEDITOR.HISTORY_NATIVE=1;CKEDITOR.HISTORY_HASH=2;CKEDITOR.HISTORY_OFF=0;CKEDITOR.dom.domObject=function(a){a&&(this.$=a)};
CKEDITOR.dom.domObject.prototype=function(){var a=function(a,b){return function(c){"undefined"!=typeof CKEDITOR&&a.fire(b,new CKEDITOR.dom.event(c))}};return{getPrivate:function(){var a;(a=this.getCustomData("_"))||this.setCustomData("_",a={});return a},on:function(d){var b=this.getCustomData("_cke_nativeListeners");b||(b={},this.setCustomData("_cke_nativeListeners",b));b[d]||(b=b[d]=a(this,d),this.$.addEventListener?this.$.addEventListener(d,b,!!CKEDITOR.event.useCapture):this.$.attachEvent&&this.$.attachEvent("on"+
d,b));return CKEDITOR.event.prototype.on.apply(this,arguments)},removeListener:function(a){CKEDITOR.event.prototype.removeListener.apply(this,arguments);if(!this.hasListeners(a)){var b=this.getCustomData("_cke_nativeListeners"),c=b&&b[a];c&&(this.$.removeEventListener?this.$.removeEventListener(a,c,!1):this.$.detachEvent&&this.$.detachEvent("on"+a,c),delete b[a])}},removeAllListeners:function(){try{var a=this.getCustomData("_cke_nativeListeners"),b;for(b in a){var c=a[b];this.$.detachEvent?this.$.detachEvent("on"+
b,c):this.$.removeEventListener&&this.$.removeEventListener(b,c,!1);delete a[b]}}catch(f){if(!CKEDITOR.env.edge||-2146828218!==f.number)throw f;}CKEDITOR.event.prototype.removeAllListeners.call(this)}}}();
(function(a){var d={};CKEDITOR.on("reset",function(){d={}});a.equals=function(a){try{return a&&a.$===this.$}catch(c){return!1}};a.setCustomData=function(a,c){var f=this.getUniqueId();(d[f]||(d[f]={}))[a]=c;return this};a.getCustomData=function(a){var c=this.$["data-cke-expando"];return(c=c&&d[c])&&a in c?c[a]:null};a.removeCustomData=function(a){var c=this.$["data-cke-expando"],c=c&&d[c],f,e;c&&(f=c[a],e=a in c,delete c[a]);return e?f:null};a.clearCustomData=function(){this.removeAllListeners();var a=
this.getUniqueId();a&&delete d[a]};a.getUniqueId=function(){return this.$["data-cke-expando"]||(this.$["data-cke-expando"]=CKEDITOR.tools.getNextNumber())};CKEDITOR.event.implementOn(a)})(CKEDITOR.dom.domObject.prototype);
CKEDITOR.dom.node=function(a){return a?new CKEDITOR.dom[a.nodeType==CKEDITOR.NODE_DOCUMENT?"document":a.nodeType==CKEDITOR.NODE_ELEMENT?"element":a.nodeType==CKEDITOR.NODE_TEXT?"text":a.nodeType==CKEDITOR.NODE_COMMENT?"comment":a.nodeType==CKEDITOR.NODE_DOCUMENT_FRAGMENT?"documentFragment":"domObject"](a):this};CKEDITOR.dom.node.prototype=new CKEDITOR.dom.domObject;CKEDITOR.NODE_ELEMENT=1;CKEDITOR.NODE_DOCUMENT=9;CKEDITOR.NODE_TEXT=3;CKEDITOR.NODE_COMMENT=8;CKEDITOR.NODE_DOCUMENT_FRAGMENT=11;
CKEDITOR.POSITION_IDENTICAL=0;CKEDITOR.POSITION_DISCONNECTED=1;CKEDITOR.POSITION_FOLLOWING=2;CKEDITOR.POSITION_PRECEDING=4;CKEDITOR.POSITION_IS_CONTAINED=8;CKEDITOR.POSITION_CONTAINS=16;
CKEDITOR.tools.extend(CKEDITOR.dom.node.prototype,{appendTo:function(a,d){a.append(this,d);return a},clone:function(a,d){function b(c){c["data-cke-expando"]&&(c["data-cke-expando"]=!1);if(c.nodeType==CKEDITOR.NODE_ELEMENT||c.nodeType==CKEDITOR.NODE_DOCUMENT_FRAGMENT)if(d||c.nodeType!=CKEDITOR.NODE_ELEMENT||c.removeAttribute("id",!1),a){c=c.childNodes;for(var f=0;f<c.length;f++)b(c[f])}}function c(b){if(b.type==CKEDITOR.NODE_ELEMENT||b.type==CKEDITOR.NODE_DOCUMENT_FRAGMENT){if(b.type!=CKEDITOR.NODE_DOCUMENT_FRAGMENT){var d=
b.getName();":"==d[0]&&b.renameNode(d.substring(1))}if(a)for(d=0;d<b.getChildCount();d++)c(b.getChild(d))}}var f=this.$.cloneNode(a);b(f);f=new CKEDITOR.dom.node(f);CKEDITOR.env.ie&&9>CKEDITOR.env.version&&(this.type==CKEDITOR.NODE_ELEMENT||this.type==CKEDITOR.NODE_DOCUMENT_FRAGMENT)&&c(f);return f},hasPrevious:function(){return!!this.$.previousSibling},hasNext:function(){return!!this.$.nextSibling},insertAfter:function(a){a.$.parentNode.insertBefore(this.$,a.$.nextSibling);return a},insertBefore:function(a){a.$.parentNode.insertBefore(this.$,
a.$);return a},insertBeforeMe:function(a){this.$.parentNode.insertBefore(a.$,this.$);return a},getAddress:function(a){for(var d=[],b=this.getDocument().$.documentElement,c=this;c&&c!=b;){var f=c.getParent();f&&d.unshift(this.getIndex.call(c,a));c=f}return d},getDocument:function(){return new CKEDITOR.dom.document(this.$.ownerDocument||this.$.parentNode.ownerDocument)},getIndex:function(a){function d(a,b){var c=b?a.getNext():a.getPrevious();return c&&c.type==CKEDITOR.NODE_TEXT?c.isEmpty()?d(c,b):c:
null}var b=this,c=-1,f;if(!this.getParent()||a&&b.type==CKEDITOR.NODE_TEXT&&b.isEmpty()&&!d(b)&&!d(b,!0))return-1;do if(!a||b.equals(this)||b.type!=CKEDITOR.NODE_TEXT||!f&&!b.isEmpty())c++,f=b.type==CKEDITOR.NODE_TEXT;while(b=b.getPrevious());return c},getNextSourceNode:function(a,d,b){if(b&&!b.call){var c=b;b=function(a){return!a.equals(c)}}a=!a&&this.getFirst&&this.getFirst();var f;if(!a){if(this.type==CKEDITOR.NODE_ELEMENT&&b&&!1===b(this,!0))return null;a=this.getNext()}for(;!a&&(f=(f||this).getParent());){if(b&&
!1===b(f,!0))return null;a=f.getNext()}return!a||b&&!1===b(a)?null:d&&d!=a.type?a.getNextSourceNode(!1,d,b):a},getPreviousSourceNode:function(a,d,b){if(b&&!b.call){var c=b;b=function(a){return!a.equals(c)}}a=!a&&this.getLast&&this.getLast();var f;if(!a){if(this.type==CKEDITOR.NODE_ELEMENT&&b&&!1===b(this,!0))return null;a=this.getPrevious()}for(;!a&&(f=(f||this).getParent());){if(b&&!1===b(f,!0))return null;a=f.getPrevious()}return!a||b&&!1===b(a)?null:d&&a.type!=d?a.getPreviousSourceNode(!1,d,b):
a},getPrevious:function(a){var d=this.$,b;do b=(d=d.previousSibling)&&10!=d.nodeType&&new CKEDITOR.dom.node(d);while(b&&a&&!a(b));return b},getNext:function(a){var d=this.$,b;do b=(d=d.nextSibling)&&new CKEDITOR.dom.node(d);while(b&&a&&!a(b));return b},getParent:function(a){var d=this.$.parentNode;return d&&(d.nodeType==CKEDITOR.NODE_ELEMENT||a&&d.nodeType==CKEDITOR.NODE_DOCUMENT_FRAGMENT)?new CKEDITOR.dom.node(d):null},getParents:function(a){var d=this,b=[];do b[a?"push":"unshift"](d);while(d=d.getParent());
return b},getCommonAncestor:function(a){if(a.equals(this))return this;if(a.contains&&a.contains(this))return a;var d=this.contains?this:this.getParent();do if(d.contains(a))return d;while(d=d.getParent());return null},getPosition:function(a){var d=this.$,b=a.$;if(d.compareDocumentPosition)return d.compareDocumentPosition(b);if(d==b)return CKEDITOR.POSITION_IDENTICAL;if(this.type==CKEDITOR.NODE_ELEMENT&&a.type==CKEDITOR.NODE_ELEMENT){if(d.contains){if(d.contains(b))return CKEDITOR.POSITION_CONTAINS+
CKEDITOR.POSITION_PRECEDING;if(b.contains(d))return CKEDITOR.POSITION_IS_CONTAINED+CKEDITOR.POSITION_FOLLOWING}if("sourceIndex"in d)return 0>d.sourceIndex||0>b.sourceIndex?CKEDITOR.POSITION_DISCONNECTED:d.sourceIndex<b.sourceIndex?CKEDITOR.POSITION_PRECEDING:CKEDITOR.POSITION_FOLLOWING}d=this.getAddress();a=a.getAddress();for(var b=Math.min(d.length,a.length),c=0;c<b;c++)if(d[c]!=a[c])return d[c]<a[c]?CKEDITOR.POSITION_PRECEDING:CKEDITOR.POSITION_FOLLOWING;return d.length<a.length?CKEDITOR.POSITION_CONTAINS+
CKEDITOR.POSITION_PRECEDING:CKEDITOR.POSITION_IS_CONTAINED+CKEDITOR.POSITION_FOLLOWING},getAscendant:function(a,d){var b=this.$,c,f;d||(b=b.parentNode);"function"==typeof a?(f=!0,c=a):(f=!1,c=function(b){b="string"==typeof b.nodeName?b.nodeName.toLowerCase():"";return"string"==typeof a?b==a:b in a});for(;b;){if(c(f?new CKEDITOR.dom.node(b):b))return new CKEDITOR.dom.node(b);try{b=b.parentNode}catch(e){b=null}}return null},hasAscendant:function(a,d){var b=this.$;d||(b=b.parentNode);for(;b;){if(b.nodeName&&
b.nodeName.toLowerCase()==a)return!0;b=b.parentNode}return!1},move:function(a,d){a.append(this.remove(),d)},remove:function(a){var d=this.$,b=d.parentNode;if(b){if(a)for(;a=d.firstChild;)b.insertBefore(d.removeChild(a),d);b.removeChild(d)}return this},replace:function(a){this.insertBefore(a);a.remove()},trim:function(){this.ltrim();this.rtrim()},ltrim:function(){for(var a;this.getFirst&&(a=this.getFirst());){if(a.type==CKEDITOR.NODE_TEXT){var d=CKEDITOR.tools.ltrim(a.getText()),b=a.getLength();if(d)d.length<
b&&(a.split(b-d.length),this.$.removeChild(this.$.firstChild));else{a.remove();continue}}break}},rtrim:function(){for(var a;this.getLast&&(a=this.getLast());){if(a.type==CKEDITOR.NODE_TEXT){var d=CKEDITOR.tools.rtrim(a.getText()),b=a.getLength();if(d)d.length<b&&(a.split(d.length),this.$.lastChild.parentNode.removeChild(this.$.lastChild));else{a.remove();continue}}break}CKEDITOR.env.needsBrFiller&&(a=this.$.lastChild)&&1==a.type&&"br"==a.nodeName.toLowerCase()&&a.parentNode.removeChild(a)},isReadOnly:function(a){var d=
this;this.type!=CKEDITOR.NODE_ELEMENT&&(d=this.getParent());CKEDITOR.env.edge&&d&&d.is("textarea","input")&&(a=!0);if(!a&&d&&"undefined"!=typeof d.$.isContentEditable)return!(d.$.isContentEditable||d.data("cke-editable"));for(;d;){if(d.data("cke-editable"))return!1;if(d.hasAttribute("contenteditable"))return"false"==d.getAttribute("contenteditable");d=d.getParent()}return!0}});CKEDITOR.dom.window=function(a){CKEDITOR.dom.domObject.call(this,a)};CKEDITOR.dom.window.prototype=new CKEDITOR.dom.domObject;
CKEDITOR.tools.extend(CKEDITOR.dom.window.prototype,{focus:function(){this.$.focus()},getViewPaneSize:function(){var a=this.$.document,d="CSS1Compat"==a.compatMode;return{width:(d?a.documentElement.clientWidth:a.body.clientWidth)||0,height:(d?a.documentElement.clientHeight:a.body.clientHeight)||0}},getScrollPosition:function(){var a=this.$;if("pageXOffset"in a)return{x:a.pageXOffset||0,y:a.pageYOffset||0};a=a.document;return{x:a.documentElement.scrollLeft||a.body.scrollLeft||0,y:a.documentElement.scrollTop||
a.body.scrollTop||0}},getFrame:function(){var a=this.$.frameElement;return a?new CKEDITOR.dom.element.get(a):null}});CKEDITOR.dom.document=function(a){CKEDITOR.dom.domObject.call(this,a)};CKEDITOR.dom.document.prototype=new CKEDITOR.dom.domObject;
CKEDITOR.tools.extend(CKEDITOR.dom.document.prototype,{type:CKEDITOR.NODE_DOCUMENT,appendStyleSheet:function(a){a=CKEDITOR.appendTimestamp(a);if(this.$.createStyleSheet)this.$.createStyleSheet(a);else{var d=new CKEDITOR.dom.element("link");d.setAttributes({rel:"stylesheet",type:"text/css",href:a});this.getHead().append(d)}},appendStyleText:function(a){if(this.$.createStyleSheet){var d=this.$.createStyleSheet("");d.cssText=a}else{var b=new CKEDITOR.dom.element("style",this);b.append(new CKEDITOR.dom.text(a,
this));this.getHead().append(b)}return d||b.$.sheet},createElement:function(a,d){var b=new CKEDITOR.dom.element(a,this);d&&(d.attributes&&b.setAttributes(d.attributes),d.styles&&b.setStyles(d.styles));return b},createText:function(a){return new CKEDITOR.dom.text(a,this)},focus:function(){this.getWindow().focus()},getActive:function(){var a;try{a=this.$.activeElement}catch(d){return null}return new CKEDITOR.dom.element(a)},getById:function(a){return(a=this.$.getElementById(a))?new CKEDITOR.dom.element(a):
null},getByAddress:function(a,d){for(var b=this.$.documentElement,c=0;b&&c<a.length;c++){var f=a[c];if(d)for(var e=-1,k=0;k<b.childNodes.length;k++){var h=b.childNodes[k];if(!0!==d||3!=h.nodeType||!h.previousSibling||3!=h.previousSibling.nodeType)if(e++,e==f){b=h;break}}else b=b.childNodes[f]}return b?new CKEDITOR.dom.node(b):null},getElementsByTag:function(a,d){CKEDITOR.env.ie&&8>=document.documentMode||!d||(a=d+":"+a);return new CKEDITOR.dom.nodeList(this.$.getElementsByTagName(a))},getHead:function(){var a=
this.$.getElementsByTagName("head")[0];return a=a?new CKEDITOR.dom.element(a):this.getDocumentElement().append(new CKEDITOR.dom.element("head"),!0)},getBody:function(){return new CKEDITOR.dom.element(this.$.body)},getDocumentElement:function(){return new CKEDITOR.dom.element(this.$.documentElement)},getWindow:function(){return new CKEDITOR.dom.window(this.$.parentWindow||this.$.defaultView)},write:function(a){this.$.open("text/html","replace");CKEDITOR.env.ie&&(a=a.replace(/(?:^\s*<!DOCTYPE[^>]*?>)|^/i,
'$\x26\n\x3cscript data-cke-temp\x3d"1"\x3e('+CKEDITOR.tools.fixDomain+")();\x3c/script\x3e"));this.$.write(a);this.$.close()},find:function(a){return new CKEDITOR.dom.nodeList(this.$.querySelectorAll(a))},findOne:function(a){return(a=this.$.querySelector(a))?new CKEDITOR.dom.element(a):null},_getHtml5ShivFrag:function(){var a=this.getCustomData("html5ShivFrag");a||(a=this.$.createDocumentFragment(),CKEDITOR.tools.enableHtml5Elements(a,!0),this.setCustomData("html5ShivFrag",a));return a}});
CKEDITOR.dom.nodeList=function(a){this.$=a};CKEDITOR.dom.nodeList.prototype={count:function(){return this.$.length},getItem:function(a){return 0>a||a>=this.$.length?null:(a=this.$[a])?new CKEDITOR.dom.node(a):null},toArray:function(){return CKEDITOR.tools.array.map(this.$,function(a){return new CKEDITOR.dom.node(a)})}};CKEDITOR.dom.element=function(a,d){"string"==typeof a&&(a=(d?d.$:document).createElement(a));CKEDITOR.dom.domObject.call(this,a)};
CKEDITOR.dom.element.get=function(a){return(a="string"==typeof a?document.getElementById(a)||document.getElementsByName(a)[0]:a)&&(a.$?a:new CKEDITOR.dom.element(a))};CKEDITOR.dom.element.prototype=new CKEDITOR.dom.node;CKEDITOR.dom.element.createFromHtml=function(a,d){var b=new CKEDITOR.dom.element("div",d);b.setHtml(a);return b.getFirst().remove()};
CKEDITOR.dom.element.setMarker=function(a,d,b,c){var f=d.getCustomData("list_marker_id")||d.setCustomData("list_marker_id",CKEDITOR.tools.getNextNumber()).getCustomData("list_marker_id"),e=d.getCustomData("list_marker_names")||d.setCustomData("list_marker_names",{}).getCustomData("list_marker_names");a[f]=d;e[b]=1;return d.setCustomData(b,c)};CKEDITOR.dom.element.clearAllMarkers=function(a){for(var d in a)CKEDITOR.dom.element.clearMarkers(a,a[d],1)};
CKEDITOR.dom.element.clearMarkers=function(a,d,b){var c=d.getCustomData("list_marker_names"),f=d.getCustomData("list_marker_id"),e;for(e in c)d.removeCustomData(e);d.removeCustomData("list_marker_names");b&&(d.removeCustomData("list_marker_id"),delete a[f])};
(function(){function a(a,b){return-1<(" "+a+" ").replace(e," ").indexOf(" "+b+" ")}function d(a){var b=!0;a.$.id||(a.$.id="cke_tmp_"+CKEDITOR.tools.getNextNumber(),b=!1);return function(){b||a.removeAttribute("id")}}function b(a,b){var c=CKEDITOR.tools.escapeCss(a.$.id);return"#"+c+" "+b.split(/,\s*/).join(", #"+c+" ")}function c(a){for(var b=0,c=0,g=k[a].length;c<g;c++)b+=parseFloat(this.getComputedStyle(k[a][c])||0,10)||0;return b}var f=document.createElement("_").classList,f="undefined"!==typeof f&&
null!==String(f.add).match(/\[Native code\]/gi),e=/[\n\t\r]/g;CKEDITOR.tools.extend(CKEDITOR.dom.element.prototype,{type:CKEDITOR.NODE_ELEMENT,addClass:f?function(a){this.$.classList.add(a);return this}:function(b){var c=this.$.className;c&&(a(c,b)||(c+=" "+b));this.$.className=c||b;return this},removeClass:f?function(a){var b=this.$;b.classList.remove(a);b.className||b.removeAttribute("class");return this}:function(b){var c=this.getAttribute("class");c&&a(c,b)&&((c=c.replace(new RegExp("(?:^|\\s+)"+
b+"(?\x3d\\s|$)"),"").replace(/^\s+/,""))?this.setAttribute("class",c):this.removeAttribute("class"));return this},hasClass:function(b){return a(this.$.className,b)},append:function(a,b){"string"==typeof a&&(a=this.getDocument().createElement(a));b?this.$.insertBefore(a.$,this.$.firstChild):this.$.appendChild(a.$);return a},appendHtml:function(a){if(this.$.childNodes.length){var b=new CKEDITOR.dom.element("div",this.getDocument());b.setHtml(a);b.moveChildren(this)}else this.setHtml(a)},appendText:function(a){null!=
this.$.text&&CKEDITOR.env.ie&&9>CKEDITOR.env.version?this.$.text+=a:this.append(new CKEDITOR.dom.text(a))},appendBogus:function(a){if(a||CKEDITOR.env.needsBrFiller){for(a=this.getLast();a&&a.type==CKEDITOR.NODE_TEXT&&!CKEDITOR.tools.rtrim(a.getText());)a=a.getPrevious();a&&a.is&&a.is("br")||(a=this.getDocument().createElement("br"),CKEDITOR.env.gecko&&a.setAttribute("type","_moz"),this.append(a))}},breakParent:function(a,b){var c=new CKEDITOR.dom.range(this.getDocument());c.setStartAfter(this);c.setEndAfter(a);
var g=c.extractContents(!1,b||!1),d;c.insertNode(this.remove());if(CKEDITOR.env.ie&&!CKEDITOR.env.edge){for(c=new CKEDITOR.dom.element("div");d=g.getFirst();)d.$.style.backgroundColor&&(d.$.style.backgroundColor=d.$.style.backgroundColor),c.append(d);c.insertAfter(this);c.remove(!0)}else g.insertAfterNode(this)},contains:document.compareDocumentPosition?function(a){return!!(this.$.compareDocumentPosition(a.$)&16)}:function(a){var b=this.$;return a.type!=CKEDITOR.NODE_ELEMENT?b.contains(a.getParent().$):
b!=a.$&&b.contains(a.$)},focus:function(){function a(){try{this.$.focus()}catch(b){}}return function(b){b?CKEDITOR.tools.setTimeout(a,100,this):a.call(this)}}(),getHtml:function(){var a=this.$.innerHTML;return CKEDITOR.env.ie?a.replace(/<\?[^>]*>/g,""):a},getOuterHtml:function(){if(this.$.outerHTML)return this.$.outerHTML.replace(/<\?[^>]*>/,"");var a=this.$.ownerDocument.createElement("div");a.appendChild(this.$.cloneNode(!0));return a.innerHTML},getClientRect:function(a){var b=CKEDITOR.tools.extend({},
this.$.getBoundingClientRect());!b.width&&(b.width=b.right-b.left);!b.height&&(b.height=b.bottom-b.top);return a?CKEDITOR.tools.getAbsoluteRectPosition(this.getWindow(),b):b},setHtml:CKEDITOR.env.ie&&9>CKEDITOR.env.version?function(a){try{var b=this.$;if(this.getParent())return b.innerHTML=a;var c=this.getDocument()._getHtml5ShivFrag();c.appendChild(b);b.innerHTML=a;c.removeChild(b);return a}catch(g){this.$.innerHTML="";b=new CKEDITOR.dom.element("body",this.getDocument());b.$.innerHTML=a;for(b=b.getChildren();b.count();)this.append(b.getItem(0));
return a}}:function(a){return this.$.innerHTML=a},setText:function(){var a=document.createElement("p");a.innerHTML="x";a=a.textContent;return function(b){this.$[a?"textContent":"innerText"]=b}}(),getAttribute:function(){var a=function(a){return this.$.getAttribute(a,2)};return CKEDITOR.env.ie&&(CKEDITOR.env.ie7Compat||CKEDITOR.env.quirks)?function(a){switch(a){case "class":a="className";break;case "http-equiv":a="httpEquiv";break;case "name":return this.$.name;case "tabindex":return a=this.$.getAttribute(a,
2),0!==a&&0===this.$.tabIndex&&(a=null),a;case "checked":return a=this.$.attributes.getNamedItem(a),(a.specified?a.nodeValue:this.$.checked)?"checked":null;case "hspace":case "value":return this.$[a];case "style":return this.$.style.cssText;case "contenteditable":case "contentEditable":return this.$.attributes.getNamedItem("contentEditable").specified?this.$.getAttribute("contentEditable"):null}return this.$.getAttribute(a,2)}:a}(),getAttributes:function(a){var b={},c=this.$.attributes,g;a=CKEDITOR.tools.isArray(a)?
a:[];for(g=0;g<c.length;g++)-1===CKEDITOR.tools.indexOf(a,c[g].name)&&(b[c[g].name]=c[g].value);return b},getChildren:function(){return new CKEDITOR.dom.nodeList(this.$.childNodes)},getClientSize:function(){return{width:this.$.clientWidth,height:this.$.clientHeight}},getComputedStyle:document.defaultView&&document.defaultView.getComputedStyle?function(a){var b=this.getWindow().$.getComputedStyle(this.$,null);return b?b.getPropertyValue(a):""}:function(a){return this.$.currentStyle[CKEDITOR.tools.cssStyleToDomStyle(a)]},
getDtd:function(){var a=CKEDITOR.dtd[this.getName()];this.getDtd=function(){return a};return a},getElementsByTag:CKEDITOR.dom.document.prototype.getElementsByTag,getTabIndex:function(){var a=this.$.tabIndex;return 0!==a||CKEDITOR.dtd.$tabIndex[this.getName()]||0===parseInt(this.getAttribute("tabindex"),10)?a:-1},getText:function(){return this.$.textContent||this.$.innerText||""},getWindow:function(){return this.getDocument().getWindow()},getId:function(){return this.$.id||null},getNameAtt:function(){return this.$.name||
null},getName:function(){var a=this.$.nodeName.toLowerCase();if(CKEDITOR.env.ie&&8>=document.documentMode){var b=this.$.scopeName;"HTML"!=b&&(a=b.toLowerCase()+":"+a)}this.getName=function(){return a};return this.getName()},getValue:function(){return this.$.value},getFirst:function(a){var b=this.$.firstChild;(b=b&&new CKEDITOR.dom.node(b))&&a&&!a(b)&&(b=b.getNext(a));return b},getLast:function(a){var b=this.$.lastChild;(b=b&&new CKEDITOR.dom.node(b))&&a&&!a(b)&&(b=b.getPrevious(a));return b},getStyle:function(a){return this.$.style[CKEDITOR.tools.cssStyleToDomStyle(a)]},
is:function(){var a=this.getName();if("object"==typeof arguments[0])return!!arguments[0][a];for(var b=0;b<arguments.length;b++)if(arguments[b]==a)return!0;return!1},isEditable:function(a){var b=this.getName();return this.isReadOnly()||"none"==this.getComputedStyle("display")||"hidden"==this.getComputedStyle("visibility")||CKEDITOR.dtd.$nonEditable[b]||CKEDITOR.dtd.$empty[b]||this.is("a")&&(this.data("cke-saved-name")||this.hasAttribute("name"))&&!this.getChildCount()?!1:!1!==a?(a=CKEDITOR.dtd[b]||
CKEDITOR.dtd.span,!(!a||!a["#"])):!0},isIdentical:function(a){var b=this.clone(0,1);a=a.clone(0,1);b.removeAttributes(["_moz_dirty","data-cke-expando","data-cke-saved-href","data-cke-saved-name"]);a.removeAttributes(["_moz_dirty","data-cke-expando","data-cke-saved-href","data-cke-saved-name"]);if(b.$.isEqualNode)return b.$.style.cssText=CKEDITOR.tools.normalizeCssText(b.$.style.cssText),a.$.style.cssText=CKEDITOR.tools.normalizeCssText(a.$.style.cssText),b.$.isEqualNode(a.$);b=b.getOuterHtml();a=
a.getOuterHtml();if(CKEDITOR.env.ie&&9>CKEDITOR.env.version&&this.is("a")){var c=this.getParent();c.type==CKEDITOR.NODE_ELEMENT&&(c=c.clone(),c.setHtml(b),b=c.getHtml(),c.setHtml(a),a=c.getHtml())}return b==a},isVisible:function(){var a=(this.$.offsetHeight||this.$.offsetWidth)&&"hidden"!=this.getComputedStyle("visibility"),b,c;a&&CKEDITOR.env.webkit&&(b=this.getWindow(),!b.equals(CKEDITOR.document.getWindow())&&(c=b.$.frameElement)&&(a=(new CKEDITOR.dom.element(c)).isVisible()));return!!a},isEmptyInlineRemoveable:function(){if(!CKEDITOR.dtd.$removeEmpty[this.getName()])return!1;
for(var a=this.getChildren(),b=0,c=a.count();b<c;b++){var g=a.getItem(b);if(g.type!=CKEDITOR.NODE_ELEMENT||!g.data("cke-bookmark"))if(g.type==CKEDITOR.NODE_ELEMENT&&!g.isEmptyInlineRemoveable()||g.type==CKEDITOR.NODE_TEXT&&CKEDITOR.tools.trim(g.getText()))return!1}return!0},hasAttributes:CKEDITOR.env.ie&&(CKEDITOR.env.ie7Compat||CKEDITOR.env.quirks)?function(){for(var a=this.$.attributes,b=0;b<a.length;b++){var c=a[b];switch(c.nodeName){case "class":if(this.getAttribute("class"))return!0;case "data-cke-expando":continue;
default:if(c.specified)return!0}}return!1}:function(){var a=this.$.attributes,b=a.length,c={"data-cke-expando":1,_moz_dirty:1};return 0<b&&(2<b||!c[a[0].nodeName]||2==b&&!c[a[1].nodeName])},hasAttribute:function(){function a(b){var c=this.$.attributes.getNamedItem(b);if("input"==this.getName())switch(b){case "class":return 0<this.$.className.length;case "checked":return!!this.$.checked;case "value":return b=this.getAttribute("type"),"checkbox"==b||"radio"==b?"on"!=this.$.value:!!this.$.value}return c?
c.specified:!1}return CKEDITOR.env.ie?8>CKEDITOR.env.version?function(b){return"name"==b?!!this.$.name:a.call(this,b)}:a:function(a){return!!this.$.attributes.getNamedItem(a)}}(),hide:function(){this.setStyle("display","none")},moveChildren:function(a,b){var c=this.$;a=a.$;if(c!=a){var g;if(b)for(;g=c.lastChild;)a.insertBefore(c.removeChild(g),a.firstChild);else for(;g=c.firstChild;)a.appendChild(c.removeChild(g))}},mergeSiblings:function(){function a(b,c,g){if(c&&c.type==CKEDITOR.NODE_ELEMENT){for(var d=
[];c.data("cke-bookmark")||c.isEmptyInlineRemoveable();)if(d.push(c),c=g?c.getNext():c.getPrevious(),!c||c.type!=CKEDITOR.NODE_ELEMENT)return;if(b.isIdentical(c)){for(var e=g?b.getLast():b.getFirst();d.length;)d.shift().move(b,!g);c.moveChildren(b,!g);c.remove();e&&e.type==CKEDITOR.NODE_ELEMENT&&e.mergeSiblings()}}}return function(b){if(!1===b||CKEDITOR.dtd.$removeEmpty[this.getName()]||this.is("a"))a(this,this.getNext(),!0),a(this,this.getPrevious())}}(),show:function(){this.setStyles({display:"",
visibility:""})},setAttribute:function(){var a=function(a,b){this.$.setAttribute(a,b);return this};return CKEDITOR.env.ie&&(CKEDITOR.env.ie7Compat||CKEDITOR.env.quirks)?function(b,c){"class"==b?this.$.className=c:"style"==b?this.$.style.cssText=c:"tabindex"==b?this.$.tabIndex=c:"checked"==b?this.$.checked=c:"contenteditable"==b?a.call(this,"contentEditable",c):a.apply(this,arguments);return this}:CKEDITOR.env.ie8Compat&&CKEDITOR.env.secure?function(b,c){if("src"==b&&c.match(/^http:\/\//))try{a.apply(this,
arguments)}catch(g){}else a.apply(this,arguments);return this}:a}(),setAttributes:function(a){for(var b in a)this.setAttribute(b,a[b]);return this},setValue:function(a){this.$.value=a;return this},removeAttribute:function(){var a=function(a){this.$.removeAttribute(a)};return CKEDITOR.env.ie&&(CKEDITOR.env.ie7Compat||CKEDITOR.env.quirks)?function(a){"class"==a?a="className":"tabindex"==a?a="tabIndex":"contenteditable"==a&&(a="contentEditable");this.$.removeAttribute(a)}:a}(),removeAttributes:function(a){if(CKEDITOR.tools.isArray(a))for(var b=
0;b<a.length;b++)this.removeAttribute(a[b]);else for(b in a=a||this.getAttributes(),a)a.hasOwnProperty(b)&&this.removeAttribute(b)},removeStyle:function(a){var b=this.$.style;if(b.removeProperty||"border"!=a&&"margin"!=a&&"padding"!=a)b.removeProperty?b.removeProperty(a):b.removeAttribute(CKEDITOR.tools.cssStyleToDomStyle(a)),this.$.style.cssText||this.removeAttribute("style");else{var c=["top","left","right","bottom"],g;"border"==a&&(g=["color","style","width"]);for(var b=[],d=0;d<c.length;d++)if(g)for(var e=
0;e<g.length;e++)b.push([a,c[d],g[e]].join("-"));else b.push([a,c[d]].join("-"));for(a=0;a<b.length;a++)this.removeStyle(b[a])}},setStyle:function(a,b){this.$.style[CKEDITOR.tools.cssStyleToDomStyle(a)]=b;return this},setStyles:function(a){for(var b in a)this.setStyle(b,a[b]);return this},setOpacity:function(a){CKEDITOR.env.ie&&9>CKEDITOR.env.version?(a=Math.round(100*a),this.setStyle("filter",100<=a?"":"progid:DXImageTransform.Microsoft.Alpha(opacity\x3d"+a+")")):this.setStyle("opacity",a)},unselectable:function(){this.setStyles(CKEDITOR.tools.cssVendorPrefix("user-select",
"none"));if(CKEDITOR.env.ie){this.setAttribute("unselectable","on");for(var a,b=this.getElementsByTag("*"),c=0,g=b.count();c<g;c++)a=b.getItem(c),a.setAttribute("unselectable","on")}},getPositionedAncestor:function(){for(var a=this;"html"!=a.getName();){if("static"!=a.getComputedStyle("position"))return a;a=a.getParent()}return null},getDocumentPosition:function(a){var b=0,c=0,g=this.getDocument(),d=g.getBody(),e="BackCompat"==g.$.compatMode;if(document.documentElement.getBoundingClientRect&&(CKEDITOR.env.ie?
8!==CKEDITOR.env.version:1)){var f=this.$.getBoundingClientRect(),k=g.$.documentElement,v=k.clientTop||d.$.clientTop||0,l=k.clientLeft||d.$.clientLeft||0,L=!0;CKEDITOR.env.ie&&(L=g.getDocumentElement().contains(this),g=g.getBody().contains(this),L=e&&g||!e&&L);L&&(CKEDITOR.env.webkit||CKEDITOR.env.ie&&12<=CKEDITOR.env.version?(b=d.$.scrollLeft||k.scrollLeft,c=d.$.scrollTop||k.scrollTop):(c=e?d.$:k,b=c.scrollLeft,c=c.scrollTop),b=f.left+b-l,c=f.top+c-v)}else for(v=this,l=null;v&&"body"!=v.getName()&&
"html"!=v.getName();){b+=v.$.offsetLeft-v.$.scrollLeft;c+=v.$.offsetTop-v.$.scrollTop;v.equals(this)||(b+=v.$.clientLeft||0,c+=v.$.clientTop||0);for(;l&&!l.equals(v);)b-=l.$.scrollLeft,c-=l.$.scrollTop,l=l.getParent();l=v;v=(f=v.$.offsetParent)?new CKEDITOR.dom.element(f):null}a&&(f=this.getWindow(),v=a.getWindow(),!f.equals(v)&&f.$.frameElement&&(a=(new CKEDITOR.dom.element(f.$.frameElement)).getDocumentPosition(a),b+=a.x,c+=a.y));document.documentElement.getBoundingClientRect||!CKEDITOR.env.gecko||
e||(b+=this.$.clientLeft?1:0,c+=this.$.clientTop?1:0);return{x:b,y:c}},scrollIntoView:function(a){var b=this.getParent();if(b){do if((b.$.clientWidth&&b.$.clientWidth<b.$.scrollWidth||b.$.clientHeight&&b.$.clientHeight<b.$.scrollHeight)&&!b.is("body")&&this.scrollIntoParent(b,a,1),b.is("html")){var c=b.getWindow();try{var g=c.$.frameElement;g&&(b=new CKEDITOR.dom.element(g))}catch(d){}}while(b=b.getParent())}},scrollIntoParent:function(a,b,c){var g,d,e,f;function k(b,c){/body|html/.test(a.getName())?
a.getWindow().$.scrollBy(b,c):(a.$.scrollLeft+=b,a.$.scrollTop+=c)}function v(a,b){var c={x:0,y:0};if(!a.is(L?"body":"html")){var g=a.$.getBoundingClientRect();c.x=g.left;c.y=g.top}g=a.getWindow();g.equals(b)||(g=v(CKEDITOR.dom.element.get(g.$.frameElement),b),c.x+=g.x,c.y+=g.y);return c}function l(a,b){return parseInt(a.getComputedStyle("margin-"+b)||0,10)||0}!a&&(a=this.getWindow());e=a.getDocument();var L="BackCompat"==e.$.compatMode;a instanceof CKEDITOR.dom.window&&(a=L?e.getBody():e.getDocumentElement());
CKEDITOR.env.webkit&&(e=this.getEditor(!1))&&(e._.previousScrollTop=null);e=a.getWindow();d=v(this,e);var w=v(a,e),J=this.$.offsetHeight;g=this.$.offsetWidth;var G=a.$.clientHeight,q=a.$.clientWidth;e=d.x-l(this,"left")-w.x||0;f=d.y-l(this,"top")-w.y||0;g=d.x+g+l(this,"right")-(w.x+q)||0;d=d.y+J+l(this,"bottom")-(w.y+G)||0;(0>f||0<d)&&k(0,!0===b?f:!1===b?d:0>f?f:d);c&&(0>e||0<g)&&k(0>e?e:g,0)},setState:function(a,b,c){b=b||"cke";switch(a){case CKEDITOR.TRISTATE_ON:this.addClass(b+"_on");this.removeClass(b+
"_off");this.removeClass(b+"_disabled");c&&this.setAttribute("aria-pressed",!0);c&&this.removeAttribute("aria-disabled");break;case CKEDITOR.TRISTATE_DISABLED:this.addClass(b+"_disabled");this.removeClass(b+"_off");this.removeClass(b+"_on");c&&this.setAttribute("aria-disabled",!0);c&&this.removeAttribute("aria-pressed");break;default:this.addClass(b+"_off"),this.removeClass(b+"_on"),this.removeClass(b+"_disabled"),c&&this.removeAttribute("aria-pressed"),c&&this.removeAttribute("aria-disabled")}},
getFrameDocument:function(){var a=this.$;try{a.contentWindow.document}catch(b){a.src=a.src}return a&&new CKEDITOR.dom.document(a.contentWindow.document)},copyAttributes:function(a,b){var c=this.$.attributes;b=b||{};for(var g=0;g<c.length;g++){var d=c[g],e=d.nodeName.toLowerCase(),f;if(!(e in b))if("checked"==e&&(f=this.getAttribute(e)))a.setAttribute(e,f);else if(!CKEDITOR.env.ie||this.hasAttribute(e))f=this.getAttribute(e),null===f&&(f=d.nodeValue),a.setAttribute(e,f)}""!==this.$.style.cssText&&
(a.$.style.cssText=this.$.style.cssText)},renameNode:function(a){if(this.getName()!=a){var b=this.getDocument();a=new CKEDITOR.dom.element(a,b);this.copyAttributes(a);this.moveChildren(a);this.getParent(!0)&&this.$.parentNode.replaceChild(a.$,this.$);a.$["data-cke-expando"]=this.$["data-cke-expando"];this.$=a.$;delete this.getName}},getChild:function(){function a(b,c){var g=b.childNodes;if(0<=c&&c<g.length)return g[c]}return function(b){var c=this.$;if(b.slice)for(b=b.slice();0<b.length&&c;)c=a(c,
b.shift());else c=a(c,b);return c?new CKEDITOR.dom.node(c):null}}(),getChildCount:function(){return this.$.childNodes.length},disableContextMenu:function(){function a(b){return b.type==CKEDITOR.NODE_ELEMENT&&b.hasClass("cke_enable_context_menu")}this.on("contextmenu",function(b){b.data.getTarget().getAscendant(a,!0)||b.data.preventDefault()})},getDirection:function(a){return a?this.getComputedStyle("direction")||this.getDirection()||this.getParent()&&this.getParent().getDirection(1)||this.getDocument().$.dir||
"ltr":this.getStyle("direction")||this.getAttribute("dir")},data:function(a,b){a="data-"+a;if(void 0===b)return this.getAttribute(a);!1===b?this.removeAttribute(a):this.setAttribute(a,b);return null},getEditor:function(a){var b=CKEDITOR.instances,c,g,d;a=a||void 0===a;for(c in b)if(g=b[c],g.element.equals(this)&&g.elementMode!=CKEDITOR.ELEMENT_MODE_APPENDTO||!a&&(d=g.editable())&&(d.equals(this)||d.contains(this)))return g;return null},find:function(a){var c=d(this);a=new CKEDITOR.dom.nodeList(this.$.querySelectorAll(b(this,
a)));c();return a},findOne:function(a){var c=d(this);a=this.$.querySelector(b(this,a));c();return a?new CKEDITOR.dom.element(a):null},forEach:function(a,b,c){if(!(c||b&&this.type!=b))var g=a(this);if(!1!==g){c=this.getChildren();for(var d=0;d<c.count();d++)g=c.getItem(d),g.type==CKEDITOR.NODE_ELEMENT?g.forEach(a,b):b&&g.type!=b||a(g)}},fireEventHandler:function(a,b){var c="on"+a,g=this.$;if(CKEDITOR.env.ie&&9>CKEDITOR.env.version){var d=g.ownerDocument.createEventObject(),e;for(e in b)d[e]=b[e];g.fireEvent(c,
d)}else g[g[a]?a:c](b)},isDetached:function(){var a=this.getDocument(),b=a.getDocumentElement();return b.equals(this)||b.contains(this)?!CKEDITOR.env.ie||8<CKEDITOR.env.version&&!CKEDITOR.env.quirks?!a.$.defaultView:!1:!0}});var k={width:["border-left-width","border-right-width","padding-left","padding-right"],height:["border-top-width","border-bottom-width","padding-top","padding-bottom"]};CKEDITOR.dom.element.prototype.setSize=function(a,b,d){"number"==typeof b&&(!d||CKEDITOR.env.ie&&CKEDITOR.env.quirks||
(b-=c.call(this,a)),this.setStyle(a,b+"px"))};CKEDITOR.dom.element.prototype.getSize=function(a,b){var d=Math.max(this.$["offset"+CKEDITOR.tools.capitalize(a)],this.$["client"+CKEDITOR.tools.capitalize(a)])||0;b&&(d-=c.call(this,a));return d}})();CKEDITOR.dom.documentFragment=function(a){a=a||CKEDITOR.document;this.$=a.type==CKEDITOR.NODE_DOCUMENT?a.$.createDocumentFragment():a};
CKEDITOR.tools.extend(CKEDITOR.dom.documentFragment.prototype,CKEDITOR.dom.element.prototype,{type:CKEDITOR.NODE_DOCUMENT_FRAGMENT,insertAfterNode:function(a){a=a.$;a.parentNode.insertBefore(this.$,a.nextSibling)},getHtml:function(){var a=new CKEDITOR.dom.element("div");this.clone(1,1).appendTo(a);return a.getHtml().replace(/\s*data-cke-expando=".*?"/g,"")}},!0,{append:1,appendBogus:1,clone:1,getFirst:1,getHtml:1,getLast:1,getParent:1,getNext:1,getPrevious:1,appendTo:1,moveChildren:1,insertBefore:1,
insertAfterNode:1,replace:1,trim:1,type:1,ltrim:1,rtrim:1,getDocument:1,getChildCount:1,getChild:1,getChildren:1});CKEDITOR.tools.extend(CKEDITOR.dom.documentFragment.prototype,CKEDITOR.dom.document.prototype,!0,{find:1,findOne:1});
(function(){function a(a,b){var c=this.range;if(this._.end)return null;if(!this._.start){this._.start=1;if(c.collapsed)return this.end(),null;c.optimize()}var g,d=c.startContainer;g=c.endContainer;var e=c.startOffset,f=c.endOffset,x,h=this.guard,k=this.type,m=a?"getPreviousSourceNode":"getNextSourceNode";if(!a&&!this._.guardLTR){var p=g.type==CKEDITOR.NODE_ELEMENT?g:g.getParent(),E=g.type==CKEDITOR.NODE_ELEMENT?g.getChild(f):g.getNext();this._.guardLTR=function(a,b){return(!b||!p.equals(a))&&(!E||
!a.equals(E))&&(a.type!=CKEDITOR.NODE_ELEMENT||!b||!a.equals(c.root))}}if(a&&!this._.guardRTL){var C=d.type==CKEDITOR.NODE_ELEMENT?d:d.getParent(),t=d.type==CKEDITOR.NODE_ELEMENT?e?d.getChild(e-1):null:d.getPrevious();this._.guardRTL=function(a,b){return(!b||!C.equals(a))&&(!t||!a.equals(t))&&(a.type!=CKEDITOR.NODE_ELEMENT||!b||!a.equals(c.root))}}var B=a?this._.guardRTL:this._.guardLTR;x=h?function(a,b){return!1===B(a,b)?!1:h(a,b)}:B;this.current?g=this.current[m](!1,k,x):(a?g.type==CKEDITOR.NODE_ELEMENT&&
(g=0<f?g.getChild(f-1):!1===x(g,!0)?null:g.getPreviousSourceNode(!0,k,x)):(g=d,g.type==CKEDITOR.NODE_ELEMENT&&((g=g.getChild(e))||(g=!1===x(d,!0)?null:d.getNextSourceNode(!0,k,x)))),g&&!1===x(g)&&(g=null));for(;g&&!this._.end;){this.current=g;if(!this.evaluator||!1!==this.evaluator(g)){if(!b)return g}else if(b&&this.evaluator)return!1;g=g[m](!1,k,x)}this.end();return this.current=null}function d(b){for(var c,g=null;c=a.call(this,b);)g=c;return g}CKEDITOR.dom.walker=CKEDITOR.tools.createClass({$:function(a){this.range=
a;this._={}},proto:{end:function(){this._.end=1},next:function(){return a.call(this)},previous:function(){return a.call(this,1)},checkForward:function(){return!1!==a.call(this,0,1)},checkBackward:function(){return!1!==a.call(this,1,1)},lastForward:function(){return d.call(this)},lastBackward:function(){return d.call(this,1)},reset:function(){delete this.current;this._={}}}});var b={block:1,"list-item":1,table:1,"table-row-group":1,"table-header-group":1,"table-footer-group":1,"table-row":1,"table-column-group":1,
"table-column":1,"table-cell":1,"table-caption":1},c={absolute:1,fixed:1};CKEDITOR.dom.element.prototype.isBlockBoundary=function(a){return"none"!=this.getComputedStyle("float")||this.getComputedStyle("position")in c||!b[this.getComputedStyle("display")]?!!(this.is(CKEDITOR.dtd.$block)||a&&this.is(a)):!0};CKEDITOR.dom.walker.blockBoundary=function(a){return function(b){return!(b.type==CKEDITOR.NODE_ELEMENT&&b.isBlockBoundary(a))}};CKEDITOR.dom.walker.listItemBoundary=function(){return this.blockBoundary({br:1})};
CKEDITOR.dom.walker.bookmark=function(a,b){function c(a){return a&&a.getName&&"span"==a.getName()&&a.data("cke-bookmark")}return function(g){var d,e;d=g&&g.type!=CKEDITOR.NODE_ELEMENT&&(e=g.getParent())&&c(e);d=a?d:d||c(g);return!!(b^d)}};CKEDITOR.dom.walker.whitespaces=function(a){return function(b){var c;b&&b.type==CKEDITOR.NODE_TEXT&&(c=!CKEDITOR.tools.trim(b.getText())||CKEDITOR.env.webkit&&b.getText()==CKEDITOR.dom.selection.FILLING_CHAR_SEQUENCE);return!!(a^c)}};CKEDITOR.dom.walker.invisible=
function(a){var b=CKEDITOR.dom.walker.whitespaces(),c=CKEDITOR.env.webkit?1:0;return function(g){b(g)?g=1:(g.type==CKEDITOR.NODE_TEXT&&(g=g.getParent()),g=g.$.offsetWidth<=c);return!!(a^g)}};CKEDITOR.dom.walker.nodeType=function(a,b){return function(c){return!!(b^c.type==a)}};CKEDITOR.dom.walker.bogus=function(a){function b(a){return!e(a)&&!k(a)}return function(c){var g=CKEDITOR.env.needsBrFiller?c.is&&c.is("br"):c.getText&&f.test(c.getText());g&&(g=c.getParent(),c=c.getNext(b),g=g.isBlockBoundary()&&
(!c||c.type==CKEDITOR.NODE_ELEMENT&&c.isBlockBoundary()));return!!(a^g)}};CKEDITOR.dom.walker.temp=function(a){return function(b){b.type!=CKEDITOR.NODE_ELEMENT&&(b=b.getParent());b=b&&b.hasAttribute("data-cke-temp");return!!(a^b)}};var f=/^[\t\r\n ]*(?:&nbsp;|\xa0)$/,e=CKEDITOR.dom.walker.whitespaces(),k=CKEDITOR.dom.walker.bookmark(),h=CKEDITOR.dom.walker.temp(),p=function(a){return k(a)||e(a)||a.type==CKEDITOR.NODE_ELEMENT&&a.is(CKEDITOR.dtd.$inline)&&!a.is(CKEDITOR.dtd.$empty)};CKEDITOR.dom.walker.ignored=
function(a){return function(b){b=e(b)||k(b)||h(b);return!!(a^b)}};var m=CKEDITOR.dom.walker.ignored();CKEDITOR.dom.walker.empty=function(a){return function(b){for(var c=0,g=b.getChildCount();c<g;++c)if(!m(b.getChild(c)))return!!a;return!a}};var g=CKEDITOR.dom.walker.empty(),x=CKEDITOR.dom.walker.validEmptyBlockContainers=CKEDITOR.tools.extend(function(a){var b={},c;for(c in a)CKEDITOR.dtd[c]["#"]&&(b[c]=1);return b}(CKEDITOR.dtd.$block),{caption:1,td:1,th:1});CKEDITOR.dom.walker.editable=function(a){return function(b){b=
m(b)?!1:b.type==CKEDITOR.NODE_TEXT||b.type==CKEDITOR.NODE_ELEMENT&&(b.is(CKEDITOR.dtd.$inline)||b.is("hr")||"false"==b.getAttribute("contenteditable")||!CKEDITOR.env.needsBrFiller&&b.is(x)&&g(b))?!0:!1;return!!(a^b)}};CKEDITOR.dom.element.prototype.getBogus=function(){var a=this;do a=a.getPreviousSourceNode();while(p(a));return a&&(CKEDITOR.env.needsBrFiller?a.is&&a.is("br"):a.getText&&f.test(a.getText()))?a:!1}})();"use strict";
CKEDITOR.dom.range=function(a){this.endOffset=this.endContainer=this.startOffset=this.startContainer=null;this.collapsed=!0;var d=a instanceof CKEDITOR.dom.document;this.document=d?a:a.getDocument();this.root=d?a.getBody():a};
(function(){function a(a){a.collapsed=a.startContainer&&a.endContainer&&a.startContainer.equals(a.endContainer)&&a.startOffset==a.endOffset}function d(a,b,c,d,e){function f(a,b,c,g){var R=c?a.getPrevious():a.getNext();if(g&&k)return R;p||g?b.append(a.clone(!0,e),c):(a.remove(),m&&b.append(a,c));return R}function l(){var a,b,c,g=Math.min(O.length,r.length);for(a=0;a<g;a++)if(b=O[a],c=r[a],!b.equals(c))return a;return a-1}function h(){var b=I-1,c=B&&z&&!q.equals(u);b<M-1||b<n-1||c?(c?a.moveToPosition(u,
CKEDITOR.POSITION_BEFORE_START):n==b+1&&t?a.moveToPosition(r[b],CKEDITOR.POSITION_BEFORE_END):a.moveToPosition(r[b+1],CKEDITOR.POSITION_BEFORE_START),d&&(b=O[b+1])&&b.type==CKEDITOR.NODE_ELEMENT&&(c=CKEDITOR.dom.element.createFromHtml('\x3cspan data-cke-bookmark\x3d"1" style\x3d"display:none"\x3e\x26nbsp;\x3c/span\x3e',a.document),c.insertAfter(b),b.mergeSiblings(!1),a.moveToBookmark({startNode:c}))):a.collapse(!0)}a.optimizeBookmark();var k=0===b,m=1==b,p=2==b;b=p||m;var q=a.startContainer,u=a.endContainer,
K=a.startOffset,E=a.endOffset,C,t,B,z,H,N;if(p&&u.type==CKEDITOR.NODE_TEXT&&(q.equals(u)||q.type===CKEDITOR.NODE_ELEMENT&&q.getFirst().equals(u)))c.append(a.document.createText(u.substring(K,E)));else{u.type==CKEDITOR.NODE_TEXT?p?N=!0:u=u.split(E):0<u.getChildCount()?E>=u.getChildCount()?(u=u.getChild(E-1),t=!0):u=u.getChild(E):z=t=!0;q.type==CKEDITOR.NODE_TEXT?p?H=!0:q.split(K):0<q.getChildCount()?0===K?(q=q.getChild(K),C=!0):q=q.getChild(K-1):B=C=!0;for(var O=q.getParents(),r=u.getParents(),I=l(),
M=O.length-1,n=r.length-1,D=c,R,aa,X,ea=-1,V=I;V<=M;V++){aa=O[V];X=aa.getNext();for(V!=M||aa.equals(r[V])&&M<n?b&&(R=D.append(aa.clone(0,e))):C?f(aa,D,!1,B):H&&D.append(a.document.createText(aa.substring(K)));X;){if(X.equals(r[V])){ea=V;break}X=f(X,D)}D=R}D=c;for(V=I;V<=n;V++)if(c=r[V],X=c.getPrevious(),c.equals(O[V]))b&&(D=D.getChild(0));else{V!=n||c.equals(O[V])&&n<M?b&&(R=D.append(c.clone(0,e))):t?f(c,D,!1,z):N&&D.append(a.document.createText(c.substring(0,E)));if(V>ea)for(;X;)X=f(X,D,!0);D=R}p||
h()}}function b(){var a=!1,b=CKEDITOR.dom.walker.whitespaces(),c=CKEDITOR.dom.walker.bookmark(!0),d=CKEDITOR.dom.walker.bogus();return function(f){return c(f)||b(f)?!0:d(f)&&!a?a=!0:f.type==CKEDITOR.NODE_TEXT&&(f.hasAscendant("pre")||CKEDITOR.tools.trim(f.getText()).length)||f.type==CKEDITOR.NODE_ELEMENT&&!f.is(e)?!1:!0}}function c(a){var b=CKEDITOR.dom.walker.whitespaces(),c=CKEDITOR.dom.walker.bookmark(1);return function(d){return c(d)||b(d)?!0:!a&&k(d)||d.type==CKEDITOR.NODE_ELEMENT&&d.is(CKEDITOR.dtd.$removeEmpty)}}
function f(a){return function(){var b;return this[a?"getPreviousNode":"getNextNode"](function(a){!b&&m(a)&&(b=a);return p(a)&&!(k(a)&&a.equals(b))})}}var e={abbr:1,acronym:1,b:1,bdo:1,big:1,cite:1,code:1,del:1,dfn:1,em:1,font:1,i:1,ins:1,label:1,kbd:1,q:1,samp:1,small:1,span:1,strike:1,strong:1,sub:1,sup:1,tt:1,u:1,"var":1},k=CKEDITOR.dom.walker.bogus(),h=/^[\t\r\n ]*(?:&nbsp;|\xa0)$/,p=CKEDITOR.dom.walker.editable(),m=CKEDITOR.dom.walker.ignored(!0);CKEDITOR.dom.range.prototype={clone:function(){var a=
new CKEDITOR.dom.range(this.root);a._setStartContainer(this.startContainer);a.startOffset=this.startOffset;a._setEndContainer(this.endContainer);a.endOffset=this.endOffset;a.collapsed=this.collapsed;return a},collapse:function(a){a?(this._setEndContainer(this.startContainer),this.endOffset=this.startOffset):(this._setStartContainer(this.endContainer),this.startOffset=this.endOffset);this.collapsed=!0},cloneContents:function(a){var b=new CKEDITOR.dom.documentFragment(this.document);this.collapsed||
d(this,2,b,!1,"undefined"==typeof a?!0:a);return b},deleteContents:function(a){this.collapsed||d(this,0,null,a)},extractContents:function(a,b){var c=new CKEDITOR.dom.documentFragment(this.document);this.collapsed||d(this,1,c,a,"undefined"==typeof b?!0:b);return c},equals:function(a){return this.startOffset===a.startOffset&&this.endOffset===a.endOffset&&this.startContainer.equals(a.startContainer)&&this.endContainer.equals(a.endContainer)},createBookmark:function(a){function b(a){return a.getAscendant(function(a){var b;
if(b=a.data&&a.data("cke-temp"))b=-1===CKEDITOR.tools.array.indexOf(["cke_copybin","cke_pastebin"],a.getAttribute("id"));return b},!0)}var c=this.startContainer,d=this.endContainer,e=this.collapsed,f,l,h,k;f=this.document.createElement("span");f.data("cke-bookmark",1);f.setStyle("display","none");f.setHtml("\x26nbsp;");a&&(h="cke_bm_"+CKEDITOR.tools.getNextNumber(),f.setAttribute("id",h+(e?"C":"S")));e||(l=f.clone(),l.setHtml("\x26nbsp;"),a&&l.setAttribute("id",h+"E"),k=this.clone(),b(d)&&(d=b(d),
k.moveToPosition(d,CKEDITOR.POSITION_AFTER_END)),k.collapse(),k.insertNode(l));k=this.clone();b(c)&&(d=b(c),k.moveToPosition(d,CKEDITOR.POSITION_BEFORE_START));k.collapse(!0);k.insertNode(f);l?(this.setStartAfter(f),this.setEndBefore(l)):this.moveToPosition(f,CKEDITOR.POSITION_AFTER_END);return{startNode:a?h+(e?"C":"S"):f,endNode:a?h+"E":l,serializable:a,collapsed:e}},createBookmark2:function(){function a(b){var g=b.container,d=b.offset,e;e=g;var f=d;e=e.type!=CKEDITOR.NODE_ELEMENT||0===f||f==e.getChildCount()?
0:e.getChild(f-1).type==CKEDITOR.NODE_TEXT&&e.getChild(f).type==CKEDITOR.NODE_TEXT;e&&(g=g.getChild(d-1),d=g.getLength());if(g.type==CKEDITOR.NODE_ELEMENT&&0<d){a:{for(e=g;d--;)if(f=e.getChild(d).getIndex(!0),0<=f){d=f;break a}d=-1}d+=1}if(g.type==CKEDITOR.NODE_TEXT){e=g;for(f=0;(e=e.getPrevious())&&e.type==CKEDITOR.NODE_TEXT;)f+=e.getText().replace(CKEDITOR.dom.selection.FILLING_CHAR_SEQUENCE,"").length;e=f;g.isEmpty()?(f=g.getPrevious(c),e?(d=e,g=f?f.getNext():g.getParent().getFirst()):(g=g.getParent(),
d=f?f.getIndex(!0)+1:0)):d+=e}b.container=g;b.offset=d}function b(a,c){var g=c.getCustomData("cke-fillingChar");if(g){var d=a.container;g.equals(d)&&(a.offset-=CKEDITOR.dom.selection.FILLING_CHAR_SEQUENCE.length,0>=a.offset&&(a.offset=d.getIndex(),a.container=d.getParent()))}}var c=CKEDITOR.dom.walker.nodeType(CKEDITOR.NODE_TEXT,!0);return function(c){var d=this.collapsed,e={container:this.startContainer,offset:this.startOffset},l={container:this.endContainer,offset:this.endOffset};c&&(a(e),b(e,this.root),
d||(a(l),b(l,this.root)));return{start:e.container.getAddress(c),end:d?null:l.container.getAddress(c),startOffset:e.offset,endOffset:l.offset,normalized:c,collapsed:d,is2:!0}}}(),moveToBookmark:function(a){if(a.is2){var b=this.document.getByAddress(a.start,a.normalized),c=a.startOffset,d=a.end&&this.document.getByAddress(a.end,a.normalized);a=a.endOffset;this.setStart(b,c);d?this.setEnd(d,a):this.collapse(!0)}else b=(c=a.serializable)?this.document.getById(a.startNode):a.startNode,a=c?this.document.getById(a.endNode):
a.endNode,this.setStartBefore(b),b.remove(),a?(this.setEndBefore(a),a.remove()):this.collapse(!0)},getBoundaryNodes:function(){var a=this.startContainer,b=this.endContainer,c=this.startOffset,d=this.endOffset,e;if(a.type==CKEDITOR.NODE_ELEMENT)if(e=a.getChildCount(),e>c)a=a.getChild(c);else if(1>e)a=a.getPreviousSourceNode();else{for(a=a.$;a.lastChild;)a=a.lastChild;a=new CKEDITOR.dom.node(a);a=a.getNextSourceNode()||a}if(b.type==CKEDITOR.NODE_ELEMENT)if(e=b.getChildCount(),e>d)b=b.getChild(d).getPreviousSourceNode(!0);
else if(1>e)b=b.getPreviousSourceNode();else{for(b=b.$;b.lastChild;)b=b.lastChild;b=new CKEDITOR.dom.node(b)}a.getPosition(b)&CKEDITOR.POSITION_FOLLOWING&&(a=b);return{startNode:a,endNode:b}},getCommonAncestor:function(a,b){var c=this.startContainer,d=this.endContainer,c=c.equals(d)?a&&c.type==CKEDITOR.NODE_ELEMENT&&this.startOffset==this.endOffset-1?c.getChild(this.startOffset):c:c.getCommonAncestor(d);return b&&!c.is?c.getParent():c},optimize:function(){var a=this.startContainer,b=this.startOffset;
a.type!=CKEDITOR.NODE_ELEMENT&&(b?b>=a.getLength()&&this.setStartAfter(a):this.setStartBefore(a));a=this.endContainer;b=this.endOffset;a.type!=CKEDITOR.NODE_ELEMENT&&(b?b>=a.getLength()&&this.setEndAfter(a):this.setEndBefore(a))},optimizeBookmark:function(){var a=this.startContainer,b=this.endContainer;a.is&&a.is("span")&&a.data("cke-bookmark")&&this.setStartAt(a,CKEDITOR.POSITION_BEFORE_START);b&&b.is&&b.is("span")&&b.data("cke-bookmark")&&this.setEndAt(b,CKEDITOR.POSITION_AFTER_END)},trim:function(a,
b){var c=this.startContainer,d=this.startOffset,e=this.collapsed;if((!a||e)&&c&&c.type==CKEDITOR.NODE_TEXT){if(d)if(d>=c.getLength())d=c.getIndex()+1,c=c.getParent();else{var f=c.split(d),d=c.getIndex()+1,c=c.getParent();this.startContainer.equals(this.endContainer)?this.setEnd(f,this.endOffset-this.startOffset):c.equals(this.endContainer)&&(this.endOffset+=1)}else d=c.getIndex(),c=c.getParent();this.setStart(c,d);if(e){this.collapse(!0);return}}c=this.endContainer;d=this.endOffset;b||e||!c||c.type!=
CKEDITOR.NODE_TEXT||(d?(d>=c.getLength()||c.split(d),d=c.getIndex()+1):d=c.getIndex(),c=c.getParent(),this.setEnd(c,d))},enlarge:function(a,b){function c(a){return a&&a.type==CKEDITOR.NODE_ELEMENT&&a.hasAttribute("contenteditable")?null:a}function d(a,b,c){var g=new CKEDITOR.dom.range(c);g.setStart(a,b);g.setEndAt(c,CKEDITOR.POSITION_BEFORE_END);c=new CKEDITOR.dom.walker(g);for(c.guard=function(a){return!(a.type==CKEDITOR.NODE_ELEMENT&&a.isBlockBoundary())};g=c.next();){if(g.type!=CKEDITOR.NODE_TEXT)return!1;
f=g!=a?g.getText():g.substring(b);if(e.test(f))return!1}return!0}var e=new RegExp(/[^\s\ufeff]/),f,l;switch(a){case CKEDITOR.ENLARGE_INLINE:var h=1;case CKEDITOR.ENLARGE_ELEMENT:if(this.collapsed)break;var k=this.getCommonAncestor();l=this.root;var m,p,q,u,K,E=!1,C;C=this.startContainer;var t=this.startOffset;C.type==CKEDITOR.NODE_TEXT?(t&&(C=!CKEDITOR.tools.trim(C.substring(0,t)).length&&C,E=!!C),C&&((u=C.getPrevious())||(q=C.getParent()))):(t&&(u=C.getChild(t-1)||C.getLast()),u||(q=C));for(q=c(q);q||
u;){if(q&&!u){!K&&q.equals(k)&&(K=!0);if(h?q.isBlockBoundary():!l.contains(q))break;E&&"inline"==q.getComputedStyle("display")||(E=!1,K?m=q:this.setStartBefore(q));u=q.getPrevious()}for(;u;)if(C=!1,u.type==CKEDITOR.NODE_COMMENT)u=u.getPrevious();else{if(u.type==CKEDITOR.NODE_TEXT)f=u.getText(),e.test(f)&&(u=null),C=/[\s\ufeff]$/.test(f);else if((u.$.offsetWidth>(CKEDITOR.env.webkit?1:0)||b&&u.is("br"))&&!u.data("cke-bookmark"))if(E&&CKEDITOR.dtd.$removeEmpty[u.getName()]){f=u.getText();if(e.test(f))u=
null;else for(var t=u.$.getElementsByTagName("*"),B=0,z;z=t[B++];)if(!CKEDITOR.dtd.$removeEmpty[z.nodeName.toLowerCase()]){u=null;break}u&&(C=!!f.length)}else u=null;C&&(E?K?m=q:q&&this.setStartBefore(q):E=!0);if(u){C=u.getPrevious();if(!q&&!C){q=u;u=null;break}u=C}else q=null}q&&(q=c(q.getParent()))}C=this.endContainer;t=this.endOffset;q=u=null;K=E=!1;C.type==CKEDITOR.NODE_TEXT?CKEDITOR.tools.trim(C.substring(t)).length?E=!0:(E=!C.getLength(),t==C.getLength()?(u=C.getNext())||(q=C.getParent()):d(C,
t,l)&&(q=C.getParent())):(u=C.getChild(t))||(q=C);for(;q||u;){if(q&&!u){!K&&q.equals(k)&&(K=!0);if(h?q.isBlockBoundary():!l.contains(q))break;E&&"inline"==q.getComputedStyle("display")||(E=!1,K?p=q:q&&this.setEndAfter(q));u=q.getNext()}for(;u;){C=!1;if(u.type==CKEDITOR.NODE_TEXT)f=u.getText(),d(u,0,l)||(u=null),C=/^[\s\ufeff]/.test(f);else if(u.type==CKEDITOR.NODE_ELEMENT){if((0<u.$.offsetWidth||b&&u.is("br"))&&!u.data("cke-bookmark"))if(E&&CKEDITOR.dtd.$removeEmpty[u.getName()]){f=u.getText();if(e.test(f))u=
null;else for(t=u.$.getElementsByTagName("*"),B=0;z=t[B++];)if(!CKEDITOR.dtd.$removeEmpty[z.nodeName.toLowerCase()]){u=null;break}u&&(C=!!f.length)}else u=null}else C=1;C&&E&&(K?p=q:this.setEndAfter(q));if(u){C=u.getNext();if(!q&&!C){q=u;u=null;break}u=C}else q=null}q&&(q=c(q.getParent()))}m&&p&&(k=m.contains(p)?p:m,this.setStartBefore(k),this.setEndAfter(k));break;case CKEDITOR.ENLARGE_BLOCK_CONTENTS:case CKEDITOR.ENLARGE_LIST_ITEM_CONTENTS:q=new CKEDITOR.dom.range(this.root);l=this.root;q.setStartAt(l,
CKEDITOR.POSITION_AFTER_START);q.setEnd(this.startContainer,this.startOffset);q=new CKEDITOR.dom.walker(q);var H,N,O=CKEDITOR.dom.walker.blockBoundary(a==CKEDITOR.ENLARGE_LIST_ITEM_CONTENTS?{br:1}:null),r=null,I=function(a){if(a.type==CKEDITOR.NODE_ELEMENT&&"false"==a.getAttribute("contenteditable"))if(r){if(r.equals(a)){r=null;return}}else r=a;else if(r)return;var b=O(a);b||(H=a);return b},h=function(a){var b=I(a);!b&&a.is&&a.is("br")&&(N=a);return b};q.guard=I;q=q.lastBackward();H=H||l;this.setStartAt(H,
!H.is("br")&&(!q&&this.checkStartOfBlock()||q&&H.contains(q))?CKEDITOR.POSITION_AFTER_START:CKEDITOR.POSITION_AFTER_END);if(a==CKEDITOR.ENLARGE_LIST_ITEM_CONTENTS){q=this.clone();q=new CKEDITOR.dom.walker(q);var M=CKEDITOR.dom.walker.whitespaces(),n=CKEDITOR.dom.walker.bookmark();q.evaluator=function(a){return!M(a)&&!n(a)};if((q=q.previous())&&q.type==CKEDITOR.NODE_ELEMENT&&q.is("br"))break}q=this.clone();q.collapse();q.setEndAt(l,CKEDITOR.POSITION_BEFORE_END);q=new CKEDITOR.dom.walker(q);q.guard=
a==CKEDITOR.ENLARGE_LIST_ITEM_CONTENTS?h:I;H=r=N=null;q=q.lastForward();H=H||l;this.setEndAt(H,!q&&this.checkEndOfBlock()||q&&H.contains(q)?CKEDITOR.POSITION_BEFORE_END:CKEDITOR.POSITION_BEFORE_START);N&&this.setEndAfter(N)}},shrink:function(a,b,c){var d="boolean"===typeof c?c:c&&"boolean"===typeof c.shrinkOnBlockBoundary?c.shrinkOnBlockBoundary:!0,e=c&&c.skipBogus;if(!this.collapsed){a=a||CKEDITOR.SHRINK_TEXT;var f=this.clone(),l=this.startContainer,h=this.endContainer,k=this.startOffset,m=this.endOffset,
p=c=1;l&&l.type==CKEDITOR.NODE_TEXT&&(k?k>=l.getLength()?f.setStartAfter(l):(f.setStartBefore(l),c=0):f.setStartBefore(l));h&&h.type==CKEDITOR.NODE_TEXT&&(m?m>=h.getLength()?f.setEndAfter(h):(f.setEndAfter(h),p=0):f.setEndBefore(h));var f=new CKEDITOR.dom.walker(f),q=CKEDITOR.dom.walker.bookmark(),u=CKEDITOR.dom.walker.bogus();f.evaluator=function(b){return b.type==(a==CKEDITOR.SHRINK_ELEMENT?CKEDITOR.NODE_ELEMENT:CKEDITOR.NODE_TEXT)};var K;f.guard=function(b,c){if(e&&u(b)||q(b))return!0;if(a==CKEDITOR.SHRINK_ELEMENT&&
b.type==CKEDITOR.NODE_TEXT||c&&b.equals(K)||!1===d&&b.type==CKEDITOR.NODE_ELEMENT&&b.isBlockBoundary()||b.type==CKEDITOR.NODE_ELEMENT&&b.hasAttribute("contenteditable"))return!1;c||b.type!=CKEDITOR.NODE_ELEMENT||(K=b);return!0};c&&(l=f[a==CKEDITOR.SHRINK_ELEMENT?"lastForward":"next"]())&&this.setStartAt(l,b?CKEDITOR.POSITION_AFTER_START:CKEDITOR.POSITION_BEFORE_START);p&&(f.reset(),(f=f[a==CKEDITOR.SHRINK_ELEMENT?"lastBackward":"previous"]())&&this.setEndAt(f,b?CKEDITOR.POSITION_BEFORE_END:CKEDITOR.POSITION_AFTER_END));
return!(!c&&!p)}},insertNode:function(a){this.optimizeBookmark();this.trim(!1,!0);var b=this.startContainer,c=b.getChild(this.startOffset);c?a.insertBefore(c):b.append(a);a.getParent()&&a.getParent().equals(this.endContainer)&&this.endOffset++;this.setStartBefore(a)},moveToPosition:function(a,b){this.setStartAt(a,b);this.collapse(!0)},moveToRange:function(a){this.setStart(a.startContainer,a.startOffset);this.setEnd(a.endContainer,a.endOffset)},selectNodeContents:function(a){this.setStart(a,0);this.setEnd(a,
a.type==CKEDITOR.NODE_TEXT?a.getLength():a.getChildCount())},setStart:function(b,c){b.type==CKEDITOR.NODE_ELEMENT&&CKEDITOR.dtd.$empty[b.getName()]&&(c=b.getIndex(),b=b.getParent());this._setStartContainer(b);this.startOffset=c;this.endContainer||(this._setEndContainer(b),this.endOffset=c);a(this)},setEnd:function(b,c){b.type==CKEDITOR.NODE_ELEMENT&&CKEDITOR.dtd.$empty[b.getName()]&&(c=b.getIndex()+1,b=b.getParent());this._setEndContainer(b);this.endOffset=c;this.startContainer||(this._setStartContainer(b),
this.startOffset=c);a(this)},setStartAfter:function(a){this.setStart(a.getParent(),a.getIndex()+1)},setStartBefore:function(a){this.setStart(a.getParent(),a.getIndex())},setEndAfter:function(a){this.setEnd(a.getParent(),a.getIndex()+1)},setEndBefore:function(a){this.setEnd(a.getParent(),a.getIndex())},setStartAt:function(b,c){switch(c){case CKEDITOR.POSITION_AFTER_START:this.setStart(b,0);break;case CKEDITOR.POSITION_BEFORE_END:b.type==CKEDITOR.NODE_TEXT?this.setStart(b,b.getLength()):this.setStart(b,
b.getChildCount());break;case CKEDITOR.POSITION_BEFORE_START:this.setStartBefore(b);break;case CKEDITOR.POSITION_AFTER_END:this.setStartAfter(b)}a(this)},setEndAt:function(b,c){switch(c){case CKEDITOR.POSITION_AFTER_START:this.setEnd(b,0);break;case CKEDITOR.POSITION_BEFORE_END:b.type==CKEDITOR.NODE_TEXT?this.setEnd(b,b.getLength()):this.setEnd(b,b.getChildCount());break;case CKEDITOR.POSITION_BEFORE_START:this.setEndBefore(b);break;case CKEDITOR.POSITION_AFTER_END:this.setEndAfter(b)}a(this)},fixBlock:function(a,
b){var c=this.createBookmark(),d=this.document.createElement(b);this.collapse(a);this.enlarge(CKEDITOR.ENLARGE_BLOCK_CONTENTS);this.extractContents().appendTo(d);d.trim();this.insertNode(d);var e=d.getBogus();e&&e.remove();d.appendBogus();this.moveToBookmark(c);return d},splitBlock:function(a,b){var c=new CKEDITOR.dom.elementPath(this.startContainer,this.root),d=new CKEDITOR.dom.elementPath(this.endContainer,this.root),e=c.block,f=d.block,l=null;if(!c.blockLimit.equals(d.blockLimit))return null;"br"!=
a&&(e||(e=this.fixBlock(!0,a),f=(new CKEDITOR.dom.elementPath(this.endContainer,this.root)).block),f||(f=this.fixBlock(!1,a)));c=e&&this.checkStartOfBlock();d=f&&this.checkEndOfBlock();this.deleteContents();e&&e.equals(f)&&(d?(l=new CKEDITOR.dom.elementPath(this.startContainer,this.root),this.moveToPosition(f,CKEDITOR.POSITION_AFTER_END),f=null):c?(l=new CKEDITOR.dom.elementPath(this.startContainer,this.root),this.moveToPosition(e,CKEDITOR.POSITION_BEFORE_START),e=null):(f=this.splitElement(e,b||
!1),e.is("ul","ol")||e.appendBogus()));return{previousBlock:e,nextBlock:f,wasStartOfBlock:c,wasEndOfBlock:d,elementPath:l}},splitElement:function(a,b){if(!this.collapsed)return null;this.setEndAt(a,CKEDITOR.POSITION_BEFORE_END);var c=this.extractContents(!1,b||!1),d=a.clone(!1,b||!1);c.appendTo(d);d.insertAfter(a);this.moveToPosition(a,CKEDITOR.POSITION_AFTER_END);return d},removeEmptyBlocksAtEnd:function(){function a(g){return function(a){return b(a)||c(a)||a.type==CKEDITOR.NODE_ELEMENT&&a.isEmptyInlineRemoveable()||
g.is("table")&&a.is("caption")?!1:!0}}var b=CKEDITOR.dom.walker.whitespaces(),c=CKEDITOR.dom.walker.bookmark(!1);return function(b){for(var c=this.createBookmark(),d=this[b?"endPath":"startPath"](),e=d.block||d.blockLimit,f;e&&!e.equals(d.root)&&!e.getFirst(a(e));)f=e.getParent(),this[b?"setEndAt":"setStartAt"](e,CKEDITOR.POSITION_AFTER_END),e.remove(1),e=f;this.moveToBookmark(c)}}(),startPath:function(){return new CKEDITOR.dom.elementPath(this.startContainer,this.root)},endPath:function(){return new CKEDITOR.dom.elementPath(this.endContainer,
this.root)},checkBoundaryOfElement:function(a,b){var d=b==CKEDITOR.START,e=this.clone();e.collapse(d);e[d?"setStartAt":"setEndAt"](a,d?CKEDITOR.POSITION_AFTER_START:CKEDITOR.POSITION_BEFORE_END);e=new CKEDITOR.dom.walker(e);e.evaluator=c(d);return e[d?"checkBackward":"checkForward"]()},checkStartOfBlock:function(a){var c=this.startContainer,d=this.startOffset;CKEDITOR.env.ie&&d&&c.type==CKEDITOR.NODE_TEXT&&(c=CKEDITOR.tools.ltrim(c.substring(0,d)),h.test(c)&&this.trim(0,1));a||this.trim();a=new CKEDITOR.dom.elementPath(this.startContainer,
this.root);c=this.clone();c.collapse(!0);c.setStartAt(a.block||a.blockLimit,CKEDITOR.POSITION_AFTER_START);a=new CKEDITOR.dom.walker(c);a.evaluator=b();return a.checkBackward()},checkEndOfBlock:function(a){var c=this.endContainer,d=this.endOffset;CKEDITOR.env.ie&&c.type==CKEDITOR.NODE_TEXT&&(c=CKEDITOR.tools.rtrim(c.substring(d)),h.test(c)&&this.trim(1,0));a||this.trim();a=new CKEDITOR.dom.elementPath(this.endContainer,this.root);c=this.clone();c.collapse(!1);c.setEndAt(a.block||a.blockLimit,CKEDITOR.POSITION_BEFORE_END);
a=new CKEDITOR.dom.walker(c);a.evaluator=b();return a.checkForward()},getPreviousNode:function(a,b,c){var d=this.clone();d.collapse(1);d.setStartAt(c||this.root,CKEDITOR.POSITION_AFTER_START);c=new CKEDITOR.dom.walker(d);c.evaluator=a;c.guard=b;return c.previous()},getNextNode:function(a,b,c){var d=this.clone();d.collapse();d.setEndAt(c||this.root,CKEDITOR.POSITION_BEFORE_END);c=new CKEDITOR.dom.walker(d);c.evaluator=a;c.guard=b;return c.next()},checkReadOnly:function(){function a(b,c){for(;b;){if(b.type==
CKEDITOR.NODE_ELEMENT){if("false"==b.getAttribute("contentEditable")&&!b.data("cke-editable"))return 0;if(b.is("html")||"true"==b.getAttribute("contentEditable")&&(b.contains(c)||b.equals(c)))break}b=b.getParent()}return 1}return function(){var b=this.startContainer,c=this.endContainer;return!(a(b,c)&&a(c,b))}}(),moveToElementEditablePosition:function(a,b){if(a.type==CKEDITOR.NODE_ELEMENT&&!a.isEditable(!1))return this.moveToPosition(a,b?CKEDITOR.POSITION_AFTER_END:CKEDITOR.POSITION_BEFORE_START),
!0;for(var c=0;a;){if(a.type==CKEDITOR.NODE_TEXT){b&&this.endContainer&&this.checkEndOfBlock()&&h.test(a.getText())?this.moveToPosition(a,CKEDITOR.POSITION_BEFORE_START):this.moveToPosition(a,b?CKEDITOR.POSITION_AFTER_END:CKEDITOR.POSITION_BEFORE_START);c=1;break}if(a.type==CKEDITOR.NODE_ELEMENT)if(a.isEditable())this.moveToPosition(a,b?CKEDITOR.POSITION_BEFORE_END:CKEDITOR.POSITION_AFTER_START),c=1;else if(b&&a.is("br")&&this.endContainer&&this.checkEndOfBlock())this.moveToPosition(a,CKEDITOR.POSITION_BEFORE_START);
else if("false"==a.getAttribute("contenteditable")&&a.is(CKEDITOR.dtd.$block))return this.setStartBefore(a),this.setEndAfter(a),!0;var d=a,e=c,f=void 0;d.type==CKEDITOR.NODE_ELEMENT&&d.isEditable(!1)&&(f=d[b?"getLast":"getFirst"](m));e||f||(f=d[b?"getPrevious":"getNext"](m));a=f}return!!c},moveToClosestEditablePosition:function(a,b){var c,d=0,e,f,l=[CKEDITOR.POSITION_AFTER_END,CKEDITOR.POSITION_BEFORE_START];a?(c=new CKEDITOR.dom.range(this.root),c.moveToPosition(a,l[b?0:1])):c=this.clone();if(a&&
!a.is(CKEDITOR.dtd.$block))d=1;else if(e=c[b?"getNextEditableNode":"getPreviousEditableNode"]())d=1,(f=e.type==CKEDITOR.NODE_ELEMENT)&&e.is(CKEDITOR.dtd.$block)&&"false"==e.getAttribute("contenteditable")?(c.setStartAt(e,CKEDITOR.POSITION_BEFORE_START),c.setEndAt(e,CKEDITOR.POSITION_AFTER_END)):!CKEDITOR.env.needsBrFiller&&f&&e.is(CKEDITOR.dom.walker.validEmptyBlockContainers)?(c.setEnd(e,0),c.collapse()):c.moveToPosition(e,l[b?1:0]);d&&this.moveToRange(c);return!!d},moveToElementEditStart:function(a){return this.moveToElementEditablePosition(a)},
moveToElementEditEnd:function(a){return this.moveToElementEditablePosition(a,!0)},getEnclosedNode:function(){var a=this.clone();a.optimize();if(a.startContainer.type!=CKEDITOR.NODE_ELEMENT||a.endContainer.type!=CKEDITOR.NODE_ELEMENT)return null;var a=new CKEDITOR.dom.walker(a),b=CKEDITOR.dom.walker.bookmark(!1,!0),c=CKEDITOR.dom.walker.whitespaces(!0);a.evaluator=function(a){return c(a)&&b(a)};var d=a.next();a.reset();return d&&d.equals(a.previous())?d:null},getTouchedStartNode:function(){var a=this.startContainer;
return this.collapsed||a.type!=CKEDITOR.NODE_ELEMENT?a:a.getChild(this.startOffset)||a},getTouchedEndNode:function(){var a=this.endContainer;return this.collapsed||a.type!=CKEDITOR.NODE_ELEMENT?a:a.getChild(this.endOffset-1)||a},getNextEditableNode:f(),getPreviousEditableNode:f(1),_getTableElement:function(a){a=a||{td:1,th:1,tr:1,tbody:1,thead:1,tfoot:1,table:1};var b=this.getTouchedStartNode(),c=this.getTouchedEndNode(),d=b.getAscendant("table",!0),c=c.getAscendant("table",!0);return d&&!this.root.contains(d)?
null:this.getEnclosedNode()?this.getEnclosedNode().getAscendant(a,!0):d&&c&&(d.equals(c)||d.contains(c)||c.contains(d))?b.getAscendant(a,!0):null},scrollIntoView:function(){var a=new CKEDITOR.dom.element.createFromHtml("\x3cspan\x3e\x26nbsp;\x3c/span\x3e",this.document),b,c,d,e=this.clone();e.optimize();(d=e.startContainer.type==CKEDITOR.NODE_TEXT)?(c=e.startContainer.getText(),b=e.startContainer.split(e.startOffset),a.insertAfter(e.startContainer)):e.insertNode(a);a.scrollIntoView();d&&(e.startContainer.setText(c),
b.remove());a.remove()},getClientRects:function(){function a(b,c){var d=CKEDITOR.tools.array.map(b,function(a){return a}),e=new CKEDITOR.dom.range(c.root),g,f,h;c.startContainer instanceof CKEDITOR.dom.element&&(f=0===c.startOffset&&c.startContainer.hasAttribute("data-widget"));c.endContainer instanceof CKEDITOR.dom.element&&(h=(h=c.endOffset===(c.endContainer.getChildCount?c.endContainer.getChildCount():c.endContainer.length))&&c.endContainer.hasAttribute("data-widget"));f&&e.setStart(c.startContainer.getParent(),
c.startContainer.getIndex());h&&e.setEnd(c.endContainer.getParent(),c.endContainer.getIndex()+1);if(f||h)c=e;e=c.cloneContents().find("[data-cke-widget-id]").toArray();if(e=CKEDITOR.tools.array.map(e,function(a){var b=c.root.editor;a=a.getAttribute("data-cke-widget-id");return b.widgets.instances[a].element}))return e=CKEDITOR.tools.array.map(e,function(a){var b;b=a.getParent().hasClass("cke_widget_wrapper")?a.getParent():a;g=this.root.getDocument().$.createRange();g.setStart(b.getParent().$,b.getIndex());
g.setEnd(b.getParent().$,b.getIndex()+1);b=g.getClientRects();b.widgetRect=a.getClientRect();return b},c),CKEDITOR.tools.array.forEach(e,function(a){function b(e){CKEDITOR.tools.array.forEach(d,function(b,g){var f=CKEDITOR.tools.objectCompare(a[e],b);f||(f=CKEDITOR.tools.objectCompare(a.widgetRect,b));f&&(Array.prototype.splice.call(d,g,a.length-e,a.widgetRect),c=!0)});c||(e<d.length-1?b(e+1):d.push(a.widgetRect))}var c;b(0)}),d}function b(a,c,e){var g;c.collapsed?e.startContainer instanceof CKEDITOR.dom.element?
(a=e.checkStartOfBlock(),g=new CKEDITOR.dom.text("​"),a?e.startContainer.append(g,!0):0===e.startOffset?g.insertBefore(e.startContainer.getFirst()):(e=e.startContainer.getChildren().getItem(e.startOffset-1),g.insertAfter(e)),c.setStart(g.$,0),c.setEnd(g.$,0),a=c.getClientRects(),g.remove()):e.startContainer instanceof CKEDITOR.dom.text&&(""===e.startContainer.getText()?(e.startContainer.setText("​"),a=c.getClientRects(),e.startContainer.setText("")):a=[d(e.createBookmark())]):a=[d(e.createBookmark())];
return a}function c(a,b,d){a=CKEDITOR.tools.extend({},a);b&&(a=CKEDITOR.tools.getAbsoluteRectPosition(d.document.getWindow(),a));!a.width&&(a.width=a.right-a.left);!a.height&&(a.height=a.bottom-a.top);return a}function d(a){var b=a.startNode;a=a.endNode;var c;b.setText("​");b.removeStyle("display");a?(a.setText("​"),a.removeStyle("display"),c=[b.getClientRect(),a.getClientRect()],a.remove()):c=[b.getClientRect(),b.getClientRect()];b.remove();return{right:Math.max(c[0].right,c[1].right),bottom:Math.max(c[0].bottom,
c[1].bottom),left:Math.min(c[0].left,c[1].left),top:Math.min(c[0].top,c[1].top),width:Math.abs(c[0].left-c[1].left),height:Math.max(c[0].bottom,c[1].bottom)-Math.min(c[0].top,c[1].top)}}return void 0!==document.getSelection?function(d){var e=this.root.getDocument().$.createRange(),f;e.setStart(this.startContainer.$,this.startOffset);e.setEnd(this.endContainer.$,this.endOffset);f=e.getClientRects();f=a(f,this);f.length||(f=b(f,e,this));return CKEDITOR.tools.array.map(f,function(a){return c(a,d,this)},
this)}:function(a){return[c(d(this.createBookmark()),a,this)]}}(),_setStartContainer:function(a){this.startContainer=a},_setEndContainer:function(a){this.endContainer=a},_find:function(a,b){var c=this.getCommonAncestor(),d=this.getBoundaryNodes(),e=[],f,l,h,k;if(c&&c.find)for(l=c.find(a),f=0;f<l.count();f++)if(c=l.getItem(f),b||!c.isReadOnly())h=c.getPosition(d.startNode)&CKEDITOR.POSITION_FOLLOWING||d.startNode.equals(c),k=c.getPosition(d.endNode)&CKEDITOR.POSITION_PRECEDING+CKEDITOR.POSITION_IS_CONTAINED||
d.endNode.equals(c),h&&k&&e.push(c);return e}};CKEDITOR.dom.range.mergeRanges=function(a){return CKEDITOR.tools.array.reduce(a,function(a,b){var c=a[a.length-1],d=!1;b=b.clone();b.enlarge(CKEDITOR.ENLARGE_ELEMENT);if(c){var e=new CKEDITOR.dom.range(b.root),d=new CKEDITOR.dom.walker(e),g=CKEDITOR.dom.walker.whitespaces();e.setStart(c.endContainer,c.endOffset);e.setEnd(b.startContainer,b.startOffset);for(e=d.next();g(e)||b.endContainer.equals(e);)e=d.next();d=!e}d?c.setEnd(b.endContainer,b.endOffset):
a.push(b);return a},[])}})();CKEDITOR.POSITION_AFTER_START=1;CKEDITOR.POSITION_BEFORE_END=2;CKEDITOR.POSITION_BEFORE_START=3;CKEDITOR.POSITION_AFTER_END=4;CKEDITOR.ENLARGE_ELEMENT=1;CKEDITOR.ENLARGE_BLOCK_CONTENTS=2;CKEDITOR.ENLARGE_LIST_ITEM_CONTENTS=3;CKEDITOR.ENLARGE_INLINE=4;CKEDITOR.START=1;CKEDITOR.END=2;CKEDITOR.SHRINK_ELEMENT=1;CKEDITOR.SHRINK_TEXT=2;"use strict";
(function(){function a(a){1>arguments.length||(this.range=a,this.forceBrBreak=0,this.enlargeBr=1,this.enforceRealBlocks=0,this._||(this._={}))}function d(a){var b=[];a.forEach(function(a){if("true"==a.getAttribute("contenteditable"))return b.push(a),!1},CKEDITOR.NODE_ELEMENT,!0);return b}function b(a,c,e,f){a:{null==f&&(f=d(e));for(var h;h=f.shift();)if(h.getDtd().p){f={element:h,remaining:f};break a}f=null}if(!f)return 0;if((h=CKEDITOR.filter.instances[f.element.data("cke-filter")])&&!h.check(c))return b(a,
c,e,f.remaining);c=new CKEDITOR.dom.range(f.element);c.selectNodeContents(f.element);c=c.createIterator();c.enlargeBr=a.enlargeBr;c.enforceRealBlocks=a.enforceRealBlocks;c.activeFilter=c.filter=h;a._.nestedEditable={element:f.element,container:e,remaining:f.remaining,iterator:c};return 1}function c(a,b,c){if(!b)return!1;a=a.clone();a.collapse(!c);return a.checkBoundaryOfElement(b,c?CKEDITOR.START:CKEDITOR.END)}var f=/^[\r\n\t ]+$/,e=CKEDITOR.dom.walker.bookmark(!1,!0),k=CKEDITOR.dom.walker.whitespaces(!0),
h=function(a){return e(a)&&k(a)},p={dd:1,dt:1,li:1};a.prototype={getNextParagraph:function(a){var d,k,y,A,F;a=a||"p";if(this._.nestedEditable){if(d=this._.nestedEditable.iterator.getNextParagraph(a))return this.activeFilter=this._.nestedEditable.iterator.activeFilter,d;this.activeFilter=this.filter;if(b(this,a,this._.nestedEditable.container,this._.nestedEditable.remaining))return this.activeFilter=this._.nestedEditable.iterator.activeFilter,this._.nestedEditable.iterator.getNextParagraph(a);this._.nestedEditable=
null}if(!this.range.root.getDtd()[a])return null;if(!this._.started){var v=this.range.clone();k=v.startPath();var l=v.endPath(),L=!v.collapsed&&c(v,k.block),w=!v.collapsed&&c(v,l.block,1);v.shrink(CKEDITOR.SHRINK_ELEMENT,!0);L&&v.setStartAt(k.block,CKEDITOR.POSITION_BEFORE_END);w&&v.setEndAt(l.block,CKEDITOR.POSITION_AFTER_START);k=v.endContainer.hasAscendant("pre",!0)||v.startContainer.hasAscendant("pre",!0);v.enlarge(this.forceBrBreak&&!k||!this.enlargeBr?CKEDITOR.ENLARGE_LIST_ITEM_CONTENTS:CKEDITOR.ENLARGE_BLOCK_CONTENTS);
v.collapsed||(k=new CKEDITOR.dom.walker(v.clone()),l=CKEDITOR.dom.walker.bookmark(!0,!0),k.evaluator=l,this._.nextNode=k.next(),k=new CKEDITOR.dom.walker(v.clone()),k.evaluator=l,k=k.previous(),this._.lastNode=k.getNextSourceNode(!0,null,v.root),this._.lastNode&&this._.lastNode.type==CKEDITOR.NODE_TEXT&&!CKEDITOR.tools.trim(this._.lastNode.getText())&&this._.lastNode.getParent().isBlockBoundary()&&(l=this.range.clone(),l.moveToPosition(this._.lastNode,CKEDITOR.POSITION_AFTER_END),l.checkEndOfBlock()&&
(l=new CKEDITOR.dom.elementPath(l.endContainer,l.root),this._.lastNode=(l.block||l.blockLimit).getNextSourceNode(!0))),this._.lastNode&&v.root.contains(this._.lastNode)||(this._.lastNode=this._.docEndMarker=v.document.createText(""),this._.lastNode.insertAfter(k)),v=null);this._.started=1;k=v}l=this._.nextNode;v=this._.lastNode;for(this._.nextNode=null;l;){var L=0,w=l.hasAscendant("pre"),J=l.type!=CKEDITOR.NODE_ELEMENT,G=0;if(J)l.type==CKEDITOR.NODE_TEXT&&f.test(l.getText())&&(J=0);else{var q=l.getName();
if(CKEDITOR.dtd.$block[q]&&"false"==l.getAttribute("contenteditable")){d=l;b(this,a,d);break}else if(l.isBlockBoundary(this.forceBrBreak&&!w&&{br:1})){if("br"==q)J=1;else if(!k&&!l.getChildCount()&&"hr"!=q){d=l;y=l.equals(v);break}k&&(k.setEndAt(l,CKEDITOR.POSITION_BEFORE_START),"br"!=q&&(this._.nextNode=l));L=1}else{if(l.getFirst()){k||(k=this.range.clone(),k.setStartAt(l,CKEDITOR.POSITION_BEFORE_START));l=l.getFirst();continue}J=1}}J&&!k&&(k=this.range.clone(),k.setStartAt(l,CKEDITOR.POSITION_BEFORE_START));
y=(!L||J)&&l.equals(v);if(k&&!L)for(;!l.getNext(h)&&!y;){q=l.getParent();if(q.isBlockBoundary(this.forceBrBreak&&!w&&{br:1})){L=1;J=0;y||q.equals(v);k.setEndAt(q,CKEDITOR.POSITION_BEFORE_END);break}l=q;J=1;y=l.equals(v);G=1}J&&k.setEndAt(l,CKEDITOR.POSITION_AFTER_END);l=this._getNextSourceNode(l,G,v);if((y=!l)||L&&k)break}if(!d){if(!k)return this._.docEndMarker&&this._.docEndMarker.remove(),this._.nextNode=null;d=new CKEDITOR.dom.elementPath(k.startContainer,k.root);l=d.blockLimit;L={div:1,th:1,td:1};
d=d.block;!d&&l&&!this.enforceRealBlocks&&L[l.getName()]&&k.checkStartOfBlock()&&k.checkEndOfBlock()&&!l.equals(k.root)?d=l:!d||this.enforceRealBlocks&&d.is(p)?(d=this.range.document.createElement(a),k.extractContents().appendTo(d),d.trim(),k.insertNode(d),A=F=!0):"li"!=d.getName()?k.checkStartOfBlock()&&k.checkEndOfBlock()||(d=d.clone(!1),k.extractContents().appendTo(d),d.trim(),F=k.splitBlock(),A=!F.wasStartOfBlock,F=!F.wasEndOfBlock,k.insertNode(d)):y||(this._.nextNode=d.equals(v)?null:this._getNextSourceNode(k.getBoundaryNodes().endNode,
1,v))}A&&(A=d.getPrevious())&&A.type==CKEDITOR.NODE_ELEMENT&&("br"==A.getName()?A.remove():A.getLast()&&"br"==A.getLast().$.nodeName.toLowerCase()&&A.getLast().remove());F&&(A=d.getLast())&&A.type==CKEDITOR.NODE_ELEMENT&&"br"==A.getName()&&(!CKEDITOR.env.needsBrFiller||A.getPrevious(e)||A.getNext(e))&&A.remove();this._.nextNode||(this._.nextNode=y||d.equals(v)||!v?null:this._getNextSourceNode(d,1,v));return d},_getNextSourceNode:function(a,b,c){function d(a){return!(a.equals(c)||a.equals(f))}var f=
this.range.root;for(a=a.getNextSourceNode(b,null,d);!e(a);)a=a.getNextSourceNode(b,null,d);return a}};CKEDITOR.dom.range.prototype.createIterator=function(){return new a(this)}})();
CKEDITOR.command=function(a,d){this.uiItems=[];this.exec=function(b){if(this.state==CKEDITOR.TRISTATE_DISABLED||!this.checkAllowed())return!1;this.editorFocus&&a.focus();return!1===this.fire("exec")?!0:!1!==d.exec.call(this,a,b)};this.refresh=function(a,b){if(!this.readOnly&&a.readOnly)return!0;if(this.context&&!b.isContextFor(this.context)||!this.checkAllowed(!0))return this.disable(),!0;this.startDisabled||this.enable();this.modes&&!this.modes[a.mode]&&this.disable();return!1===this.fire("refresh",
{editor:a,path:b})?!0:d.refresh&&!1!==d.refresh.apply(this,arguments)};var b;this.checkAllowed=function(c){return c||"boolean"!=typeof b?b=a.activeFilter.checkFeature(this):b};CKEDITOR.tools.extend(this,d,{modes:{wysiwyg:1},editorFocus:1,contextSensitive:!!d.context,state:CKEDITOR.TRISTATE_DISABLED});CKEDITOR.event.call(this)};
CKEDITOR.command.prototype={enable:function(){this.state==CKEDITOR.TRISTATE_DISABLED&&this.checkAllowed()&&this.setState(this.preserveState&&"undefined"!=typeof this.previousState?this.previousState:CKEDITOR.TRISTATE_OFF)},disable:function(){this.setState(CKEDITOR.TRISTATE_DISABLED)},setState:function(a){if(this.state==a||a!=CKEDITOR.TRISTATE_DISABLED&&!this.checkAllowed())return!1;this.previousState=this.state;this.state=a;this.fire("state");return!0},toggleState:function(){this.state==CKEDITOR.TRISTATE_OFF?
this.setState(CKEDITOR.TRISTATE_ON):this.state==CKEDITOR.TRISTATE_ON&&this.setState(CKEDITOR.TRISTATE_OFF)}};CKEDITOR.event.implementOn(CKEDITOR.command.prototype);CKEDITOR.ENTER_P=1;CKEDITOR.ENTER_BR=2;CKEDITOR.ENTER_DIV=3;
CKEDITOR.config={customConfig:"config.js",autoUpdateElement:!0,language:"",defaultLanguage:"en",contentsLangDirection:"",enterMode:CKEDITOR.ENTER_P,forceEnterMode:!1,shiftEnterMode:CKEDITOR.ENTER_BR,docType:"\x3c!DOCTYPE html\x3e",bodyId:"",bodyClass:"",fullPage:!1,height:200,contentsCss:CKEDITOR.getUrl("contents.css"),extraPlugins:"",removePlugins:"",protectedSource:[],tabIndex:0,useComputedState:!0,width:"",baseFloatZIndex:1E4,blockedKeystrokes:[CKEDITOR.CTRL+66,CKEDITOR.CTRL+73,CKEDITOR.CTRL+85]};
(function(){function a(a,b,c,d,e){var f,g;a=[];for(f in b){g=b[f];g="boolean"==typeof g?{}:"function"==typeof g?{match:g}:B(g);"$"!=f.charAt(0)&&(g.elements=f);c&&(g.featureName=c.toLowerCase());var n=g;n.elements=k(n.elements,/\s+/)||null;n.propertiesOnly=n.propertiesOnly||!0===n.elements;var t=/\s*,\s*/,r=void 0;for(r in N){n[r]=k(n[r],t)||null;var l=n,z=O[r],h=k(n[O[r]],t),E=n[r],D=[],H=!0,I=void 0;h?H=!1:h={};for(I in E)"!"==I.charAt(0)&&(I=I.slice(1),D.push(I),h[I]=!0,H=!1);for(;I=D.pop();)E[I]=
E["!"+I],delete E["!"+I];l[z]=(H?!1:h)||null}n.match=n.match||null;d.push(g);a.push(g)}b=e.elements;e=e.generic;var C;c=0;for(d=a.length;c<d;++c){f=B(a[c]);g=!0===f.classes||!0===f.styles||!0===f.attributes;n=f;r=z=t=void 0;for(t in N)n[t]=L(n[t]);l=!0;for(r in O){t=O[r];z=n[t];h=[];E=void 0;for(E in z)-1<E.indexOf("*")?h.push(new RegExp("^"+E.replace(/\*/g,".*")+"$")):h.push(E);z=h;z.length&&(n[t]=z,l=!1)}n.nothingRequired=l;n.noProperties=!(n.attributes||n.classes||n.styles);if(!0===f.elements||
null===f.elements)e[g?"unshift":"push"](f);else for(C in n=f.elements,delete f.elements,n)if(b[C])b[C][g?"unshift":"push"](f);else b[C]=[f]}}function d(a,c,d,e){if(!a.match||a.match(c))if(e||h(a,c))if(a.propertiesOnly||(d.valid=!0),d.allAttributes||(d.allAttributes=b(a.attributes,c.attributes,d.validAttributes)),d.allStyles||(d.allStyles=b(a.styles,c.styles,d.validStyles)),!d.allClasses){a=a.classes;c=c.classes;e=d.validClasses;if(a)if(!0===a)a=!0;else{for(var f=0,g=c.length,n;f<g;++f)n=c[f],e[n]||
(e[n]=a(n));a=!1}else a=!1;d.allClasses=a}}function b(a,b,c){if(!a)return!1;if(!0===a)return!0;for(var d in b)c[d]||(c[d]=a(d));return!1}function c(a,b,c){if(!a.match||a.match(b)){if(a.noProperties)return!1;c.hadInvalidAttribute=f(a.attributes,b.attributes)||c.hadInvalidAttribute;c.hadInvalidStyle=f(a.styles,b.styles)||c.hadInvalidStyle;a=a.classes;b=b.classes;if(a){for(var d=!1,e=!0===a,g=b.length;g--;)if(e||a(b[g]))b.splice(g,1),d=!0;a=d}else a=!1;c.hadInvalidClass=a||c.hadInvalidClass}}function f(a,
b){if(!a)return!1;var c=!1,d=!0===a,e;for(e in b)if(d||a(e))delete b[e],c=!0;return c}function e(a,b,c){if(a.disabled||a.customConfig&&!c||!b)return!1;a._.cachedChecks={};return!0}function k(a,b){if(!a)return!1;if(!0===a)return a;if("string"==typeof a)return a=z(a),"*"==a?!0:CKEDITOR.tools.convertArrayToObject(a.split(b));if(CKEDITOR.tools.isArray(a))return a.length?CKEDITOR.tools.convertArrayToObject(a):!1;var c={},d=0,e;for(e in a)c[e]=a[e],d++;return d?c:!1}function h(a,b){if(a.nothingRequired)return!0;
var c,d,e,f;if(e=a.requiredClasses)for(f=b.classes,c=0;c<e.length;++c)if(d=e[c],"string"==typeof d){if(-1==CKEDITOR.tools.indexOf(f,d))return!1}else if(!CKEDITOR.tools.checkIfAnyArrayItemMatches(f,d))return!1;return p(b.styles,a.requiredStyles)&&p(b.attributes,a.requiredAttributes)}function p(a,b){if(!b)return!0;for(var c=0,d;c<b.length;++c)if(d=b[c],"string"==typeof d){if(!(d in a))return!1}else if(!CKEDITOR.tools.checkIfAnyObjectPropertyMatches(a,d))return!1;return!0}function m(a){if(!a)return{};
a=a.split(/\s*,\s*/).sort();for(var b={};a.length;)b[a.shift()]="cke-test";return b}function g(a){var b,c,d,e,f={},g=1;for(a=z(a);b=a.match(r);)(c=b[2])?(d=x(c,"styles"),e=x(c,"attrs"),c=x(c,"classes")):d=e=c=null,f["$"+g++]={elements:b[1],classes:c,styles:d,attributes:e},a=a.slice(b[0].length);return f}function x(a,b){var c=a.match(I[b]);return c?z(c[1]):null}function y(a){var b=a.styleBackup=a.attributes.style,c=a.classBackup=a.attributes["class"];a.styles||(a.styles=CKEDITOR.tools.parseCssText(b||
"",1));a.classes||(a.classes=c?c.split(/\s+/):[])}function A(a,b,e,f){var g=0,t;f.toHtml&&(b.name=b.name.replace(M,"$1"));if(f.doCallbacks&&a.elementCallbacks){a:{t=a.elementCallbacks;for(var r=0,z=t.length,B;r<z;++r)if(B=t[r](b)){t=B;break a}t=void 0}if(t)return t}if(f.doTransform&&(t=a._.transformations[b.name])){y(b);for(r=0;r<t.length;++r)q(a,b,t[r]);v(b)}if(f.doFilter){a:{r=b.name;z=a._;a=z.allowedRules.elements[r];t=z.allowedRules.generic;r=z.disallowedRules.elements[r];z=z.disallowedRules.generic;
B=f.skipRequired;var k={valid:!1,validAttributes:{},validClasses:{},validStyles:{},allAttributes:!1,allClasses:!1,allStyles:!1,hadInvalidAttribute:!1,hadInvalidClass:!1,hadInvalidStyle:!1},h,E;if(a||t){y(b);if(r)for(h=0,E=r.length;h<E;++h)if(!1===c(r[h],b,k)){a=null;break a}if(z)for(h=0,E=z.length;h<E;++h)c(z[h],b,k);if(a)for(h=0,E=a.length;h<E;++h)d(a[h],b,k,B);if(t)for(h=0,E=t.length;h<E;++h)d(t[h],b,k,B);a=k}else a=null}if(!a||!a.valid)return e.push(b),1;E=a.validAttributes;var D=a.validStyles;
t=a.validClasses;var r=b.attributes,H=b.styles,z=b.classes;B=b.classBackup;var O=b.styleBackup,I,C,N=[],k=[],m=/^data-cke-/;h=!1;delete r.style;delete r["class"];delete b.classBackup;delete b.styleBackup;if(!a.allAttributes)for(I in r)E[I]||(m.test(I)?I==(C=I.replace(/^data-cke-saved-/,""))||E[C]||(delete r[I],h=!0):(delete r[I],h=!0));if(!a.allStyles||a.hadInvalidStyle){for(I in H)a.allStyles||D[I]?N.push(I+":"+H[I]):h=!0;N.length&&(r.style=N.sort().join("; "))}else O&&(r.style=O);if(!a.allClasses||
a.hadInvalidClass){for(I=0;I<z.length;++I)(a.allClasses||t[z[I]])&&k.push(z[I]);k.length&&(r["class"]=k.sort().join(" "));B&&k.length<B.split(/\s+/).length&&(h=!0)}else B&&(r["class"]=B);h&&(g=1);if(!f.skipFinalValidation&&!l(b))return e.push(b),1}f.toHtml&&(b.name=b.name.replace(n,"cke:$1"));return g}function F(a){var b=[],c;for(c in a)-1<c.indexOf("*")&&b.push(c.replace(/\*/g,".*"));return b.length?new RegExp("^(?:"+b.join("|")+")$"):null}function v(a){var b=a.attributes,c;delete b.style;delete b["class"];
if(c=CKEDITOR.tools.writeCssText(a.styles,!0))b.style=c;a.classes.length&&(b["class"]=a.classes.sort().join(" "))}function l(a){switch(a.name){case "a":if(!(a.children.length||a.attributes.name||a.attributes.id))return!1;break;case "img":if(!a.attributes.src)return!1}return!0}function L(a){if(!a)return!1;if(!0===a)return!0;var b=F(a);return function(c){return c in a||b&&c.match(b)}}function w(){return new CKEDITOR.htmlParser.element("br")}function J(a){return a.type==CKEDITOR.NODE_ELEMENT&&("br"==
a.name||t.$block[a.name])}function G(a,b,c){var d=a.name;if(t.$empty[d]||!a.children.length)"hr"==d&&"br"==b?a.replaceWith(w()):(a.parent&&c.push({check:"it",el:a.parent}),a.remove());else if(t.$block[d]||"tr"==d)if("br"==b)a.previous&&!J(a.previous)&&(b=w(),b.insertBefore(a)),a.next&&!J(a.next)&&(b=w(),b.insertAfter(a)),a.replaceWithChildren();else{var d=a.children,e;b:{e=t[b];for(var f=0,g=d.length,n;f<g;++f)if(n=d[f],n.type==CKEDITOR.NODE_ELEMENT&&!e[n.name]){e=!1;break b}e=!0}if(e)a.name=b,a.attributes=
{},c.push({check:"parent-down",el:a});else{e=a.parent;for(var f=e.type==CKEDITOR.NODE_DOCUMENT_FRAGMENT||"body"==e.name,r,z,g=d.length;0<g;)n=d[--g],f&&(n.type==CKEDITOR.NODE_TEXT||n.type==CKEDITOR.NODE_ELEMENT&&t.$inline[n.name])?(r||(r=new CKEDITOR.htmlParser.element(b),r.insertAfter(a),c.push({check:"parent-down",el:r})),r.add(n,0)):(r=null,z=t[e.name]||t.span,n.insertAfter(a),e.type==CKEDITOR.NODE_DOCUMENT_FRAGMENT||n.type!=CKEDITOR.NODE_ELEMENT||z[n.name]||c.push({check:"el-up",el:n}));a.remove()}}else d in
{style:1,script:1}?a.remove():(a.parent&&c.push({check:"it",el:a.parent}),a.replaceWithChildren())}function q(a,b,c){var d,e;for(d=0;d<c.length;++d)if(e=c[d],!(e.check&&!a.check(e.check,!1)||e.left&&!e.left(b))){e.right(b,D);break}}function u(a,b){var c=b.getDefinition(),d=c.attributes,e=c.styles,f,g,n,t;if(a.name!=c.element)return!1;for(f in d)if("class"==f)for(c=d[f].split(/\s+/),n=a.classes.join("|");t=c.pop();){if(-1==n.indexOf(t))return!1}else if(a.attributes[f]!=d[f])return!1;for(g in e)if(a.styles[g]!=
e[g])return!1;return!0}function K(a,b){var c,d;"string"==typeof a?c=a:a instanceof CKEDITOR.style?d=a:(c=a[0],d=a[1]);return[{element:c,left:d,right:function(a,c){c.transform(a,b)}}]}function E(a){return function(b){return u(b,a)}}function C(a){return function(b,c){c[a](b)}}var t=CKEDITOR.dtd,B=CKEDITOR.tools.copy,z=CKEDITOR.tools.trim,H=["","p","br","div"];CKEDITOR.FILTER_SKIP_TREE=2;CKEDITOR.filter=function(a,b){this.allowedContent=[];this.disallowedContent=[];this.elementCallbacks=null;this.disabled=
!1;this.editor=null;this.id=CKEDITOR.tools.getNextNumber();this._={allowedRules:{elements:{},generic:[]},disallowedRules:{elements:{},generic:[]},transformations:{},cachedTests:{},cachedChecks:{}};CKEDITOR.filter.instances[this.id]=this;var c=this.editor=a instanceof CKEDITOR.editor?a:null;if(c&&!b){this.customConfig=!0;var d=c.config.allowedContent;!0===d?this.disabled=!0:(d||(this.customConfig=!1),this.allow(d,"config",1),this.allow(c.config.extraAllowedContent,"extra",1),this.allow(H[c.enterMode]+
" "+H[c.shiftEnterMode],"default",1),this.disallow(c.config.disallowedContent))}else this.customConfig=!1,this.allow(b||a,"default",1)};CKEDITOR.filter.instances={};CKEDITOR.filter.prototype={allow:function(b,c,d){if(!e(this,b,d))return!1;var f,n;if("string"==typeof b)b=g(b);else if(b instanceof CKEDITOR.style){if(b.toAllowedContentRules)return this.allow(b.toAllowedContentRules(this.editor),c,d);f=b.getDefinition();b={};d=f.attributes;b[f.element]=f={styles:f.styles,requiredStyles:f.styles&&CKEDITOR.tools.object.keys(f.styles)};
d&&(d=B(d),f.classes=d["class"]?d["class"].split(/\s+/):null,f.requiredClasses=f.classes,delete d["class"],f.attributes=d,f.requiredAttributes=d&&CKEDITOR.tools.object.keys(d))}else if(CKEDITOR.tools.isArray(b)){for(f=0;f<b.length;++f)n=this.allow(b[f],c,d);return n}a(this,b,c,this.allowedContent,this._.allowedRules);return!0},applyTo:function(a,b,c,d){if(this.disabled)return!1;var e=this,f=[],g=this.editor&&this.editor.config.protectedSource,n,r=!1,z={doFilter:!c,doTransform:!0,doCallbacks:!0,toHtml:b};
a.forEach(function(a){if(a.type==CKEDITOR.NODE_ELEMENT){if("off"==a.attributes["data-cke-filter"])return!1;if(!b||"span"!=a.name||!~CKEDITOR.tools.object.keys(a.attributes).join("|").indexOf("data-cke-"))if(n=A(e,a,f,z),n&1)r=!0;else if(n&2)return!1}else if(a.type==CKEDITOR.NODE_COMMENT&&a.value.match(/^\{cke_protected\}(?!\{C\})/)){var c;a:{var d=decodeURIComponent(a.value.replace(/^\{cke_protected\}/,""));c=[];var t,l,B;if(g)for(l=0;l<g.length;++l)if((B=d.match(g[l]))&&B[0].length==d.length){c=
!0;break a}d=CKEDITOR.htmlParser.fragment.fromHtml(d);1==d.children.length&&(t=d.children[0]).type==CKEDITOR.NODE_ELEMENT&&A(e,t,c,z);c=!c.length}c||f.push(a)}},null,!0);f.length&&(r=!0);var B;a=[];d=H[d||(this.editor?this.editor.enterMode:CKEDITOR.ENTER_P)];for(var h;c=f.pop();)c.type==CKEDITOR.NODE_ELEMENT?G(c,d,a):c.remove();for(;B=a.pop();)if(c=B.el,c.parent)switch(h=t[c.parent.name]||t.span,B.check){case "it":t.$removeEmpty[c.name]&&!c.children.length?G(c,d,a):l(c)||G(c,d,a);break;case "el-up":c.parent.type==
CKEDITOR.NODE_DOCUMENT_FRAGMENT||h[c.name]||G(c,d,a);break;case "parent-down":c.parent.type==CKEDITOR.NODE_DOCUMENT_FRAGMENT||h[c.name]||G(c.parent,d,a)}return r},checkFeature:function(a){if(this.disabled||!a)return!0;a.toFeature&&(a=a.toFeature(this.editor));return!a.requiredContent||this.check(a.requiredContent)},disable:function(){this.disabled=!0},disallow:function(b){if(!e(this,b,!0))return!1;"string"==typeof b&&(b=g(b));a(this,b,null,this.disallowedContent,this._.disallowedRules);return!0},
addContentForms:function(a){if(!this.disabled&&a){var b,c,d=[],e;for(b=0;b<a.length&&!e;++b)c=a[b],("string"==typeof c||c instanceof CKEDITOR.style)&&this.check(c)&&(e=c);if(e){for(b=0;b<a.length;++b)d.push(K(a[b],e));this.addTransformations(d)}}},addElementCallback:function(a){this.elementCallbacks||(this.elementCallbacks=[]);this.elementCallbacks.push(a)},addFeature:function(a){if(this.disabled||!a)return!0;a.toFeature&&(a=a.toFeature(this.editor));this.allow(a.allowedContent,a.name);this.addTransformations(a.contentTransformations);
this.addContentForms(a.contentForms);return a.requiredContent&&(this.customConfig||this.disallowedContent.length)?this.check(a.requiredContent):!0},addTransformations:function(a){var b,c;if(!this.disabled&&a){var d=this._.transformations,e;for(e=0;e<a.length;++e){b=a[e];var f=void 0,g=void 0,n=void 0,t=void 0,r=void 0,z=void 0;c=[];for(g=0;g<b.length;++g)n=b[g],"string"==typeof n?(n=n.split(/\s*:\s*/),t=n[0],r=null,z=n[1]):(t=n.check,r=n.left,z=n.right),f||(f=n,f=f.element?f.element:t?t.match(/^([a-z0-9]+)/i)[0]:
f.left.getDefinition().element),r instanceof CKEDITOR.style&&(r=E(r)),c.push({check:t==f?null:t,left:r,right:"string"==typeof z?C(z):z});b=f;d[b]||(d[b]=[]);d[b].push(c)}}},check:function(a,b,c){if(this.disabled)return!0;if(CKEDITOR.tools.isArray(a)){for(var d=a.length;d--;)if(this.check(a[d],b,c))return!0;return!1}var e,f;if("string"==typeof a){f=a+"\x3c"+(!1===b?"0":"1")+(c?"1":"0")+"\x3e";if(f in this._.cachedChecks)return this._.cachedChecks[f];e=g(a).$1;var n=e.styles,d=e.classes;e.name=e.elements;
e.classes=d=d?d.split(/\s*,\s*/):[];e.styles=m(n);e.attributes=m(e.attributes);e.children=[];d.length&&(e.attributes["class"]=d.join(" "));n&&(e.attributes.style=CKEDITOR.tools.writeCssText(e.styles))}else e=a.getDefinition(),n=e.styles,d=e.attributes||{},n&&!CKEDITOR.tools.isEmpty(n)?(n=B(n),d.style=CKEDITOR.tools.writeCssText(n,!0)):n={},e={name:e.element,attributes:d,classes:d["class"]?d["class"].split(/\s+/):[],styles:n,children:[]};var n=CKEDITOR.tools.clone(e),t=[],r;if(!1!==b&&(r=this._.transformations[e.name])){for(d=
0;d<r.length;++d)q(this,e,r[d]);v(e)}A(this,n,t,{doFilter:!0,doTransform:!1!==b,skipRequired:!c,skipFinalValidation:!c});0<t.length?c=!1:((b=e.attributes["class"])&&(e.attributes["class"]=e.attributes["class"].split(" ").sort().join(" ")),c=CKEDITOR.tools.objectCompare(e.attributes,n.attributes,!0),b&&(e.attributes["class"]=b));"string"==typeof a&&(this._.cachedChecks[f]=c);return c},getAllowedEnterMode:function(){var a=["p","div","br"],b={p:CKEDITOR.ENTER_P,div:CKEDITOR.ENTER_DIV,br:CKEDITOR.ENTER_BR};
return function(c,d){var e=a.slice(),f;if(this.check(H[c]))return c;for(d||(e=e.reverse());f=e.pop();)if(this.check(f))return b[f];return CKEDITOR.ENTER_BR}}(),clone:function(){var a=new CKEDITOR.filter,b=CKEDITOR.tools.clone;a.allowedContent=b(this.allowedContent);a._.allowedRules=b(this._.allowedRules);a.disallowedContent=b(this.disallowedContent);a._.disallowedRules=b(this._.disallowedRules);a._.transformations=b(this._.transformations);a.disabled=this.disabled;a.editor=this.editor;return a},destroy:function(){delete CKEDITOR.filter.instances[this.id];
delete this._;delete this.allowedContent;delete this.disallowedContent}};var N={styles:1,attributes:1,classes:1},O={styles:"requiredStyles",attributes:"requiredAttributes",classes:"requiredClasses"},r=/^([a-z0-9\-*\s]+)((?:\s*\{[!\w\-,\s\*]+\}\s*|\s*\[[!\w\-,\s\*]+\]\s*|\s*\([!\w\-,\s\*]+\)\s*){0,3})(?:;\s*|$)/i,I={styles:/{([^}]+)}/,attrs:/\[([^\]]+)\]/,classes:/\(([^\)]+)\)/},M=/^cke:(object|embed|param)$/,n=/^(object|embed|param)$/,D;D=CKEDITOR.filter.transformationsTools={sizeToStyle:function(a){this.lengthToStyle(a,
"width");this.lengthToStyle(a,"height")},sizeToAttribute:function(a){this.lengthToAttribute(a,"width");this.lengthToAttribute(a,"height")},lengthToStyle:function(a,b,c){c=c||b;if(!(c in a.styles)){var d=a.attributes[b];d&&(/^\d+$/.test(d)&&(d+="px"),a.styles[c]=d)}delete a.attributes[b]},lengthToAttribute:function(a,b,c){c=c||b;if(!(c in a.attributes)){var d=a.styles[b],e=d&&d.match(/^(\d+)(?:\.\d*)?px$/);e?a.attributes[c]=e[1]:"cke-test"==d&&(a.attributes[c]="cke-test")}delete a.styles[b]},alignmentToStyle:function(a){if(!("float"in
a.styles)){var b=a.attributes.align;if("left"==b||"right"==b)a.styles["float"]=b}delete a.attributes.align},alignmentToAttribute:function(a){if(!("align"in a.attributes)){var b=a.styles["float"];if("left"==b||"right"==b)a.attributes.align=b}delete a.styles["float"]},splitBorderShorthand:function(a){if(a.styles.border){var b=CKEDITOR.tools.style.parse.border(a.styles.border);b.color&&(a.styles["border-color"]=b.color);b.style&&(a.styles["border-style"]=b.style);b.width&&(a.styles["border-width"]=b.width);
delete a.styles.border}},listTypeToStyle:function(a){if(a.attributes.type)switch(a.attributes.type){case "a":a.styles["list-style-type"]="lower-alpha";break;case "A":a.styles["list-style-type"]="upper-alpha";break;case "i":a.styles["list-style-type"]="lower-roman";break;case "I":a.styles["list-style-type"]="upper-roman";break;case "1":a.styles["list-style-type"]="decimal";break;default:a.styles["list-style-type"]=a.attributes.type}},splitMarginShorthand:function(a){function b(d){a.styles["margin-top"]=
c[d[0]];a.styles["margin-right"]=c[d[1]];a.styles["margin-bottom"]=c[d[2]];a.styles["margin-left"]=c[d[3]]}if(a.styles.margin){var c=a.styles.margin.match(/(auto|0|(?:\-?[\.\d]+(?:\w+|%)))/g)||["0px"];switch(c.length){case 1:b([0,0,0,0]);break;case 2:b([0,1,0,1]);break;case 3:b([0,1,2,1]);break;case 4:b([0,1,2,3])}delete a.styles.margin}},matchesStyle:u,transform:function(a,b){if("string"==typeof b)a.name=b;else{var c=b.getDefinition(),d=c.styles,e=c.attributes,f,g,n,t;a.name=c.element;for(f in e)if("class"==
f)for(c=a.classes.join("|"),n=e[f].split(/\s+/);t=n.pop();)-1==c.indexOf(t)&&a.classes.push(t);else a.attributes[f]=e[f];for(g in d)a.styles[g]=d[g]}}}})();
(function(){CKEDITOR.focusManager=function(a){if(a.focusManager)return a.focusManager;this.hasFocus=!1;this.currentActive=null;this._={editor:a};return this};CKEDITOR.focusManager._={blurDelay:200};CKEDITOR.focusManager.prototype={focus:function(a){this._.timer&&clearTimeout(this._.timer);a&&(this.currentActive=a);this.hasFocus||this._.locked||((a=CKEDITOR.currentInstance)&&a.focusManager.blur(1),this.hasFocus=!0,(a=this._.editor.container)&&a.addClass("cke_focus"),this._.editor.fire("focus"))},lock:function(){this._.locked=
1},unlock:function(){delete this._.locked},blur:function(a){function d(){if(this.hasFocus){this.hasFocus=!1;var a=this._.editor.container;a&&a.removeClass("cke_focus");this._.editor.fire("blur")}}if(!this._.locked){this._.timer&&clearTimeout(this._.timer);var b=CKEDITOR.focusManager._.blurDelay;a||!b?d.call(this):this._.timer=CKEDITOR.tools.setTimeout(function(){delete this._.timer;d.call(this)},b,this)}},add:function(a,d){var b=a.getCustomData("focusmanager");if(!b||b!=this){b&&b.remove(a);var b=
"focus",c="blur";d&&(CKEDITOR.env.ie?(b="focusin",c="focusout"):CKEDITOR.event.useCapture=1);var f={blur:function(){a.equals(this.currentActive)&&this.blur()},focus:function(){this.focus(a)}};a.on(b,f.focus,this);a.on(c,f.blur,this);d&&(CKEDITOR.event.useCapture=0);a.setCustomData("focusmanager",this);a.setCustomData("focusmanager_handlers",f)}},remove:function(a){a.removeCustomData("focusmanager");var d=a.removeCustomData("focusmanager_handlers");a.removeListener("blur",d.blur);a.removeListener("focus",
d.focus)}}})();CKEDITOR.keystrokeHandler=function(a){if(a.keystrokeHandler)return a.keystrokeHandler;this.keystrokes={};this.blockedKeystrokes={};this._={editor:a};return this};
(function(){var a,d=function(b){b=b.data;var d=b.getKeystroke(),e=this.keystrokes[d],k=this._.editor;a=!1===k.fire("key",{keyCode:d,domEvent:b});a||(e&&(a=!1!==k.execCommand(e,{from:"keystrokeHandler"})),a||(a=!!this.blockedKeystrokes[d]));a&&b.preventDefault(!0);return!a},b=function(b){a&&(a=!1,b.data.preventDefault(!0))};CKEDITOR.keystrokeHandler.prototype={attach:function(a){a.on("keydown",d,this);if(CKEDITOR.env.gecko&&CKEDITOR.env.mac)a.on("keypress",b,this)}}})();
(function(){CKEDITOR.lang={languages:{af:1,ar:1,az:1,bg:1,bn:1,bs:1,ca:1,cs:1,cy:1,da:1,de:1,"de-ch":1,el:1,"en-au":1,"en-ca":1,"en-gb":1,en:1,eo:1,es:1,"es-mx":1,et:1,eu:1,fa:1,fi:1,fo:1,"fr-ca":1,fr:1,gl:1,gu:1,he:1,hi:1,hr:1,hu:1,id:1,is:1,it:1,ja:1,ka:1,km:1,ko:1,ku:1,lt:1,lv:1,mk:1,mn:1,ms:1,nb:1,nl:1,no:1,oc:1,pl:1,"pt-br":1,pt:1,ro:1,ru:1,si:1,sk:1,sl:1,sq:1,"sr-latn":1,sr:1,sv:1,th:1,tr:1,tt:1,ug:1,uk:1,vi:1,"zh-cn":1,zh:1},rtl:{ar:1,fa:1,he:1,ku:1,ug:1},load:function(a,d,b){a&&CKEDITOR.lang.languages[a]||
(a=this.detect(d,a));var c=this;d=function(){c[a].dir=c.rtl[a]?"rtl":"ltr";b(a,c[a])};this[a]?d():CKEDITOR.scriptLoader.load(CKEDITOR.getUrl("lang/"+a+".js"),d,this)},detect:function(a,d){var b=this.languages;d=d||navigator.userLanguage||navigator.language||a;var c=d.toLowerCase().match(/([a-z]+)(?:-([a-z]+))?/),f=c[1],c=c[2];b[f+"-"+c]?f=f+"-"+c:b[f]||(f=null);CKEDITOR.lang.detect=f?function(){return f}:function(a){return a};return f||a}}})();
CKEDITOR.scriptLoader=function(){var a={},d={};return{load:function(b,c,f,e){var k="string"==typeof b;k&&(b=[b]);f||(f=CKEDITOR);var h=b.length,p=h,m=[],g=[],x=function(a){c&&(k?c.call(f,a):c.call(f,m,g))};if(0===p)x(!0);else{var y=function(a,b){(b?m:g).push(a);0>=--p&&(e&&CKEDITOR.document.getDocumentElement().removeStyle("cursor"),x(b))},A=function(b,c){a[b]=1;var e=d[b];delete d[b];for(var f=0;f<e.length;f++)e[f](b,c)},F=function(b){if(a[b])y(b,!0);else{var e=d[b]||(d[b]=[]);e.push(y);if(!(1<e.length)){var f=
new CKEDITOR.dom.element("script");f.setAttributes({type:"text/javascript",src:b});c&&(CKEDITOR.env.ie&&(8>=CKEDITOR.env.version||CKEDITOR.env.ie9Compat)?f.$.onreadystatechange=function(){if("loaded"==f.$.readyState||"complete"==f.$.readyState)f.$.onreadystatechange=null,A(b,!0)}:(f.$.onload=function(){setTimeout(function(){f.$.onload=null;f.$.onerror=null;A(b,!0)},0)},f.$.onerror=function(){f.$.onload=null;f.$.onerror=null;A(b,!1)}));f.appendTo(CKEDITOR.document.getHead())}}};e&&CKEDITOR.document.getDocumentElement().setStyle("cursor",
"wait");for(var v=0;v<h;v++)F(b[v])}},queue:function(){function a(){var b;(b=c[0])&&this.load(b.scriptUrl,b.callback,CKEDITOR,0)}var c=[];return function(d,e){var k=this;c.push({scriptUrl:d,callback:function(){e&&e.apply(this,arguments);c.shift();a.call(k)}});1==c.length&&a.call(this)}}()}}();CKEDITOR.resourceManager=function(a,d){this.basePath=a;this.fileName=d;this.registered={};this.loaded={};this.externals={};this._={waitingList:{}}};
CKEDITOR.resourceManager.prototype={add:function(a,d){if(this.registered[a])throw Error('[CKEDITOR.resourceManager.add] The resource name "'+a+'" is already registered.');var b=this.registered[a]=d||{};b.name=a;b.path=this.getPath(a);CKEDITOR.fire(a+CKEDITOR.tools.capitalize(this.fileName)+"Ready",b);return this.get(a)},get:function(a){return this.registered[a]||null},getPath:function(a){var d=this.externals[a];return CKEDITOR.getUrl(d&&d.dir||this.basePath+a+"/")},getFilePath:function(a){var d=this.externals[a];
return CKEDITOR.getUrl(this.getPath(a)+(d?d.file:this.fileName+".js"))},addExternal:function(a,d,b){b||(d=d.replace(/[^\/]+$/,function(a){b=a;return""}));b=b||this.fileName+".js";a=a.split(",");for(var c=0;c<a.length;c++)this.externals[a[c]]={dir:d,file:b}},load:function(a,d,b){CKEDITOR.tools.isArray(a)||(a=a?[a]:[]);for(var c=this.loaded,f=this.registered,e=[],k={},h={},p=0;p<a.length;p++){var m=a[p];if(m)if(c[m]||f[m])h[m]=this.get(m);else{var g=this.getFilePath(m);e.push(g);g in k||(k[g]=[]);k[g].push(m)}}CKEDITOR.scriptLoader.load(e,
function(a,e){if(e.length)throw Error('[CKEDITOR.resourceManager.load] Resource name "'+k[e[0]].join(",")+'" was not found at "'+e[0]+'".');for(var f=0;f<a.length;f++)for(var g=k[a[f]],m=0;m<g.length;m++){var l=g[m];h[l]=this.get(l);c[l]=1}d.call(b,h)},this)}};CKEDITOR.plugins=new CKEDITOR.resourceManager("plugins/","plugin");
CKEDITOR.plugins.load=CKEDITOR.tools.override(CKEDITOR.plugins.load,function(a){var d={};return function(b,c,f){var e={},k=function(b){a.call(this,b,function(a){CKEDITOR.tools.extend(e,a);var b=[],g;for(g in a){var h=a[g],y=h&&h.requires;if(!d[g]){if(h.icons)for(var A=h.icons.split(","),F=A.length;F--;)CKEDITOR.skin.addIcon(A[F],h.path+"icons/"+(CKEDITOR.env.hidpi&&h.hidpi?"hidpi/":"")+A[F]+".png");h.isSupportedEnvironment=h.isSupportedEnvironment||function(){return!0};d[g]=1}if(y)for(y.split&&(y=
y.split(",")),h=0;h<y.length;h++)e[y[h]]||b.push(y[h])}if(b.length)k.call(this,b);else{for(g in e)h=e[g],h.onLoad&&!h.onLoad._called&&(!1===h.onLoad()&&delete e[g],h.onLoad._called=1);c&&c.call(f||window,e)}},this)};k.call(this,b)}});CKEDITOR.plugins.setLang=function(a,d,b){var c=this.get(a);a=c.langEntries||(c.langEntries={});c=c.lang||(c.lang=[]);c.split&&(c=c.split(","));-1==CKEDITOR.tools.indexOf(c,d)&&c.push(d);a[d]=b};
CKEDITOR.ui=function(a){if(a.ui)return a.ui;this.items={};this.instances={};this.editor=a;this._={handlers:{}};return this};
CKEDITOR.ui.prototype={add:function(a,d,b){b.name=a.toLowerCase();var c=this.items[a]={type:d,command:b.command||null,args:Array.prototype.slice.call(arguments,2)};CKEDITOR.tools.extend(c,b)},get:function(a){return this.instances[a]},create:function(a){var d=this.items[a],b=d&&this._.handlers[d.type],c=d&&d.command&&this.editor.getCommand(d.command),b=b&&b.create.apply(this,d.args);this.instances[a]=b;c&&c.uiItems.push(b);b&&!b.type&&(b.type=d.type);return b},addHandler:function(a,d){this._.handlers[a]=
d},space:function(a){return CKEDITOR.document.getById(this.spaceId(a))},spaceId:function(a){return this.editor.id+"_"+a}};CKEDITOR.event.implementOn(CKEDITOR.ui);
(function(){function a(a,f,g){CKEDITOR.event.call(this);a=a&&CKEDITOR.tools.clone(a);if(void 0!==f){if(!(f instanceof CKEDITOR.dom.element))throw Error("Expect element of type CKEDITOR.dom.element.");if(!g)throw Error("One of the element modes must be specified.");if(CKEDITOR.env.ie&&CKEDITOR.env.quirks&&g==CKEDITOR.ELEMENT_MODE_INLINE)throw Error("Inline element mode is not supported on IE quirks.");if(!b(f,g))throw Error('The specified element mode is not supported on element: "'+f.getName()+'".');
this.element=f;this.elementMode=g;this.name=this.elementMode!=CKEDITOR.ELEMENT_MODE_APPENDTO&&(f.getId()||f.getNameAtt())}else this.elementMode=CKEDITOR.ELEMENT_MODE_NONE;this._={};this.commands={};this.templates={};this.name=this.name||d();this.id=CKEDITOR.tools.getNextId();this.status="unloaded";this.config=CKEDITOR.tools.prototypedCopy(CKEDITOR.config);this.ui=new CKEDITOR.ui(this);this.focusManager=new CKEDITOR.focusManager(this);this.keystrokeHandler=new CKEDITOR.keystrokeHandler(this);this.on("readOnly",
c);this.on("selectionChange",function(a){e(this,a.data.path)});this.on("activeFilterChange",function(){e(this,this.elementPath(),!0)});this.on("mode",c);CKEDITOR.dom.selection.setupEditorOptimization(this);this.on("instanceReady",function(){if(this.config.startupFocus){if("end"===this.config.startupFocus){var a=this.createRange();a.selectNodeContents(this.editable());a.shrink(CKEDITOR.SHRINK_ELEMENT,!0);a.collapse();this.getSelection().selectRanges([a])}this.focus()}});CKEDITOR.fire("instanceCreated",
null,this);CKEDITOR.add(this);CKEDITOR.tools.setTimeout(function(){this.isDestroyed()||this.isDetached()||h(this,a)},0,this)}function d(){do var a="editor"+ ++F;while(CKEDITOR.instances[a]);return a}function b(a,b){return b==CKEDITOR.ELEMENT_MODE_INLINE?a.is(CKEDITOR.dtd.$editable)||a.is("textarea"):b==CKEDITOR.ELEMENT_MODE_REPLACE?!a.is(CKEDITOR.dtd.$nonBodyContent):1}function c(){var a=this.commands,b;for(b in a)f(this,a[b])}function f(a,b){b[b.startDisabled?"disable":a.readOnly&&!b.readOnly?"disable":
b.modes[a.mode]?"enable":"disable"]()}function e(a,b,c){if(b){var d,e,f=a.commands;for(e in f)d=f[e],(c||d.contextSensitive)&&d.refresh(a,b)}}function k(a){var b=a.config.customConfig;if(!b)return!1;var b=CKEDITOR.getUrl(b),c=v[b]||(v[b]={});c.fn?(c.fn.call(a,a.config),CKEDITOR.getUrl(a.config.customConfig)!=b&&k(a)||a.fireOnce("customConfigLoaded")):CKEDITOR.scriptLoader.queue(b,function(){c.fn=c.fn||CKEDITOR.editorConfig||function(){};k(a)});return!0}function h(a,b){a.on("customConfigLoaded",function(){if(b){if(b.on)for(var c in b.on)a.on(c,
b.on[c]);CKEDITOR.tools.extend(a.config,b,!0);delete a.config.on}c=a.config;a.readOnly=c.readOnly?!0:a.elementMode==CKEDITOR.ELEMENT_MODE_INLINE?a.element.is("textarea")?a.element.hasAttribute("disabled")||a.element.hasAttribute("readonly"):a.element.isReadOnly():a.elementMode==CKEDITOR.ELEMENT_MODE_REPLACE?a.element.hasAttribute("disabled")||a.element.hasAttribute("readonly"):!1;a.blockless=a.elementMode==CKEDITOR.ELEMENT_MODE_INLINE?!(a.element.is("textarea")||CKEDITOR.dtd[a.element.getName()].p):
!1;a.tabIndex=c.tabIndex||a.element&&a.element.getAttribute("tabindex")||0;a.activeEnterMode=a.enterMode=a.blockless?CKEDITOR.ENTER_BR:c.enterMode;a.activeShiftEnterMode=a.shiftEnterMode=a.blockless?CKEDITOR.ENTER_BR:c.shiftEnterMode;c.skin&&(CKEDITOR.skinName=c.skin);a.fireOnce("configLoaded");a.dataProcessor=new CKEDITOR.htmlDataProcessor(a);a.filter=a.activeFilter=new CKEDITOR.filter(a);p(a)});b&&null!=b.customConfig&&(a.config.customConfig=b.customConfig);k(a)||a.fireOnce("customConfigLoaded")}
function p(a){CKEDITOR.skin.loadPart("editor",function(){m(a)})}function m(a){CKEDITOR.lang.load(a.config.language,a.config.defaultLanguage,function(b,c){var d=a.config.title,e=a.config.applicationTitle;a.langCode=b;a.lang=CKEDITOR.tools.prototypedCopy(c);a.title="string"==typeof d||!1===d?d:[a.lang.editor,a.name].join(", ");a.applicationTitle="string"==typeof e||!1===e?e:[a.lang.application,a.name].join(", ");a.config.contentsLangDirection||(a.config.contentsLangDirection=a.elementMode==CKEDITOR.ELEMENT_MODE_INLINE?
a.element.getDirection(1):a.lang.dir);a.fire("langLoaded");g(a)})}function g(a){a.getStylesSet(function(b){a.once("loaded",function(){a.fire("stylesSet",{styles:b})},null,null,1);x(a)})}function x(a){function b(a){if(!a)return"";CKEDITOR.tools.isArray(a)&&(a=a.join(","));return a.replace(/\s/g,"")}var c=a.config,d=b(c.plugins),e=b(c.extraPlugins),f=b(c.removePlugins);if(e)var g=new RegExp("(?:^|,)(?:"+e.replace(/,/g,"|")+")(?\x3d,|$)","g"),d=d.replace(g,""),d=d+(","+e);if(f)var h=new RegExp("(?:^|,)(?:"+
f.replace(/,/g,"|")+")(?\x3d,|$)","g"),d=d.replace(h,"");CKEDITOR.env.air&&(d+=",adobeair");CKEDITOR.plugins.load(d.split(","),function(b){var d=[],e=[],f=[];a.plugins=CKEDITOR.tools.extend({},a.plugins,b);for(var g in b){var k=b[g],N=k.lang,O=null,r=k.requires,I;CKEDITOR.tools.isArray(r)&&(r=r.join(","));if(r&&(I=r.match(h)))for(;r=I.pop();)CKEDITOR.error("editor-plugin-required",{plugin:r.replace(",",""),requiredBy:g});N&&!a.lang[g]&&(N.split&&(N=N.split(",")),0<=CKEDITOR.tools.indexOf(N,a.langCode)?
O=a.langCode:(O=a.langCode.replace(/-.*/,""),O=O!=a.langCode&&0<=CKEDITOR.tools.indexOf(N,O)?O:0<=CKEDITOR.tools.indexOf(N,"en")?"en":N[0]),k.langEntries&&k.langEntries[O]?(a.lang[g]=k.langEntries[O],O=null):f.push(CKEDITOR.getUrl(k.path+"lang/"+O+".js")));e.push(O);d.push(k)}CKEDITOR.scriptLoader.load(f,function(){if(!a.isDestroyed()&&!a.isDetached()){for(var b=["beforeInit","init","afterInit"],f=0;f<b.length;f++)for(var g=0;g<d.length;g++){var r=d[g];0===f&&e[g]&&r.lang&&r.langEntries&&(a.lang[r.name]=
r.langEntries[e[g]]);if(r[b[f]])r[b[f]](a)}a.fireOnce("pluginsLoaded");c.keystrokes&&a.setKeystroke(a.config.keystrokes);for(g=0;g<a.config.blockedKeystrokes.length;g++)a.keystrokeHandler.blockedKeystrokes[a.config.blockedKeystrokes[g]]=1;a.status="loaded";a.fireOnce("loaded");CKEDITOR.fire("instanceLoaded",null,a)}})})}function y(){var a=this.element;if(a&&this.elementMode!=CKEDITOR.ELEMENT_MODE_APPENDTO){var b=this.getData();this.config.htmlEncodeOutput&&(b=CKEDITOR.tools.htmlEncode(b));a.is("textarea")?
a.setValue(b):a.setHtml(b);return!0}return!1}function A(a,b){function c(a){var b=a.startContainer,d=a.endContainer,e=b.is&&b.is("tr"),f=b.is&&b.is("td");a=f&&b.equals(d)&&a.endOffset===b.getChildCount();b=f&&1===b.getChildCount()&&"img"===b.getChildren().getItem(0).getName();return e||a&&!b?!0:!1}function d(a){var b=a.startContainer;return b.is("tr")?a.cloneContents():b.clone(!0)}for(var e=new CKEDITOR.dom.documentFragment,f,g,h,k=0;k<a.length;k++){var C=a[k],t=C.startContainer.getAscendant("tr",
!0);c(C)?(f||(f=t.getAscendant("table").clone(),f.append(t.getAscendant({thead:1,tbody:1,tfoot:1}).clone()),e.append(f),f=f.findOne("thead, tbody, tfoot")),g&&g.equals(t)||(g=t,h=t.clone(),f.append(h)),h.append(d(C))):e.append(C.cloneContents())}return f?e:b.getHtmlFromRange(a[0])}a.prototype=CKEDITOR.editor.prototype;CKEDITOR.editor=a;var F=0,v={};CKEDITOR.tools.extend(CKEDITOR.editor.prototype,{plugins:{detectConflict:function(a,b){for(var c=0;c<b.length;c++){var d=b[c];if(this[d])return CKEDITOR.warn("editor-plugin-conflict",
{plugin:a,replacedWith:d}),!0}return!1}},addCommand:function(a,b){b.name=a.toLowerCase();var c=b instanceof CKEDITOR.command?b:new CKEDITOR.command(this,b);this.mode&&f(this,c);return this.commands[a]=c},_attachToForm:function(){function a(b){c.updateElement();c._.required&&!d.getValue()&&!1===c.fire("required")&&b.data.preventDefault()}function b(a){return!!(a&&a.call&&a.apply)}var c=this,d=c.element,e=new CKEDITOR.dom.element(d.$.form);d.is("textarea")&&e&&(e.on("submit",a),b(e.$.submit)&&(e.$.submit=
CKEDITOR.tools.override(e.$.submit,function(b){return function(){a();b.apply?b.apply(this):b()}})),c.on("destroy",function(){e.removeListener("submit",a)}))},destroy:function(a){var b=CKEDITOR.filter.instances,c=this;this.fire("beforeDestroy");!a&&y.call(this);this.editable(null);this.filter&&delete this.filter;CKEDITOR.tools.array.forEach(CKEDITOR.tools.object.keys(b),function(a){a=b[a];c===a.editor&&a.destroy()});delete this.activeFilter;this.status="destroyed";this.fire("destroy");this.removeAllListeners();
CKEDITOR.remove(this);CKEDITOR.fire("instanceDestroyed",null,this)},elementPath:function(a){if(!a){a=this.getSelection();if(!a)return null;a=a.getStartElement()}return a?new CKEDITOR.dom.elementPath(a,this.editable()):null},createRange:function(){var a=this.editable();return a?new CKEDITOR.dom.range(a):null},execCommand:function(a,b){var c=this.getCommand(a),d={name:a,commandData:b||{},command:c};return c&&c.state!=CKEDITOR.TRISTATE_DISABLED&&!1!==this.fire("beforeCommandExec",d)&&(d.returnValue=
c.exec(d.commandData),!c.async&&!1!==this.fire("afterCommandExec",d))?d.returnValue:!1},getCommand:function(a){return this.commands[a]},getData:function(a){!a&&this.fire("beforeGetData");var b=this._.data;"string"!=typeof b&&(b=(b=this.element)&&this.elementMode==CKEDITOR.ELEMENT_MODE_REPLACE?b.is("textarea")?b.getValue():b.getHtml():"");b={dataValue:b};!a&&this.fire("getData",b);return b.dataValue},getSnapshot:function(){var a=this.fire("getSnapshot");"string"!=typeof a&&(a=(a=this.element)&&this.elementMode==
CKEDITOR.ELEMENT_MODE_REPLACE?a.is("textarea")?a.getValue():a.getHtml():"");return a},loadSnapshot:function(a){this.fire("loadSnapshot",a)},setData:function(a,b,c){var d=!0,e=b;b&&"object"==typeof b&&(c=b.internal,e=b.callback,d=!b.noSnapshot);!c&&d&&this.fire("saveSnapshot");if(e||!c)this.once("dataReady",function(a){!c&&d&&this.fire("saveSnapshot");e&&e.call(a.editor)});a={dataValue:a};!c&&this.fire("setData",a);this._.data=a.dataValue;!c&&this.fire("afterSetData",a)},setReadOnly:function(a){a=
null==a||a;this.readOnly!=a&&(this.readOnly=a,this.keystrokeHandler.blockedKeystrokes[8]=+a,this.editable().setReadOnly(a),this.fire("readOnly"))},insertHtml:function(a,b,c){this.fire("insertHtml",{dataValue:a,mode:b,range:c})},insertText:function(a){this.fire("insertText",a)},insertElement:function(a){this.fire("insertElement",a)},getSelectedHtml:function(a){var b=this.editable(),c=this.getSelection(),c=c&&c.getRanges();if(!b||!c||0===c.length)return null;b=A(c,b);return a?b.getHtml():b},extractSelectedHtml:function(a,
b){var c=this.editable(),d=this.getSelection().getRanges(),e=new CKEDITOR.dom.documentFragment,f;if(!c||0===d.length)return null;for(f=0;f<d.length;f++)e.append(c.extractHtmlFromRange(d[f],b));b||this.getSelection().selectRanges([d[0]]);return a?e.getHtml():e},focus:function(){this.fire("beforeFocus")},checkDirty:function(){return"ready"==this.status&&this._.previousValue!==this.getSnapshot()},resetDirty:function(){this._.previousValue=this.getSnapshot()},updateElement:function(){return y.call(this)},
setKeystroke:function(){for(var a=this.keystrokeHandler.keystrokes,b=CKEDITOR.tools.isArray(arguments[0])?arguments[0]:[[].slice.call(arguments,0)],c,d,e=b.length;e--;)c=b[e],d=0,CKEDITOR.tools.isArray(c)&&(d=c[1],c=c[0]),d?a[c]=d:delete a[c]},getCommandKeystroke:function(a,b){var c="string"===typeof a?this.getCommand(a):a,d=[];if(c){var e=CKEDITOR.tools.object.findKey(this.commands,c),f=this.keystrokeHandler.keystrokes;if(c.fakeKeystroke)d.push(c.fakeKeystroke);else for(var g in f)f[g]===e&&d.push(g)}return b?
d:d[0]||null},addFeature:function(a){return this.filter.addFeature(a)},setActiveFilter:function(a){a||(a=this.filter);this.activeFilter!==a&&(this.activeFilter=a,this.fire("activeFilterChange"),a===this.filter?this.setActiveEnterMode(null,null):this.setActiveEnterMode(a.getAllowedEnterMode(this.enterMode),a.getAllowedEnterMode(this.shiftEnterMode,!0)))},setActiveEnterMode:function(a,b){a=a?this.blockless?CKEDITOR.ENTER_BR:a:this.enterMode;b=b?this.blockless?CKEDITOR.ENTER_BR:b:this.shiftEnterMode;
if(this.activeEnterMode!=a||this.activeShiftEnterMode!=b)this.activeEnterMode=a,this.activeShiftEnterMode=b,this.fire("activeEnterModeChange")},showNotification:function(a){alert(a)},isDetached:function(){return!!this.container&&this.container.isDetached()},isDestroyed:function(){return"destroyed"===this.status}});CKEDITOR.editor._getEditorElement=function(a){if(!CKEDITOR.env.isCompatible)return null;var b=CKEDITOR.dom.element.get(a);return b?b.getEditor()?(CKEDITOR.error("editor-element-conflict",
{editorName:b.getEditor().name}),null):b:(CKEDITOR.error("editor-incorrect-element",{element:a}),null)};CKEDITOR.editor.initializeDelayedEditorCreation=function(a,b,c){if(b.delayIfDetached_callback)return CKEDITOR.warn("editor-delayed-creation",{method:"callback"}),b.delayIfDetached_callback(function(){CKEDITOR[c](a,b);CKEDITOR.warn("editor-delayed-creation-success",{method:"callback"})}),null;var d=void 0===b.delayIfDetached_interval?CKEDITOR.config.delayIfDetached_interval:b.delayIfDetached_interval;
CKEDITOR.warn("editor-delayed-creation",{method:"interval - "+d+" ms"});var e=setInterval(function(){a.isDetached()||(clearInterval(e),CKEDITOR[c](a,b),CKEDITOR.warn("editor-delayed-creation-success",{method:"interval - "+d+" ms"}))},d);return function(){clearInterval(e)}};CKEDITOR.editor.shouldDelayEditorCreation=function(a,b){CKEDITOR.editor.mergeDelayedCreationConfigs(b);return b&&b.delayIfDetached&&a.isDetached()};CKEDITOR.editor.mergeDelayedCreationConfigs=function(a){a&&(a.delayIfDetached="boolean"===
typeof a.delayIfDetached?a.delayIfDetached:CKEDITOR.config.delayIfDetached,a.delayIfDetached_interval=isNaN(a.delayIfDetached_interval)?CKEDITOR.config.delayIfDetached_interval:a.delayIfDetached_interval,a.delayIfDetached_callback=a.delayIfDetached_callback||CKEDITOR.config.delayIfDetached_callback)}})();CKEDITOR.ELEMENT_MODE_NONE=0;CKEDITOR.ELEMENT_MODE_REPLACE=1;CKEDITOR.ELEMENT_MODE_APPENDTO=2;CKEDITOR.ELEMENT_MODE_INLINE=3;CKEDITOR.config.delayIfDetached=!1;
CKEDITOR.config.delayIfDetached_callback=void 0;CKEDITOR.config.delayIfDetached_interval=50;CKEDITOR.htmlParser=function(){this._={htmlPartsRegex:/<(?:(?:\/([^>]+)>)|(?:!--([\S|\s]*?)--!?>)|(?:([^\/\s>]+)((?:\s+[\w\-:.]+(?:\s*=\s*?(?:(?:"[^"]*")|(?:'[^']*')|[^\s"'\/>]+))?)*)[\S\s]*?(\/?)>))/g}};
(function(){var a=/([\w\-:.]+)(?:(?:\s*=\s*(?:(?:"([^"]*)")|(?:'([^']*)')|([^\s>]+)))|(?=\s|$))/g,d={checked:1,compact:1,declare:1,defer:1,disabled:1,ismap:1,multiple:1,nohref:1,noresize:1,noshade:1,nowrap:1,readonly:1,selected:1};CKEDITOR.htmlParser.prototype={onTagOpen:function(){},onTagClose:function(){},onText:function(){},onCDATA:function(){},onComment:function(){},parse:function(b){for(var c,f,e=0,k;c=this._.htmlPartsRegex.exec(b);){f=c.index;if(f>e)if(e=b.substring(e,f),k)k.push(e);else this.onText(e);
e=this._.htmlPartsRegex.lastIndex;if(f=c[1])if(f=f.toLowerCase(),k&&CKEDITOR.dtd.$cdata[f]&&(this.onCDATA(k.join("")),k=null),!k){this.onTagClose(f);continue}if(k)k.push(c[0]);else if(f=c[3]){if(f=f.toLowerCase(),!/="/.test(f)){var h={},p,m=c[4];c=!!c[5];if(m)for(;p=a.exec(m);){var g=p[1].toLowerCase();p=p[2]||p[3]||p[4]||"";h[g]=!p&&d[g]?g:CKEDITOR.tools.htmlDecodeAttr(p)}this.onTagOpen(f,h,c);!k&&CKEDITOR.dtd.$cdata[f]&&(k=[])}}else if(f=c[2])this.onComment(f)}if(b.length>e)this.onText(b.substring(e,
b.length))}}})();
CKEDITOR.htmlParser.basicWriter=CKEDITOR.tools.createClass({$:function(){this._={output:[]}},proto:{openTag:function(a){this._.output.push("\x3c",a)},openTagClose:function(a,d){d?this._.output.push(" /\x3e"):this._.output.push("\x3e")},attribute:function(a,d){"string"==typeof d&&(d=CKEDITOR.tools.htmlEncodeAttr(d));this._.output.push(" ",a,'\x3d"',d,'"')},closeTag:function(a){this._.output.push("\x3c/",a,"\x3e")},text:function(a){this._.output.push(a)},comment:function(a){this._.output.push("\x3c!--",a,
"--\x3e")},write:function(a){this._.output.push(a)},reset:function(){this._.output=[];this._.indent=!1},getHtml:function(a){var d=this._.output.join("");a&&this.reset();return d}}});"use strict";
(function(){CKEDITOR.htmlParser.node=function(){};CKEDITOR.htmlParser.node.prototype={remove:function(){var a=this.parent.children,d=CKEDITOR.tools.indexOf(a,this),b=this.previous,c=this.next;b&&(b.next=c);c&&(c.previous=b);a.splice(d,1);this.parent=null},replaceWith:function(a){var d=this.parent.children,b=CKEDITOR.tools.indexOf(d,this),c=a.previous=this.previous,f=a.next=this.next;c&&(c.next=a);f&&(f.previous=a);d[b]=a;a.parent=this.parent;this.parent=null},insertAfter:function(a){var d=a.parent.children,
b=CKEDITOR.tools.indexOf(d,a),c=a.next;d.splice(b+1,0,this);this.next=a.next;this.previous=a;a.next=this;c&&(c.previous=this);this.parent=a.parent},insertBefore:function(a){var d=a.parent.children,b=CKEDITOR.tools.indexOf(d,a);d.splice(b,0,this);this.next=a;(this.previous=a.previous)&&(a.previous.next=this);a.previous=this;this.parent=a.parent},getAscendant:function(a){var d="function"==typeof a?a:"string"==typeof a?function(b){return b.name==a}:function(b){return b.name in a},b=this.parent;for(;b&&
b.type==CKEDITOR.NODE_ELEMENT;){if(d(b))return b;b=b.parent}return null},wrapWith:function(a){this.replaceWith(a);a.add(this);return a},getIndex:function(){return CKEDITOR.tools.indexOf(this.parent.children,this)},getFilterContext:function(a){return a||{}}}})();"use strict";CKEDITOR.htmlParser.comment=function(a){this.value=a;this._={isBlockLike:!1}};
CKEDITOR.htmlParser.comment.prototype=CKEDITOR.tools.extend(new CKEDITOR.htmlParser.node,{type:CKEDITOR.NODE_COMMENT,filter:function(a,d){var b=this.value;if(!(b=a.onComment(d,b,this)))return this.remove(),!1;if("string"!=typeof b)return this.replaceWith(b),!1;this.value=b;return!0},writeHtml:function(a,d){d&&this.filter(d);a.comment(this.value)}});"use strict";
(function(){CKEDITOR.htmlParser.text=function(a){this.value=a;this._={isBlockLike:!1}};CKEDITOR.htmlParser.text.prototype=CKEDITOR.tools.extend(new CKEDITOR.htmlParser.node,{type:CKEDITOR.NODE_TEXT,filter:function(a,d){if(!(this.value=a.onText(d,this.value,this)))return this.remove(),!1},writeHtml:function(a,d){d&&this.filter(d);a.text(this.value)}})})();"use strict";
(function(){CKEDITOR.htmlParser.cdata=function(a){this.value=a};CKEDITOR.htmlParser.cdata.prototype=CKEDITOR.tools.extend(new CKEDITOR.htmlParser.node,{type:CKEDITOR.NODE_TEXT,filter:function(a){var d=this.getAscendant("style");if(d&&d.getAscendant({math:1,svg:1})){var d=CKEDITOR.htmlParser.fragment.fromHtml(this.value),b=new CKEDITOR.htmlParser.basicWriter;a.applyTo(d);d.writeHtml(b);this.value=b.getHtml()}},writeHtml:function(a){a.write(this.value)}})})();"use strict";
CKEDITOR.htmlParser.fragment=function(){this.children=[];this.parent=null;this._={isBlockLike:!0,hasInlineStarted:!1}};
(function(){function a(a){return a.attributes["data-cke-survive"]?!1:"a"==a.name&&a.attributes.href||CKEDITOR.dtd.$removeEmpty[a.name]}var d=CKEDITOR.tools.extend({table:1,ul:1,ol:1,dl:1},CKEDITOR.dtd.table,CKEDITOR.dtd.ul,CKEDITOR.dtd.ol,CKEDITOR.dtd.dl),b={ol:1,ul:1},c=CKEDITOR.tools.extend({},{html:1},CKEDITOR.dtd.html,CKEDITOR.dtd.body,CKEDITOR.dtd.head,{style:1,script:1}),f={ul:"li",ol:"li",dl:"dd",table:"tbody",tbody:"tr",thead:"tr",tfoot:"tr",tr:"td"};CKEDITOR.htmlParser.fragment.fromHtml=
function(e,k,h){function p(a){var b;if(0<l.length)for(var c=0;c<l.length;c++){var d=l[c],e=d.name,f=CKEDITOR.dtd[e],g=w.name&&CKEDITOR.dtd[w.name];g&&!g[e]||a&&f&&!f[a]&&CKEDITOR.dtd[a]?e==w.name&&(x(w,w.parent,1),c--):(b||(m(),b=1),d=d.clone(),d.parent=w,w=d,l.splice(c,1),c--)}}function m(){for(;L.length;)x(L.shift(),w)}function g(a){if(a._.isBlockLike&&"pre"!=a.name&&"textarea"!=a.name){var b=a.children.length,c=a.children[b-1],d;c&&c.type==CKEDITOR.NODE_TEXT&&((d=CKEDITOR.tools.rtrim(c.value))?
c.value=d:a.children.length=b-1)}}function x(b,c,d){c=c||w||v;var e=w;void 0===b.previous&&(y(c,b)&&(w=c,F.onTagOpen(h,{}),b.returnPoint=c=w),g(b),a(b)&&!b.children.length||c.add(b),"pre"==b.name&&(G=!1),"textarea"==b.name&&(J=!1));b.returnPoint?(w=b.returnPoint,delete b.returnPoint):w=d?c:e}function y(a,b){if((a==v||"body"==a.name)&&h&&(!a.name||CKEDITOR.dtd[a.name][h])){var c,d;return(c=b.attributes&&(d=b.attributes["data-cke-real-element-type"])?d:b.name)&&c in CKEDITOR.dtd.$inline&&!(c in CKEDITOR.dtd.head)&&
!b.isOrphan||b.type==CKEDITOR.NODE_TEXT}}function A(a,b){return a in CKEDITOR.dtd.$listItem||a in CKEDITOR.dtd.$tableContent?a==b||"dt"==a&&"dd"==b||"dd"==a&&"dt"==b:!1}var F=new CKEDITOR.htmlParser,v=k instanceof CKEDITOR.htmlParser.element?k:"string"==typeof k?new CKEDITOR.htmlParser.element(k):new CKEDITOR.htmlParser.fragment,l=[],L=[],w=v,J="textarea"==v.name,G="pre"==v.name;F.onTagOpen=function(e,f,g,h){f=new CKEDITOR.htmlParser.element(e,f);f.isUnknown&&g&&(f.isEmpty=!0);f.isOptionalClose=h;
if(a(f))l.push(f);else{if("pre"==e)G=!0;else{if("br"==e&&G){w.add(new CKEDITOR.htmlParser.text("\n"));return}"textarea"==e&&(J=!0)}if("br"==e)L.push(f);else{for(;!(h=(g=w.name)?CKEDITOR.dtd[g]||(w._.isBlockLike?CKEDITOR.dtd.div:CKEDITOR.dtd.span):c,f.isUnknown||w.isUnknown||h[e]);)if(w.isOptionalClose)F.onTagClose(g);else if(e in b&&g in b)g=w.children,(g=g[g.length-1])&&"li"==g.name||x(g=new CKEDITOR.htmlParser.element("li"),w),!f.returnPoint&&(f.returnPoint=w),w=g;else if(e in CKEDITOR.dtd.$listItem&&
!A(e,g))F.onTagOpen("li"==e?"ul":"dl",{},0,1);else if(g in d&&!A(e,g))!f.returnPoint&&(f.returnPoint=w),w=w.parent;else if(g in CKEDITOR.dtd.$inline&&l.unshift(w),w.parent)x(w,w.parent,1);else{f.isOrphan=1;break}p(e);m();f.parent=w;f.isEmpty?x(f):w=f}}};F.onTagClose=function(a){for(var b=l.length-1;0<=b;b--)if(a==l[b].name){l.splice(b,1);return}for(var c=[],d=[],e=w;e!=v&&e.name!=a;)e._.isBlockLike||d.unshift(e),c.push(e),e=e.returnPoint||e.parent;if(e!=v){for(b=0;b<c.length;b++){var f=c[b];x(f,f.parent)}w=
e;e._.isBlockLike?m():(b=CKEDITOR.config.shiftLineBreaks,!0!==b&&L.length&&("function"!==typeof b?m():(b=b(L[L.length-1]),!0!==b&&(m(),b instanceof CKEDITOR.htmlParser.text&&w.add(b),b instanceof CKEDITOR.htmlParser.element&&x(b,w)))));x(e,e.parent);e==w&&(w=w.parent);l=l.concat(d)}"body"==a&&(h=!1)};F.onText=function(a){if(!(w._.hasInlineStarted&&!L.length||G||J)&&(a=CKEDITOR.tools.ltrim(a),0===a.length))return;var b=w.name,e=b?CKEDITOR.dtd[b]||(w._.isBlockLike?CKEDITOR.dtd.div:CKEDITOR.dtd.span):
c;if(!J&&!e["#"]&&b in d)F.onTagOpen(f[b]||""),F.onText(a);else{m();p();G||J||(a=a.replace(/[\t\r\n ]{2,}|[\t\r\n]/g," "));a=new CKEDITOR.htmlParser.text(a);if(y(w,a))this.onTagOpen(h,{},0,1);w.add(a)}};F.onCDATA=function(a){w.add(new CKEDITOR.htmlParser.cdata(a))};F.onComment=function(a){m();p();w.add(new CKEDITOR.htmlParser.comment(a))};F.parse(e);for(m();w!=v;)x(w,w.parent,1);g(v);return v};CKEDITOR.htmlParser.fragment.prototype={type:CKEDITOR.NODE_DOCUMENT_FRAGMENT,add:function(a,b){isNaN(b)&&
(b=this.children.length);var c=0<b?this.children[b-1]:null;if(c){if(a._.isBlockLike&&c.type==CKEDITOR.NODE_TEXT&&(c.value=CKEDITOR.tools.rtrim(c.value),0===c.value.length)){this.children.pop();this.add(a);return}c.next=a}a.previous=c;a.parent=this;this.children.splice(b,0,a);this._.hasInlineStarted||(this._.hasInlineStarted=a.type==CKEDITOR.NODE_TEXT||a.type==CKEDITOR.NODE_ELEMENT&&!a._.isBlockLike)},filter:function(a,b){b=this.getFilterContext(b);a.onRoot(b,this);this.filterChildren(a,!1,b)},filterChildren:function(a,
b,c){if(this.childrenFilteredBy!=a.id){c=this.getFilterContext(c);if(b&&!this.parent)a.onRoot(c,this);this.childrenFilteredBy=a.id;for(b=0;b<this.children.length;b++)!1===this.children[b].filter(a,c)&&b--}},writeHtml:function(a,b){b&&this.filter(b);this.writeChildrenHtml(a)},writeChildrenHtml:function(a,b,c){var d=this.getFilterContext();if(c&&!this.parent&&b)b.onRoot(d,this);b&&this.filterChildren(b,!1,d);b=0;c=this.children;for(d=c.length;b<d;b++)c[b].writeHtml(a)},forEach:function(a,b,c){if(!(c||
b&&this.type!=b))var d=a(this);if(!1!==d){c=this.children;for(var f=0;f<c.length;f++)d=c[f],d.type==CKEDITOR.NODE_ELEMENT?d.forEach(a,b):b&&d.type!=b||a(d)}},getFilterContext:function(a){return a||{}}};CKEDITOR.config.shiftLineBreaks=!0})();"use strict";
(function(){function a(){this.rules=[]}function d(b,c,d,e){var k,h;for(k in c)(h=b[k])||(h=b[k]=new a),h.add(c[k],d,e)}CKEDITOR.htmlParser.filter=CKEDITOR.tools.createClass({$:function(b){this.id=CKEDITOR.tools.getNextNumber();this.elementNameRules=new a;this.attributeNameRules=new a;this.elementsRules={};this.attributesRules={};this.textRules=new a;this.commentRules=new a;this.rootRules=new a;b&&this.addRules(b,10)},proto:{addRules:function(a,c){var f;"number"==typeof c?f=c:c&&"priority"in c&&(f=
c.priority);"number"!=typeof f&&(f=10);"object"!=typeof c&&(c={});a.elementNames&&this.elementNameRules.addMany(a.elementNames,f,c);a.attributeNames&&this.attributeNameRules.addMany(a.attributeNames,f,c);a.elements&&d(this.elementsRules,a.elements,f,c);a.attributes&&d(this.attributesRules,a.attributes,f,c);a.text&&this.textRules.add(a.text,f,c);a.comment&&this.commentRules.add(a.comment,f,c);a.root&&this.rootRules.add(a.root,f,c)},applyTo:function(a){a.filter(this)},onElementName:function(a,c){return this.elementNameRules.execOnName(a,
c)},onAttributeName:function(a,c){return this.attributeNameRules.execOnName(a,c)},onText:function(a,c,d){return this.textRules.exec(a,c,d)},onComment:function(a,c,d){return this.commentRules.exec(a,c,d)},onRoot:function(a,c){return this.rootRules.exec(a,c)},onElement:function(a,c){for(var d=[this.elementsRules["^"],this.elementsRules[c.name],this.elementsRules.$],e,k=0;3>k;k++)if(e=d[k]){e=e.exec(a,c,this);if(!1===e)return null;if(e&&e!=c)return this.onNode(a,e);if(c.parent&&!c.name)break}return c},
onNode:function(a,c){var d=c.type;return d==CKEDITOR.NODE_ELEMENT?this.onElement(a,c):d==CKEDITOR.NODE_TEXT?new CKEDITOR.htmlParser.text(this.onText(a,c.value,c)):d==CKEDITOR.NODE_COMMENT?new CKEDITOR.htmlParser.comment(this.onComment(a,c.value,c)):null},onAttribute:function(a,c,d,e){return(d=this.attributesRules[d])?d.exec(a,e,c,this):e}}});CKEDITOR.htmlParser.filterRulesGroup=a;a.prototype={add:function(a,c,d){this.rules.splice(this.findIndex(c),0,{value:a,priority:c,options:d})},addMany:function(a,
c,d){for(var e=[this.findIndex(c),0],k=0,h=a.length;k<h;k++)e.push({value:a[k],priority:c,options:d});this.rules.splice.apply(this.rules,e)},findIndex:function(a){for(var c=this.rules,d=c.length-1;0<=d&&a<c[d].priority;)d--;return d+1},exec:function(a,c){var d=c instanceof CKEDITOR.htmlParser.node||c instanceof CKEDITOR.htmlParser.fragment,e=Array.prototype.slice.call(arguments,1),k=this.rules,h=k.length,p,m,g,x;for(x=0;x<h;x++)if(d&&(p=c.type,m=c.name),g=k[x],!(a.nonEditable&&!g.options.applyToAll||
a.nestedEditable&&g.options.excludeNestedEditable)){g=g.value.apply(null,e);if(!1===g||d&&g&&(g.name!=m||g.type!=p))return g;null!=g&&(e[0]=c=g)}return c},execOnName:function(a,c){for(var d=0,e=this.rules,k=e.length,h;c&&d<k;d++)h=e[d],a.nonEditable&&!h.options.applyToAll||a.nestedEditable&&h.options.excludeNestedEditable||(c=c.replace(h.value[0],h.value[1]));return c}}})();
(function(){function a(a,d){function g(a){return a||CKEDITOR.env.needsNbspFiller?new CKEDITOR.htmlParser.text(" "):new CKEDITOR.htmlParser.element("br",{"data-cke-bogus":1})}function t(a,d){return function(f){if(f.type!=CKEDITOR.NODE_DOCUMENT_FRAGMENT){var n=[],t=b(f),z,B;if(t)for(r(t,1)&&n.push(t);t;)e(t)&&(z=c(t))&&r(z)&&((B=c(z))&&!e(B)?n.push(z):(g(h).insertAfter(z),z.remove())),t=t.previous;for(t=0;t<n.length;t++)n[t].remove();if(n=!a||!1!==("function"==typeof d?d(f):d))h||CKEDITOR.env.needsBrFiller||
f.type!=CKEDITOR.NODE_DOCUMENT_FRAGMENT?h||CKEDITOR.env.needsBrFiller||!(7<document.documentMode||f.name in CKEDITOR.dtd.tr||f.name in CKEDITOR.dtd.$listItem)?(n=b(f),n=!n||"form"==f.name&&"input"==n.name):n=!1:n=!1;n&&f.add(g(a))}}}function r(a,b){if((!h||CKEDITOR.env.needsBrFiller)&&a.type==CKEDITOR.NODE_ELEMENT&&"br"==a.name&&!a.attributes["data-cke-eol"])return!0;var c;return a.type==CKEDITOR.NODE_TEXT&&(c=a.value.match(L))&&(c.index&&((new CKEDITOR.htmlParser.text(a.value.substring(0,c.index))).insertBefore(a),
a.value=c[0]),!CKEDITOR.env.needsBrFiller&&h&&(!b||a.parent.name in B)||!h&&((c=a.previous)&&"br"==c.name||!c||e(c)))?!0:!1}var z={elements:{}},h="html"==d,B=CKEDITOR.tools.extend({},q),E;for(E in B)"#"in J[E]||delete B[E];for(E in B)z.elements[E]=t(h,a.config.fillEmptyBlocks);z.root=t(h,!1);z.elements.br=function(a){return function(b){if(b.parent.type!=CKEDITOR.NODE_DOCUMENT_FRAGMENT){var d=b.attributes;if("data-cke-bogus"in d||"data-cke-eol"in d)delete d["data-cke-bogus"];else{for(d=b.next;d&&f(d);)d=
d.next;var n=c(b);!d&&e(b.parent)?k(b.parent,g(a)):e(d)&&n&&!e(n)&&g(a).insertBefore(d)}}}}(h);return z}function d(a,b){return a!=CKEDITOR.ENTER_BR&&!1!==b?a==CKEDITOR.ENTER_DIV?"div":"p":!1}function b(a){for(a=a.children[a.children.length-1];a&&f(a);)a=a.previous;return a}function c(a){for(a=a.previous;a&&f(a);)a=a.previous;return a}function f(a){return a.type==CKEDITOR.NODE_TEXT&&!CKEDITOR.tools.trim(a.value)||a.type==CKEDITOR.NODE_ELEMENT&&a.attributes["data-cke-bookmark"]}function e(a){return a&&
(a.type==CKEDITOR.NODE_ELEMENT&&a.name in q||a.type==CKEDITOR.NODE_DOCUMENT_FRAGMENT)}function k(a,b){var c=a.children[a.children.length-1];a.children.push(b);b.parent=a;c&&(c.next=b,b.previous=c)}function h(a){a=a.attributes;"false"!=a.contenteditable&&(a["data-cke-editable"]=a.contenteditable?"true":1);a.contenteditable="false"}function p(a){a=a.attributes;switch(a["data-cke-editable"]){case "true":a.contenteditable="true";break;case "1":delete a.contenteditable}}function m(a,b){return a.replace(t,
function(a,c,d){return"\x3c"+c+d.replace(B,function(a,c){return z.test(c)&&-1==d.indexOf("data-cke-saved-"+c)?" data-cke-saved-"+a+" data-cke-"+b+"-"+a:a})+"\x3e"})}function g(a,b){return a.replace(b,function(a,b,c){0===a.indexOf("\x3ctextarea")&&(a=b+A(c).replace(/</g,"\x26lt;").replace(/>/g,"\x26gt;")+"\x3c/textarea\x3e");return"\x3ccke:encoded\x3e"+encodeURIComponent(a)+"\x3c/cke:encoded\x3e"})}function x(a){return a.replace(O,function(a,b){return decodeURIComponent(b)})}function y(a){return a.replace(/\x3c!--(?!{cke_protected})[\s\S]+?--\x3e/g,
function(a){return"\x3c!--"+w+"{C}"+encodeURIComponent(a).replace(/--/g,"%2D%2D")+"--\x3e"})}function A(a){return a.replace(/\x3c!--\{cke_protected\}\{C\}([\s\S]+?)--\x3e/g,function(a,b){return decodeURIComponent(b)})}function F(a,b){var c=b._.dataStore;return a.replace(/\x3c!--\{cke_protected\}([\s\S]+?)--\x3e/g,function(a,b){return decodeURIComponent(b)}).replace(/\{cke_protected_(\d+)\}/g,function(a,b){return c&&c[b]||""})}function v(a,b,c){var d=[],e=b.config.protectedSource,f=b._.dataStore||
(b._.dataStore={id:1}),g=new RegExp("\x3c\\!--\\{cke_temp_"+c+"(comment)?\\}(\\d*?)--\x3e","g"),e=[/<script[\s\S]*?(<\/script>|$)/gi,/<noscript[\s\S]*?<\/noscript>/gi,/<meta[\s\S]*?\/?>/gi].concat(e);a=a.replace(/\x3c!--[\s\S]*?--\x3e/g,function(a){return"\x3c!--{cke_temp_"+c+"comment}"+(d.push(a)-1)+"--\x3e"});for(var t=0;t<e.length;t++)a=a.replace(e[t],function(a){a=a.replace(g,function(a,b,c){return d[c]});return g.test(a)?a:"\x3c!--{cke_temp_"+c+"}"+(d.push(a)-1)+"--\x3e"});a=a.replace(g,function(a,
b,c){return"\x3c!--"+w+(b?"{C}":"")+encodeURIComponent(d[c]).replace(/--/g,"%2D%2D")+"--\x3e"});a=a.replace(/<\w+(?:\s+(?:(?:[^\s=>]+\s*=\s*(?:[^'"\s>]+|'[^']*'|"[^"]*"))|[^\s=\/>]+))+\s*\/?>/g,function(a){return a.replace(/\x3c!--\{cke_protected\}([^>]*)--\x3e/g,function(a,b){f[f.id]=decodeURIComponent(b);return"{cke_protected_"+f.id++ +"}"})});return a=a.replace(/<(title|iframe|textarea)([^>]*)>([\s\S]*?)<\/\1>/g,function(a,c,d,e){return"\x3c"+c+d+"\x3e"+F(A(e),b)+"\x3c/"+c+"\x3e"})}var l;CKEDITOR.htmlDataProcessor=
function(b){var c,e,f=this;this.editor=b;this.dataFilter=c=new CKEDITOR.htmlParser.filter;this.htmlFilter=e=new CKEDITOR.htmlParser.filter;this.writer=new CKEDITOR.htmlParser.basicWriter;c.addRules(u);c.addRules(K,{applyToAll:!0});c.addRules(a(b,"data"),{applyToAll:!0});e.addRules(E);e.addRules(C,{applyToAll:!0});e.addRules(a(b,"html"),{applyToAll:!0});b.on("toHtml",function(a){var c;var e=window.crypto||window.msCrypto;c=e?e.getRandomValues(new Uint32Array(1))[0]:Math.floor(9E9*Math.random()+1E9);
a=a.data;var e=a.dataValue,e=l(e),e=v(e,b,c),e=g(e,N),e=m(e,c),e=g(e,H),e=e.replace(r,"$1cke:$2"),e=e.replace(M,"\x3ccke:$1$2\x3e\x3c/cke:$1\x3e"),e=e.replace(/(<pre\b[^>]*>)(\r\n|\n)/g,"$1$2$2"),e=e.replace(/([^a-z0-9<\-])(on\w{3,})(?!>)/gi,"$1data-cke-"+c+"-$2"),f=a.context||b.editable().getName(),t;CKEDITOR.env.ie&&9>CKEDITOR.env.version&&"pre"==f&&(f="div",e="\x3cpre\x3e"+e+"\x3c/pre\x3e",t=1);f=b.document.createElement(f);f.setHtml("a"+e);e=f.getHtml().substr(1);e=e.replace(new RegExp("data-cke-"+
c+"-","ig"),"");t&&(e=e.replace(/^<pre>|<\/pre>$/gi,""));e=e.replace(I,"$1$2");e=x(e);e=A(e);c=!1===a.fixForBody?!1:d(a.enterMode,b.config.autoParagraph);e=CKEDITOR.htmlParser.fragment.fromHtml(e,a.context,c);c&&(t=e,!t.children.length&&CKEDITOR.dtd[t.name][c]&&(c=new CKEDITOR.htmlParser.element(c),t.add(c)));a.dataValue=e},null,null,5);b.on("toHtml",function(a){a.data.filter.applyTo(a.data.dataValue,!0,a.data.dontFilter,a.data.enterMode)&&b.fire("dataFiltered")},null,null,6);b.on("toHtml",function(a){a.data.dataValue.filterChildren(f.dataFilter,
!0)},null,null,10);b.on("toHtml",function(a){a=a.data;var b=a.dataValue,c=new CKEDITOR.htmlParser.basicWriter;b.writeChildrenHtml(c);b=c.getHtml(!0);a.dataValue=y(b)},null,null,15);b.on("toDataFormat",function(a){var c=a.data.dataValue;a.data.enterMode!=CKEDITOR.ENTER_BR&&(c=c.replace(/^<br *\/?>/i,""));a.data.dataValue=CKEDITOR.htmlParser.fragment.fromHtml(c,a.data.context,d(a.data.enterMode,b.config.autoParagraph))},null,null,5);b.on("toDataFormat",function(a){a.data.dataValue.filterChildren(f.htmlFilter,
!0)},null,null,10);b.on("toDataFormat",function(a){a.data.filter.applyTo(a.data.dataValue,!1,!0)},null,null,11);b.on("toDataFormat",function(a){var c=a.data.dataValue,d=f.writer;d.reset();c.writeChildrenHtml(d);c=d.getHtml(!0);c=A(c);c=F(c,b);a.data.dataValue=c},null,null,15)};CKEDITOR.htmlDataProcessor.prototype={toHtml:function(a,b,c,d){var e=this.editor,f,g,t,r;b&&"object"==typeof b?(f=b.context,c=b.fixForBody,d=b.dontFilter,g=b.filter,t=b.enterMode,r=b.protectedWhitespaces):f=b;f||null===f||(f=
e.editable().getName());return e.fire("toHtml",{dataValue:a,context:f,fixForBody:c,dontFilter:d,filter:g||e.filter,enterMode:t||e.enterMode,protectedWhitespaces:r}).dataValue},toDataFormat:function(a,b){var c,d,e;b&&(c=b.context,d=b.filter,e=b.enterMode);c||null===c||(c=this.editor.editable().getName());return this.editor.fire("toDataFormat",{dataValue:a,filter:d||this.editor.filter,context:c,enterMode:e||this.editor.enterMode}).dataValue},protectSource:function(a){return v(a,this.editor)},unprotectSource:function(a){return F(a,
this.editor)},unprotectRealComments:function(a){return A(a)}};var L=/(?:&nbsp;|\xa0)$/,w="{cke_protected}",J=CKEDITOR.dtd,G="caption colgroup col thead tfoot tbody".split(" "),q=CKEDITOR.tools.extend({},J.$blockLimit,J.$block),u={elements:{input:h,textarea:h}},K={attributeNames:[[/^on/,"data-cke-pa-on"],[/^srcdoc/,"data-cke-pa-srcdoc"],[/^data-cke-expando$/,""]],elements:{iframe:function(a){if(a.attributes&&a.attributes.src){var b=a.attributes.src.toLowerCase().replace(/[^a-z]/gi,"");if(0===b.indexOf("javascript")||
0===b.indexOf("data"))a.attributes["data-cke-pa-src"]=a.attributes.src,delete a.attributes.src}}}},E={elements:{embed:function(a){var b=a.parent;if(b&&"object"==b.name){var c=b.attributes.width,b=b.attributes.height;c&&(a.attributes.width=c);b&&(a.attributes.height=b)}},a:function(a){var b=a.attributes;if(!(a.children.length||b.name||b.id||a.attributes["data-cke-saved-name"]))return!1}}},C={elementNames:[[/^cke:/,""],[/^\?xml:namespace$/,""]],attributeNames:[[/^data-cke-(saved|pa)-/,""],[/^data-cke-.*/,
""],["hidefocus",""]],elements:{$:function(a){var b=a.attributes;if(b){if(b["data-cke-temp"])return!1;for(var c=["name","href","src"],d,e=0;e<c.length;e++)d="data-cke-saved-"+c[e],d in b&&delete b[c[e]]}return a},table:function(a){a.children.slice(0).sort(function(a,b){var c,d;a.type==CKEDITOR.NODE_ELEMENT&&b.type==a.type&&(c=CKEDITOR.tools.indexOf(G,a.name),d=CKEDITOR.tools.indexOf(G,b.name));-1<c&&-1<d&&c!=d||(c=a.parent?a.getIndex():-1,d=b.parent?b.getIndex():-1);return c>d?1:-1})},param:function(a){a.children=
[];a.isEmpty=!0;return a},span:function(a){"Apple-style-span"==a.attributes["class"]&&delete a.name},html:function(a){delete a.attributes.contenteditable;delete a.attributes["class"]},body:function(a){delete a.attributes.spellcheck;delete a.attributes.contenteditable},style:function(a){var b=a.children[0];b&&b.value&&(b.value=CKEDITOR.tools.trim(b.value));a.attributes.type||(a.attributes.type="text/css")},title:function(a){var b=a.children[0];!b&&k(a,b=new CKEDITOR.htmlParser.text);b.value=a.attributes["data-cke-title"]||
""},input:p,textarea:p},attributes:{"class":function(a){return CKEDITOR.tools.ltrim(a.replace(/(?:^|\s+)cke_[^\s]*/g,""))||!1}}};CKEDITOR.env.ie&&(C.attributes.style=function(a){return a.replace(/(^|;)([^\:]+)/g,function(a){return a.toLowerCase()})});var t=/<(a|area|img|input|source)\b([^>]*)>/gi,B=/([\w-:]+)\s*=\s*(?:(?:"[^"]*")|(?:'[^']*')|(?:[^ "'>]+))/gi,z=/^(href|src|name)$/i,H=/(?:<style(?=[ >])[^>]*>[\s\S]*?<\/style>)|(?:<(:?link|meta|base)[^>]*>)/gi,N=/(<textarea(?=[ >])[^>]*>)([\s\S]*?)(?:<\/textarea>)/gi,
O=/<cke:encoded>([^<]*)<\/cke:encoded>/gi,r=/(<\/?)((?:object|embed|param|html|body|head|title)([\s][^>]*)?>)/gi,I=/(<\/?)cke:((?:html|body|head|title)[^>]*>)/gi,M=/<cke:(param|embed)([^>]*?)\/?>(?!\s*<\/cke:\1)/gi;l=function(){function a(b,c){for(var d=0;d<b.length;d++){var e=b[d];e.lastIndex=0;if(e.test(c))return!0}return!1}function b(a){return CKEDITOR.tools.array.reduce(a.split(""),function(a,b){var d=b.toLowerCase(),e=b.toUpperCase(),f=c(d);d!==e&&(f+="|"+c(e));return a+("("+f+")")},"")}function c(a){var b;
b=a.charCodeAt(0);var d=b.toString(16);b={htmlCode:"\x26#"+b+";?",hex:"\x26#x0*"+d+";?",entity:{"\x3c":"\x26lt;","\x3e":"\x26gt;",":":"\x26colon;"}[a]};for(var e in b)b[e]&&(a+="|"+b[e]);return a}var d=[new RegExp("("+b("\x3ccke:encoded\x3e")+"(.*?)"+b("\x3c/cke:encoded\x3e")+")|("+b("\x3c")+b("/")+"?"+b("cke:encoded\x3e")+")","gi"),new RegExp("(("+b("{cke_protected")+")(_[0-9]*)?"+b("}")+")","gi"),/<!(?:\s*-\s*){2,3}!?\s*>/g];return function(b){for(;a(d,b);)for(var c=d,e=0;e<c.length;e++)b=b.replace(c[e],
"");return b}}()})();"use strict";CKEDITOR.htmlParser.element=function(a,d){this.name=a;this.attributes=d||{};this.children=[];var b=a||"",c=b.match(/^cke:(.*)/);c&&(b=c[1]);b=!!(CKEDITOR.dtd.$nonBodyContent[b]||CKEDITOR.dtd.$block[b]||CKEDITOR.dtd.$listItem[b]||CKEDITOR.dtd.$tableContent[b]||CKEDITOR.dtd.$nonEditable[b]||"br"==b);this.isEmpty=!!CKEDITOR.dtd.$empty[a];this.isUnknown=!CKEDITOR.dtd[a];this._={isBlockLike:b,hasInlineStarted:this.isEmpty||!b}};
CKEDITOR.htmlParser.cssStyle=function(a){var d={};((a instanceof CKEDITOR.htmlParser.element?a.attributes.style:a)||"").replace(/&quot;/g,'"').replace(/\s*([^ :;]+)\s*:\s*([^;]+)\s*(?=;|$)/g,function(a,c,f){"font-family"==c&&(f=f.replace(/["']/g,""));d[c.toLowerCase()]=f});return{rules:d,populate:function(a){var c=this.toString();c&&(a instanceof CKEDITOR.dom.element?a.setAttribute("style",c):a instanceof CKEDITOR.htmlParser.element?a.attributes.style=c:a.style=c)},toString:function(){var a=[],c;
for(c in d)d[c]&&a.push(c,":",d[c],";");return a.join("")}}};
(function(){function a(a){return function(b){return b.type==CKEDITOR.NODE_ELEMENT&&("string"==typeof a?b.name==a:b.name in a)}}var d=function(a,b){a=a[0];b=b[0];return a<b?-1:a>b?1:0},b=CKEDITOR.htmlParser.fragment.prototype;CKEDITOR.htmlParser.element.prototype=CKEDITOR.tools.extend(new CKEDITOR.htmlParser.node,{type:CKEDITOR.NODE_ELEMENT,add:b.add,clone:function(){return new CKEDITOR.htmlParser.element(this.name,this.attributes)},filter:function(a,b){var d=this,k,h;b=d.getFilterContext(b);if(!d.parent)a.onRoot(b,
d);for(;;){k=d.name;if(!(h=a.onElementName(b,k)))return this.remove(),!1;d.name=h;if(!(d=a.onElement(b,d)))return this.remove(),!1;if(d!==this)return this.replaceWith(d),!1;if(d.name==k)break;if(d.type!=CKEDITOR.NODE_ELEMENT)return this.replaceWith(d),!1;if(!d.name)return this.replaceWithChildren(),!1}k=d.attributes;var p,m;for(p in k){for(h=k[p];;)if(m=a.onAttributeName(b,p))if(m!=p)delete k[p],p=m;else break;else{delete k[p];break}m&&(!1===(h=a.onAttribute(b,d,m,h))?delete k[m]:k[m]=h)}d.isEmpty||
this.filterChildren(a,!1,b);return!0},filterChildren:b.filterChildren,writeHtml:function(a,b){b&&this.filter(b);var e=this.name,k=[],h=this.attributes,p,m;a.openTag(e,h);for(p in h)k.push([p,h[p]]);a.sortAttributes&&k.sort(d);p=0;for(m=k.length;p<m;p++)h=k[p],a.attribute(h[0],h[1]);a.openTagClose(e,this.isEmpty);this.writeChildrenHtml(a);this.isEmpty||a.closeTag(e)},writeChildrenHtml:b.writeChildrenHtml,replaceWithChildren:function(){for(var a=this.children,b=a.length;b;)a[--b].insertAfter(this);
this.remove()},forEach:b.forEach,getFirst:function(b){if(!b)return this.children.length?this.children[0]:null;"function"!=typeof b&&(b=a(b));for(var d=0,e=this.children.length;d<e;++d)if(b(this.children[d]))return this.children[d];return null},getHtml:function(){var a=new CKEDITOR.htmlParser.basicWriter;this.writeChildrenHtml(a);return a.getHtml()},setHtml:function(a){a=this.children=CKEDITOR.htmlParser.fragment.fromHtml(a).children;for(var b=0,d=a.length;b<d;++b)a[b].parent=this},getOuterHtml:function(){var a=
new CKEDITOR.htmlParser.basicWriter;this.writeHtml(a);return a.getHtml()},split:function(a){for(var b=this.children.splice(a,this.children.length-a),d=this.clone(),k=0;k<b.length;++k)b[k].parent=d;d.children=b;b[0]&&(b[0].previous=null);0<a&&(this.children[a-1].next=null);this.parent.add(d,this.getIndex()+1);return d},find:function(a,b){void 0===b&&(b=!1);var d=[],k;for(k=0;k<this.children.length;k++){var h=this.children[k];"function"==typeof a&&a(h)?d.push(h):"string"==typeof a&&h.name===a&&d.push(h);
b&&h.find&&(d=d.concat(h.find(a,b)))}return d},findOne:function(a,b){var d=null,k=CKEDITOR.tools.array.find(this.children,function(h){var k="function"===typeof a?a(h):h.name===a;if(k||!b)return k;h.children&&h.findOne&&(d=h.findOne(a,!0));return!!d});return d||k||null},addClass:function(a){if(!this.hasClass(a)){var b=this.attributes["class"]||"";this.attributes["class"]=b+(b?" ":"")+a}},removeClass:function(a){var b=this.attributes["class"];b&&((b=CKEDITOR.tools.trim(b.replace(new RegExp("(?:\\s+|^)"+
a+"(?:\\s+|$)")," ")))?this.attributes["class"]=b:delete this.attributes["class"])},hasClass:function(a){var b=this.attributes["class"];return b?(new RegExp("(?:^|\\s)"+a+"(?\x3d\\s|$)")).test(b):!1},getFilterContext:function(a){var b=[];a||(a={nonEditable:!1,nestedEditable:!1});a.nonEditable||"false"!=this.attributes.contenteditable?a.nonEditable&&!a.nestedEditable&&"true"==this.attributes.contenteditable&&b.push("nestedEditable",!0):b.push("nonEditable",!0);if(b.length){a=CKEDITOR.tools.copy(a);
for(var d=0;d<b.length;d+=2)a[b[d]]=b[d+1]}return a}},!0)})();(function(){var a=/{([^}]+)}/g;CKEDITOR.template=function(a){this.source="function"===typeof a?a:String(a)};CKEDITOR.template.prototype.output=function(d,b){var c=("function"===typeof this.source?this.source(d):this.source).replace(a,function(a,b){return void 0!==d[b]?d[b]:a});return b?b.push(c):c}})();delete CKEDITOR.loadFullCore;CKEDITOR.instances={};CKEDITOR.document=new CKEDITOR.dom.document(document);
CKEDITOR.add=function(a){function d(){CKEDITOR.currentInstance==a&&(CKEDITOR.currentInstance=null,CKEDITOR.fire("currentInstance"))}CKEDITOR.instances[a.name]=a;a.on("focus",function(){CKEDITOR.currentInstance!=a&&(CKEDITOR.currentInstance=a,CKEDITOR.fire("currentInstance"))});a.on("blur",d);a.on("destroy",d);CKEDITOR.fire("instance",null,a)};CKEDITOR.remove=function(a){delete CKEDITOR.instances[a.name]};
(function(){var a={};CKEDITOR.addTemplate=function(d,b){var c=a[d];if(c)return c;c={name:d,source:b};CKEDITOR.fire("template",c);return a[d]=new CKEDITOR.template(c.source)};CKEDITOR.getTemplate=function(d){return a[d]}})();(function(){var a=[];CKEDITOR.addCss=function(d){a.push(d)};CKEDITOR.getCss=function(){return a.join("\n")}})();CKEDITOR.on("instanceDestroyed",function(){CKEDITOR.tools.isEmpty(this.instances)&&CKEDITOR.fire("reset")});CKEDITOR.TRISTATE_ON=1;CKEDITOR.TRISTATE_OFF=2;
CKEDITOR.TRISTATE_DISABLED=0;
(function(){CKEDITOR.inline=function(a,d){a=CKEDITOR.editor._getEditorElement(a);if(!a)return null;if(CKEDITOR.editor.shouldDelayEditorCreation(a,d))return CKEDITOR.editor.initializeDelayedEditorCreation(a,d,"inline");var b=a.is("textarea")?a:null,c=b?b.getValue():a.getHtml(),f=new CKEDITOR.editor(d,a,CKEDITOR.ELEMENT_MODE_INLINE);b?(f.setData(c,null,!0),a=CKEDITOR.dom.element.createFromHtml('\x3cdiv contenteditable\x3d"'+!!f.readOnly+'" class\x3d"cke_textarea_inline"\x3e'+b.getValue()+"\x3c/div\x3e",
CKEDITOR.document),a.insertAfter(b),b.hide(),b.$.form&&f._attachToForm()):(d&&"undefined"!==typeof d.readOnly&&!d.readOnly&&a.setAttribute("contenteditable","true"),f.setData(c,null,!0));f.on("loaded",function(){f.fire("uiReady");f.editable(a);f.container=a;f.ui.contentsElement=a;f.setData(f.getData(1));f.resetDirty();f.fire("contentDom");f.mode="wysiwyg";f.fire("mode");f.status="ready";f.fireOnce("instanceReady");CKEDITOR.fire("instanceReady",null,f)},null,null,1E4);f.on("destroy",function(){var a=
f.container;b&&a&&(a.clearCustomData(),a.remove());b&&b.show();f.element.clearCustomData();delete f.element});return f};CKEDITOR.inlineAll=function(){var a,d,b;for(b in CKEDITOR.dtd.$editable)for(var c=CKEDITOR.document.getElementsByTag(b),f=0,e=c.count();f<e;f++)a=c.getItem(f),"true"!=a.getAttribute("contenteditable")||a.getEditor()||(d={element:a,config:{}},!1!==CKEDITOR.fire("inline",d)&&CKEDITOR.inline(a,d.config))};CKEDITOR.domReady(function(){!CKEDITOR.disableAutoInline&&CKEDITOR.inlineAll()})})();
CKEDITOR.replaceClass="ckeditor";
(function(){function a(a,f,e,k){a=CKEDITOR.editor._getEditorElement(a);if(!a)return null;if(CKEDITOR.editor.shouldDelayEditorCreation(a,f))return CKEDITOR.editor.initializeDelayedEditorCreation(a,f,"replace");var h=new CKEDITOR.editor(f,a,k);k==CKEDITOR.ELEMENT_MODE_REPLACE&&(a.setStyle("visibility","hidden"),h._.required=a.hasAttribute("required"),a.removeAttribute("required"));e&&h.setData(e,null,!0);h.on("loaded",function(){h.isDestroyed()||h.isDetached()||(b(h),k==CKEDITOR.ELEMENT_MODE_REPLACE&&
h.config.autoUpdateElement&&a.$.form&&h._attachToForm(),h.setMode(h.config.startupMode,function(){h.resetDirty();h.status="ready";h.fireOnce("instanceReady");CKEDITOR.fire("instanceReady",null,h)}))});h.on("destroy",d);return h}function d(){var a=this.container,b=this.element;a&&(a.clearCustomData(),a.remove());b&&(b.clearCustomData(),this.elementMode==CKEDITOR.ELEMENT_MODE_REPLACE&&(b.show(),this._.required&&b.setAttribute("required","required")),delete this.element)}function b(a){var b=a.name,d=
a.element,k=a.elementMode,h=a.fire("uiSpace",{space:"top",html:""}).html,p=a.fire("uiSpace",{space:"bottom",html:""}).html,m=new CKEDITOR.template('\x3c{outerEl} id\x3d"cke_{name}" class\x3d"{id} cke cke_reset cke_chrome cke_editor_{name} cke_{langDir} '+CKEDITOR.env.cssClass+'"  dir\x3d"{langDir}" lang\x3d"{langCode}" role\x3d"application"'+(a.applicationTitle?' aria-labelledby\x3d"cke_{name}_arialbl"':"")+"\x3e"+(a.applicationTitle?'\x3cspan id\x3d"cke_{name}_arialbl" class\x3d"cke_voice_label"\x3e{voiceLabel}\x3c/span\x3e':
"")+'\x3c{outerEl} class\x3d"cke_inner cke_reset" role\x3d"presentation"\x3e{topHtml}\x3c{outerEl} id\x3d"{contentId}" class\x3d"cke_contents cke_reset" role\x3d"presentation"\x3e\x3c/{outerEl}\x3e{bottomHtml}\x3c/{outerEl}\x3e\x3c/{outerEl}\x3e'),b=CKEDITOR.dom.element.createFromHtml(m.output({id:a.id,name:b,langDir:a.lang.dir,langCode:a.langCode,voiceLabel:a.applicationTitle,topHtml:h?'\x3cspan id\x3d"'+a.ui.spaceId("top")+'" class\x3d"cke_top cke_reset_all" role\x3d"presentation" style\x3d"height:auto"\x3e'+
h+"\x3c/span\x3e":"",contentId:a.ui.spaceId("contents"),bottomHtml:p?'\x3cspan id\x3d"'+a.ui.spaceId("bottom")+'" class\x3d"cke_bottom cke_reset_all" role\x3d"presentation"\x3e'+p+"\x3c/span\x3e":"",outerEl:CKEDITOR.env.ie?"span":"div"}));k==CKEDITOR.ELEMENT_MODE_REPLACE?(d.hide(),b.insertAfter(d)):d.append(b);a.container=b;a.ui.contentsElement=a.ui.space("contents");h&&a.ui.space("top").unselectable();p&&a.ui.space("bottom").unselectable();d=a.config.width;k=a.config.height;d&&b.setStyle("width",
CKEDITOR.tools.cssLength(d));k&&a.ui.space("contents").setStyle("height",CKEDITOR.tools.cssLength(k));b.disableContextMenu();CKEDITOR.env.webkit&&b.on("focus",function(){a.focus()});a.fireOnce("uiReady")}CKEDITOR.replace=function(b,d){return a(b,d,null,CKEDITOR.ELEMENT_MODE_REPLACE)};CKEDITOR.appendTo=function(b,d,e){return a(b,d,e,CKEDITOR.ELEMENT_MODE_APPENDTO)};CKEDITOR.replaceAll=function(){for(var a=document.getElementsByTagName("textarea"),b=0;b<a.length;b++){var d=null,k=a[b];if(k.name||k.id){if("string"==
typeof arguments[0]){if(!(new RegExp("(?:^|\\s)"+arguments[0]+"(?:$|\\s)")).test(k.className))continue}else if("function"==typeof arguments[0]&&(d={},!1===arguments[0](k,d)))continue;this.replace(k,d)}}};CKEDITOR.editor.prototype.addMode=function(a,b){(this._.modes||(this._.modes={}))[a]=b};CKEDITOR.editor.prototype.setMode=function(a,b){var d=this,k=this._.modes;if(a!=d.mode&&k&&k[a]){d.fire("beforeSetMode",a);if(d.mode){var h=d.checkDirty(),k=d._.previousModeData,p,m=0;d.fire("beforeModeUnload");
d.editable(0);d._.previousMode=d.mode;d._.previousModeData=p=d.getData(1);"source"==d.mode&&k==p&&(d.fire("lockSnapshot",{forceUpdate:!0}),m=1);d.ui.space("contents").setHtml("");d.mode=""}else d._.previousModeData=d.getData(1);this._.modes[a](function(){d.mode=a;void 0!==h&&!h&&d.resetDirty();m?d.fire("unlockSnapshot"):"wysiwyg"==a&&d.fire("saveSnapshot");setTimeout(function(){d.isDestroyed()||d.isDetached()||(d.fire("mode"),b&&b.call(d))},0)})}};CKEDITOR.editor.prototype.resize=function(a,b,d,k){var h=
this.container,p=this.ui.space("contents"),m=CKEDITOR.env.webkit&&this.document&&this.document.getWindow().$.frameElement;k=k?this.container.getFirst(function(a){return a.type==CKEDITOR.NODE_ELEMENT&&a.hasClass("cke_inner")}):h;if(a||0===a)a=CKEDITOR.tools.convertToPx(CKEDITOR.tools.cssLength(a));k.setSize("width",a,!0);m&&(m.style.width="1%");b=CKEDITOR.tools.convertToPx(CKEDITOR.tools.cssLength(b));var g=(k.$.offsetHeight||0)-(p.$.clientHeight||0),h=Math.max(b-(d?0:g),0);b=d?b+g:b;p.setStyle("height",
CKEDITOR.tools.cssLength(h));m&&(m.style.width="100%");this.fire("resize",{outerHeight:b,contentsHeight:h,outerWidth:a||k.getSize("width")})};CKEDITOR.editor.prototype.getResizable=function(a){return a?this.ui.space("contents"):this.container};CKEDITOR.domReady(function(){CKEDITOR.replaceClass&&CKEDITOR.replaceAll(CKEDITOR.replaceClass)})})();CKEDITOR.config.startupMode="wysiwyg";
(function(){function a(a){var b=a.editor,e=a.data.path,g=e.blockLimit,f=a.data.selection,h=f.getRanges()[0],k;if(CKEDITOR.env.gecko||CKEDITOR.env.ie&&CKEDITOR.env.needsBrFiller)if(f=d(f,e))f.appendBogus(),k=CKEDITOR.env.ie&&!CKEDITOR.env.edge||CKEDITOR.env.edge&&b._.previousActive;p(b,e.block,g)&&h.collapsed&&!h.getCommonAncestor().isReadOnly()&&(e=h.clone(),e.enlarge(CKEDITOR.ENLARGE_BLOCK_CONTENTS),g=new CKEDITOR.dom.walker(e),g.guard=function(a){return!c(a)||a.type==CKEDITOR.NODE_COMMENT||a.isReadOnly()},
!g.checkForward()||e.checkStartOfBlock()&&e.checkEndOfBlock())&&(b=h.fixBlock(!0,b.activeEnterMode==CKEDITOR.ENTER_DIV?"div":"p"),CKEDITOR.env.needsBrFiller||(b=b.getFirst(c))&&b.type==CKEDITOR.NODE_TEXT&&CKEDITOR.tools.trim(b.getText()).match(/^(?:&nbsp;|\xa0)$/)&&b.remove(),k=1,a.cancel());k&&h.select()}function d(a,b){if(a.isFake)return 0;var d=b.block||b.blockLimit,e=d&&d.getLast(c);if(!(!d||!d.isBlockBoundary()||e&&e.type==CKEDITOR.NODE_ELEMENT&&e.isBlockBoundary()||d.is("pre")||d.getBogus()))return d}
function b(a){var b=a.data.getTarget();b.is("input")&&(b=b.getAttribute("type"),"submit"!=b&&"reset"!=b||a.data.preventDefault())}function c(a){return A(a)&&F(a)}function f(a,b){return function(c){var d=c.data.$.toElement||c.data.$.fromElement||c.data.$.relatedTarget;(d=d&&d.nodeType==CKEDITOR.NODE_ELEMENT?new CKEDITOR.dom.element(d):null)&&(b.equals(d)||b.contains(d))||a.call(this,c)}}function e(a){return!!a.getRanges()[0].startPath().contains({table:1,ul:1,ol:1,dl:1})}function k(a){function b(a){var e=
{table:1,ul:1,ol:1,dl:1};return function(b,g){g&&b.type==CKEDITOR.NODE_ELEMENT&&b.is(e)&&(d=b);if(!(g||!c(b)||a&&l(b)))return!1}}var d,g=a.getRanges()[0],f=a.root;return e(a)&&(a=g.clone(),a.collapse(1),a.setStartAt(f,CKEDITOR.POSITION_AFTER_START),f=new CKEDITOR.dom.walker(a),f.guard=b(),f.checkBackward(),d)?(a=g.clone(),a.collapse(),a.setEndAt(d,CKEDITOR.POSITION_AFTER_END),f=new CKEDITOR.dom.walker(a),f.guard=b(!0),d=!1,f.checkForward(),d):null}function h(a){return a.block.getParent().getChildCount()}
function p(a,b,c){return!1!==a.config.autoParagraph&&a.activeEnterMode!=CKEDITOR.ENTER_BR&&(a.editable().equals(c)&&!b||b&&"true"==b.getAttribute("contenteditable"))}function m(a){return a.activeEnterMode!=CKEDITOR.ENTER_BR&&!1!==a.config.autoParagraph?a.activeEnterMode==CKEDITOR.ENTER_DIV?"div":"p":!1}function g(a){a&&a.isEmptyInlineRemoveable()&&a.remove()}function x(a){var b=a.editor;b.getSelection().scrollIntoView();setTimeout(function(){b.fire("saveSnapshot")},0)}function y(a,b,c){var d=a.getCommonAncestor(b);
for(b=a=c?b:a;(a=a.getParent())&&!d.equals(a)&&1==a.getChildCount();)b=a;b.remove()}var A,F,v,l,L,w,J,G,q,u,K={ul:1,ol:1,dl:1};CKEDITOR.editable=CKEDITOR.tools.createClass({base:CKEDITOR.dom.element,$:function(a,b){this.base(b.$||b);this.editor=a;this.status="unloaded";this.hasFocus=!1;this.setup()},proto:{focus:function(){var a;if(CKEDITOR.env.webkit&&!this.hasFocus&&(a=this.editor._.previousActive||this.getDocument().getActive(),this.contains(a))){a.focus();return}CKEDITOR.env.edge&&14<CKEDITOR.env.version&&
!this.hasFocus&&this.getDocument().equals(CKEDITOR.document)&&(this.editor._.previousScrollTop=this.$.scrollTop);try{if(!CKEDITOR.env.ie||CKEDITOR.env.edge&&14<CKEDITOR.env.version||!this.getDocument().equals(CKEDITOR.document))if(CKEDITOR.env.chrome){var b=this.$.scrollTop;this.$.focus();this.$.scrollTop=b}else this.$.focus();else this.$.setActive()}catch(c){if(!CKEDITOR.env.ie)throw c;}CKEDITOR.env.safari&&!this.isInline()&&(a=CKEDITOR.document.getActive(),a.equals(this.getWindow().getFrame())||
this.getWindow().focus())},on:function(a,b){var c=Array.prototype.slice.call(arguments,0);CKEDITOR.env.ie&&/^focus|blur$/.exec(a)&&(a="focus"==a?"focusin":"focusout",b=f(b,this),c[0]=a,c[1]=b);return CKEDITOR.dom.element.prototype.on.apply(this,c)},attachListener:function(a){!this._.listeners&&(this._.listeners=[]);var b=Array.prototype.slice.call(arguments,1),b=a.on.apply(a,b);this._.listeners.push(b);return b},clearListeners:function(){var a=this._.listeners;try{for(;a.length;)a.pop().removeListener()}catch(b){}},
restoreAttrs:function(){var a=this._.attrChanges,b,c;for(c in a)a.hasOwnProperty(c)&&(b=a[c],null!==b?this.setAttribute(c,b):this.removeAttribute(c))},attachClass:function(a){var b=this.getCustomData("classes");this.hasClass(a)||(!b&&(b=[]),b.push(a),this.setCustomData("classes",b),this.addClass(a))},changeAttr:function(a,b){var c=this.getAttribute(a);b!==c&&(!this._.attrChanges&&(this._.attrChanges={}),a in this._.attrChanges||(this._.attrChanges[a]=c),this.setAttribute(a,b))},insertText:function(a){this.editor.focus();
this.insertHtml(this.transformPlainTextToHtml(a),"text")},transformPlainTextToHtml:function(a){var b=this.editor.getSelection().getStartElement().hasAscendant("pre",!0)?CKEDITOR.ENTER_BR:this.editor.activeEnterMode;return CKEDITOR.tools.transformPlainTextToHtml(a,b)},insertHtml:function(a,b,c){var d=this.editor;d.focus();d.fire("saveSnapshot");c||(c=d.getSelection().getRanges()[0]);w(this,b||"html",a,c);c.select();x(this);this.editor.fire("afterInsertHtml",{})},insertHtmlIntoRange:function(a,b,c){w(this,
c||"html",a,b);this.editor.fire("afterInsertHtml",{intoRange:b})},insertElement:function(a,b){var d=this.editor;d.focus();d.fire("saveSnapshot");var e=d.activeEnterMode,d=d.getSelection(),g=a.getName(),g=CKEDITOR.dtd.$block[g];b||(b=d.getRanges()[0]);this.insertElementIntoRange(a,b)&&(b.moveToPosition(a,CKEDITOR.POSITION_AFTER_END),g&&((g=a.getNext(function(a){return c(a)&&!l(a)}))&&g.type==CKEDITOR.NODE_ELEMENT&&g.is(CKEDITOR.dtd.$block)?g.getDtd()["#"]?b.moveToElementEditStart(g):b.moveToElementEditEnd(a):
g||e==CKEDITOR.ENTER_BR||(g=b.fixBlock(!0,e==CKEDITOR.ENTER_DIV?"div":"p"),b.moveToElementEditStart(g))));d.selectRanges([b]);x(this)},insertElementIntoSelection:function(a){this.insertElement(a)},insertElementIntoRange:function(a,b){var c=this.editor,d=c.config.enterMode,e=a.getName(),f=CKEDITOR.dtd.$block[e];if(b.checkReadOnly())return!1;b.deleteContents(1);b.startContainer.type==CKEDITOR.NODE_ELEMENT&&(b.startContainer.is({tr:1,table:1,tbody:1,thead:1,tfoot:1})?J(b):b.startContainer.is(CKEDITOR.dtd.$list)&&
G(b));var h,k;if(f)for(;(h=b.getCommonAncestor(0,1))&&(k=CKEDITOR.dtd[h.getName()])&&(!k||!k[e]);)if(h.getName()in CKEDITOR.dtd.span){var f=b.splitElement(h),r=b.createBookmark();g(h);g(f);b.moveToBookmark(r)}else b.checkStartOfBlock()&&b.checkEndOfBlock()?(b.setStartBefore(h),b.collapse(!0),h.remove()):b.splitBlock(d==CKEDITOR.ENTER_DIV?"div":"p",c.editable());b.insertNode(a);return!0},setData:function(a,b){b||(a=this.editor.dataProcessor.toHtml(a));this.setHtml(a);this.fixInitialSelection();"unloaded"==
this.status&&(this.status="ready");this.editor.fire("dataReady")},getData:function(a){var b=this.getHtml();a||(b=this.editor.dataProcessor.toDataFormat(b));return b},setReadOnly:function(a){this.setAttribute("contenteditable",String(!a));this.setAttribute("aria-readonly",String(a))},detach:function(){this.status="detached";this.editor.setData(this.editor.getData(),{internal:!0});this.clearListeners();try{this._.cleanCustomData()}catch(a){if(!CKEDITOR.env.ie||-2146828218!==a.number)throw a;}this.editor.fire("contentDomUnload");
delete this.editor.document;delete this.editor.window;delete this.editor},isInline:function(){return this.getDocument().equals(CKEDITOR.document)},fixInitialSelection:function(){function a(){var b=c.getDocument().$,d=b.getSelection(),e;a:if(d.anchorNode&&d.anchorNode==c.$)e=!0;else{if(CKEDITOR.env.webkit&&(e=c.getDocument().getActive())&&e.equals(c)&&!d.anchorNode){e=!0;break a}e=void 0}e&&(e=new CKEDITOR.dom.range(c),e.moveToElementEditStart(c),b=b.createRange(),b.setStart(e.startContainer.$,e.startOffset),
b.collapse(!0),d.removeAllRanges(),d.addRange(b))}function b(){var a=c.getDocument().$,d=a.selection,e=c.getDocument().getActive();"None"==d.type&&e.equals(c)&&(d=new CKEDITOR.dom.range(c),a=a.body.createTextRange(),d.moveToElementEditStart(c),d=d.startContainer,d.type!=CKEDITOR.NODE_ELEMENT&&(d=d.getParent()),a.moveToElementText(d.$),a.collapse(!0),a.select())}var c=this;if(CKEDITOR.env.ie&&(9>CKEDITOR.env.version||CKEDITOR.env.quirks))this.hasFocus&&(this.focus(),b());else if(this.hasFocus)this.focus(),
a();else this.once("focus",function(){a()},null,null,-999)},getHtmlFromRange:function(a){if(a.collapsed)return new CKEDITOR.dom.documentFragment(a.document);a={doc:this.getDocument(),range:a.clone()};q.eol.detect(a,this);q.bogus.exclude(a);q.cell.shrink(a);a.fragment=a.range.cloneContents();q.tree.rebuild(a,this);q.eol.fix(a,this);return new CKEDITOR.dom.documentFragment(a.fragment.$)},extractHtmlFromRange:function(a,b){var c=u,d={range:a,doc:a.document},e=this.getHtmlFromRange(a);if(a.collapsed)return a.optimize(),
e;a.enlarge(CKEDITOR.ENLARGE_INLINE,1);c.table.detectPurge(d);d.bookmark=a.createBookmark();delete d.range;var g=this.editor.createRange();g.moveToPosition(d.bookmark.startNode,CKEDITOR.POSITION_BEFORE_START);d.targetBookmark=g.createBookmark();c.list.detectMerge(d,this);c.table.detectRanges(d,this);c.block.detectMerge(d,this);d.tableContentsRanges?(c.table.deleteRanges(d),a.moveToBookmark(d.bookmark),d.range=a):(a.moveToBookmark(d.bookmark),d.range=a,a.extractContents(c.detectExtractMerge(d)));a.moveToBookmark(d.targetBookmark);
a.optimize();c.fixUneditableRangePosition(a);c.list.merge(d,this);c.table.purge(d,this);c.block.merge(d,this);if(b){c=a.startPath();if(d=a.checkStartOfBlock()&&a.checkEndOfBlock()&&c.block&&!a.root.equals(c.block)){a:{var d=c.block.getElementsByTag("span"),g=0,f;if(d)for(;f=d.getItem(g++);)if(!F(f)){d=!0;break a}d=!1}d=!d}d&&(a.moveToPosition(c.block,CKEDITOR.POSITION_BEFORE_START),c.block.remove())}else c.autoParagraph(this.editor,a),v(a.startContainer)&&a.startContainer.appendBogus();a.startContainer.mergeSiblings();
return e},setup:function(){var a=this.editor;this.attachListener(a,"beforeGetData",function(){var b=this.getData();this.is("textarea")||!1!==a.config.ignoreEmptyParagraph&&(b=b.replace(L,function(a,b){return b}));a.setData(b,null,1)},this);this.attachListener(a,"getSnapshot",function(a){a.data=this.getData(1)},this);this.attachListener(a,"afterSetData",function(){this.setData(a.getData(1))},this);this.attachListener(a,"loadSnapshot",function(a){this.setData(a.data,1)},this);this.attachListener(a,
"beforeFocus",function(){var b=a.getSelection();(b=b&&b.getNative())&&"Control"==b.type||this.focus()},this);this.attachListener(a,"insertHtml",function(a){this.insertHtml(a.data.dataValue,a.data.mode,a.data.range)},this);this.attachListener(a,"insertElement",function(a){this.insertElement(a.data)},this);this.attachListener(a,"insertText",function(a){this.insertText(a.data)},this);this.setReadOnly(a.readOnly);this.attachClass("cke_editable");a.elementMode==CKEDITOR.ELEMENT_MODE_INLINE?this.attachClass("cke_editable_inline"):
a.elementMode!=CKEDITOR.ELEMENT_MODE_REPLACE&&a.elementMode!=CKEDITOR.ELEMENT_MODE_APPENDTO||this.attachClass("cke_editable_themed");this.attachClass("cke_contents_"+a.config.contentsLangDirection);a.keystrokeHandler.blockedKeystrokes[8]=+a.readOnly;a.keystrokeHandler.attach(this);this.on("blur",function(){this.hasFocus=!1},null,null,-1);this.on("focus",function(){this.hasFocus=!0},null,null,-1);if(CKEDITOR.env.webkit)this.on("scroll",function(){a._.previousScrollTop=a.editable().$.scrollTop},null,
null,-1);if(CKEDITOR.env.edge&&14<CKEDITOR.env.version){var d=function(){var b=a.editable();null!=a._.previousScrollTop&&b.getDocument().equals(CKEDITOR.document)&&(b.$.scrollTop=a._.previousScrollTop,a._.previousScrollTop=null,this.removeListener("scroll",d))};this.on("scroll",d)}a.focusManager.add(this);this.equals(CKEDITOR.document.getActive())&&(this.hasFocus=!0,a.once("contentDom",function(){a.focusManager.focus(this)},this));this.isInline()&&this.changeAttr("tabindex",a.tabIndex);if(!this.is("textarea")){a.document=
this.getDocument();a.window=this.getWindow();var g=a.document;this.changeAttr("spellcheck",!a.config.disableNativeSpellChecker);var f=a.config.contentsLangDirection;this.getDirection(1)!=f&&this.changeAttr("dir",f);var z=CKEDITOR.getCss();if(z){var f=g.getHead(),H=f.getCustomData("stylesheet");H?z!=H.getText()&&(CKEDITOR.env.ie&&9>CKEDITOR.env.version?H.$.styleSheet.cssText=z:H.setText(z)):(z=g.appendStyleText(z),z=new CKEDITOR.dom.element(z.ownerNode||z.owningElement),f.setCustomData("stylesheet",
z),z.data("cke-temp",1))}f=g.getCustomData("stylesheet_ref")||0;g.setCustomData("stylesheet_ref",f+1);this.setCustomData("cke_includeReadonly",!a.config.disableReadonlyStyling);this.attachListener(this,"click",function(a){a=a.data;var b=(new CKEDITOR.dom.elementPath(a.getTarget(),this)).contains("a");b&&2!=a.$.button&&b.isReadOnly()&&a.preventDefault()});var N={8:1,46:1};this.attachListener(a,"key",function(b){if(a.readOnly)return!0;var c=b.data.domEvent.getKey(),d,g=a.getSelection();if(0!==g.getRanges().length){if(c in
N){var f;b=g.getRanges()[0];var t=b.startPath(),z,B,H,c=8==c,m=!1;if(CKEDITOR.env.ie&&11>CKEDITOR.env.version&&g.getSelectedElement())f=g.getSelectedElement();else if(e(g)){var l=new CKEDITOR.dom.walker(b),p=b.collapsed?b.startContainer:l.next(),m=!1,C;if(b.checkStartOfBlock()){C=b.startPath().block||b.startPath().blockLimit;var q=C.getName();C=-1!==CKEDITOR.tools.array.indexOf(["dd","dt","li"],q)&&null===C.getPrevious()}else C=!1;if(C){for(;p&&!m;)m=p.$.nodeName.toLowerCase(),m=!!K[m],p=l.next();
l=h(b.startPath());p=h(b.endPath());m=m||l!==p}else m=void 0;m||(f=k(g))}f||m?(a.fire("saveSnapshot"),m?((d=b.startContainer.getAscendant(K,!0))?(b.setStart(d,0),b.enlarge(CKEDITOR.ENLARGE_ELEMENT),f=b):f=null,f.deleteContents()):(b.moveToPosition(f,CKEDITOR.POSITION_BEFORE_START),f.remove()),b.select(),a.fire("saveSnapshot"),d=1):b.collapsed&&((z=t.block)&&(H=z[c?"getPrevious":"getNext"](A))&&H.type==CKEDITOR.NODE_ELEMENT&&H.is("table")&&b[c?"checkStartOfBlock":"checkEndOfBlock"]()?(a.fire("saveSnapshot"),
b[c?"checkEndOfBlock":"checkStartOfBlock"]()&&z.remove(),b["moveToElementEdit"+(c?"End":"Start")](H),b.select(),a.fire("saveSnapshot"),d=1):t.blockLimit&&t.blockLimit.is("td")&&(B=t.blockLimit.getAscendant("table"))&&b.checkBoundaryOfElement(B,c?CKEDITOR.START:CKEDITOR.END)&&(H=B[c?"getPrevious":"getNext"](A))?(a.fire("saveSnapshot"),b["moveToElementEdit"+(c?"End":"Start")](H),b.checkStartOfBlock()&&b.checkEndOfBlock()?H.remove():b.select(),a.fire("saveSnapshot"),d=1):(B=t.contains(["td","th","caption"]))&&
b.checkBoundaryOfElement(B,c?CKEDITOR.START:CKEDITOR.END)&&(d=1))}return!d}});a.blockless&&CKEDITOR.env.ie&&CKEDITOR.env.needsBrFiller&&this.attachListener(this,"keyup",function(b){b.data.getKeystroke()in N&&!this.getFirst(c)&&(this.appendBogus(),b=a.createRange(),b.moveToPosition(this,CKEDITOR.POSITION_AFTER_START),b.select())});this.attachListener(this,"dblclick",function(b){if(a.readOnly)return!1;b={element:b.data.getTarget()};a.fire("doubleclick",b)});CKEDITOR.env.ie&&this.attachListener(this,
"click",b);CKEDITOR.env.ie&&!CKEDITOR.env.edge||this.attachListener(this,"mousedown",function(b){var c=b.data.getTarget();c.is("img","hr","input","textarea","select")&&!c.isReadOnly()&&(a.getSelection().selectElement(c),c.is("input","textarea","select")&&b.data.preventDefault())});CKEDITOR.env.edge&&this.attachListener(this,"mouseup",function(b){(b=b.data.getTarget())&&b.is("img")&&!b.isReadOnly()&&a.getSelection().selectElement(b)});CKEDITOR.env.gecko&&this.attachListener(this,"mouseup",function(b){if(2==
b.data.$.button&&(b=b.data.getTarget(),!b.getAscendant("table")&&!b.getOuterHtml().replace(L,""))){var c=a.createRange();c.moveToElementEditStart(b);c.select(!0)}});CKEDITOR.env.webkit&&(this.attachListener(this,"click",function(a){a.data.getTarget().is("input","select")&&a.data.preventDefault()}),this.attachListener(this,"mouseup",function(a){a.data.getTarget().is("input","textarea")&&a.data.preventDefault()}));CKEDITOR.env.webkit&&this.attachListener(a,"key",function(b){if(a.readOnly)return!0;var c=
b.data.domEvent.getKey();if(c in N&&(b=a.getSelection(),0!==b.getRanges().length)){var c=8==c,d=b.getRanges()[0];b=d.startPath();if(d.collapsed)a:{var e=b.block;if(e&&d[c?"checkStartOfBlock":"checkEndOfBlock"](!0)&&d.moveToClosestEditablePosition(e,!c)&&d.collapsed){if(d.startContainer.type==CKEDITOR.NODE_ELEMENT){var g=d.startContainer.getChild(d.startOffset-(c?1:0));if(g&&g.type==CKEDITOR.NODE_ELEMENT&&g.is("hr")){a.fire("saveSnapshot");g.remove();b=!0;break a}}d=d.startPath().block;if(!d||d&&d.contains(e))b=
void 0;else{a.fire("saveSnapshot");var f;(f=(c?d:e).getBogus())&&f.remove();f=a.getSelection();g=f.createBookmarks();(c?e:d).moveChildren(c?d:e,!1);b.lastElement.mergeSiblings();y(e,d,!c);f.selectBookmarks(g);b=!0}}else b=!1}else c=d,f=b.block,d=c.endPath().block,f&&d&&!f.equals(d)?(a.fire("saveSnapshot"),(e=f.getBogus())&&e.remove(),c.enlarge(CKEDITOR.ENLARGE_INLINE),c.deleteContents(),d.getParent()&&(d.moveChildren(f,!1),b.lastElement.mergeSiblings(),y(f,d,!0)),c=a.getSelection().getRanges()[0],
c.collapse(1),c.optimize(),""===c.startContainer.getHtml()&&c.startContainer.appendBogus(),c.select(),b=!0):b=!1;if(!b)return;a.getSelection().scrollIntoView();a.fire("saveSnapshot");return!1}},this,null,100)}},getUniqueId:function(){var a;try{this._.expandoNumber=a=CKEDITOR.dom.domObject.prototype.getUniqueId.call(this)}catch(b){a=this._&&this._.expandoNumber}return a}},_:{cleanCustomData:function(){this.removeClass("cke_editable");this.restoreAttrs();for(var a=this.removeCustomData("classes");a&&
a.length;)this.removeClass(a.pop());if(!this.is("textarea")){var a=this.getDocument(),b=a.getHead();if(b.getCustomData("stylesheet")){var c=a.getCustomData("stylesheet_ref");--c?a.setCustomData("stylesheet_ref",c):(a.removeCustomData("stylesheet_ref"),b.removeCustomData("stylesheet").remove())}}}}});CKEDITOR.editor.prototype.editable=function(a){var b=this._.editable;if(b&&a)return 0;if(!arguments.length)return b;a?b=a instanceof CKEDITOR.editable?a:new CKEDITOR.editable(this,a):(b&&b.detach(),b=
null);return this._.editable=b};CKEDITOR.on("instanceLoaded",function(b){var c=b.editor;c.on("insertElement",function(a){a=a.data;a.type==CKEDITOR.NODE_ELEMENT&&(a.is("input")||a.is("textarea"))&&("false"!=a.getAttribute("contentEditable")&&a.data("cke-editable",a.hasAttribute("contenteditable")?"true":"1"),a.setAttribute("contentEditable",!1))});c.on("selectionChange",function(b){if(!c.readOnly){var d=c.getSelection();d&&!d.isLocked&&(d=c.checkDirty(),c.fire("lockSnapshot"),a(b),c.fire("unlockSnapshot"),
!d&&c.resetDirty())}})});CKEDITOR.on("instanceCreated",function(a){var b=a.editor;b.on("mode",function(){var a=b.editable();if(a&&a.isInline()){var c=b.title;a.changeAttr("role","textbox");a.changeAttr("aria-multiline","true");c&&a.changeAttr("aria-label",c);c&&a.changeAttr("title",c);var d=b.fire("ariaEditorHelpLabel",{}).label;if(d&&(c=this.ui.space(this.elementMode==CKEDITOR.ELEMENT_MODE_INLINE?"top":"contents"))){var e=CKEDITOR.tools.getNextId(),d=CKEDITOR.dom.element.createFromHtml('\x3cspan id\x3d"'+
e+'" class\x3d"cke_voice_label"\x3e'+d+"\x3c/span\x3e");c.append(d);a.changeAttr("aria-describedby",e)}}})});CKEDITOR.addCss(".cke_editable{cursor:text}.cke_editable img,.cke_editable input,.cke_editable textarea{cursor:default}");A=CKEDITOR.dom.walker.whitespaces(!0);F=CKEDITOR.dom.walker.bookmark(!1,!0);v=CKEDITOR.dom.walker.empty();l=CKEDITOR.dom.walker.bogus();L=/(^|<body\b[^>]*>)\s*<(p|div|address|h\d|center|pre)[^>]*>\s*(?:<br[^>]*>|&nbsp;|\u00A0|&#160;)?\s*(:?<\/\2>)?\s*(?=$|<\/body>)/gi;w=
function(){function a(b){return b.type==CKEDITOR.NODE_ELEMENT}function b(c,d){var e,g,f,h,r=[],t=d.range.startContainer;e=d.range.startPath();for(var t=l[t.getName()],k=0,z=c.getChildren(),B=z.count(),H=-1,I=-1,M=0,N=e.contains(l.$list);k<B;++k)e=z.getItem(k),a(e)?(f=e.getName(),N&&f in CKEDITOR.dtd.$list?r=r.concat(b(e,d)):(h=!!t[f],"br"!=f||!e.data("cke-eol")||k&&k!=B-1||(M=(g=k?r[k-1].node:z.getItem(k+1))&&(!a(g)||!g.is("br")),g=g&&a(g)&&l.$block[g.getName()]),-1!=H||h||(H=k),h||(I=k),r.push({isElement:1,
isLineBreak:M,isBlock:e.isBlockBoundary(),hasBlockSibling:g,node:e,name:f,allowed:h}),g=M=0)):r.push({isElement:0,node:e,allowed:1});-1<H&&(r[H].firstNotAllowed=1);-1<I&&(r[I].lastNotAllowed=1);return r}function d(b,c){var e=[],g=b.getChildren(),f=g.count(),r,h=0,k=l[c],z=!b.is(l.$inline)||b.is("br");for(z&&e.push(" ");h<f;h++)r=g.getItem(h),a(r)&&!r.is(k)?e=e.concat(d(r,c)):e.push(r);z&&e.push(" ");return e}function e(b){return a(b.startContainer)&&b.startContainer.getChild(b.startOffset-1)}function f(b){return b&&
a(b)&&(b.is(l.$removeEmpty)||b.is("a")&&!b.isBlockBoundary())}function h(b,c,d,e){var g=b.clone(),f,t;g.setEndAt(c,CKEDITOR.POSITION_BEFORE_END);(f=(new CKEDITOR.dom.walker(g)).next())&&a(f)&&r[f.getName()]&&(t=f.getPrevious())&&a(t)&&!t.getParent().equals(b.startContainer)&&d.contains(t)&&e.contains(f)&&f.isIdentical(t)&&(f.moveChildren(t),f.remove(),h(b,c,d,e))}function k(b,c){function d(b,c){if(c.isBlock&&c.isElement&&!c.node.is("br")&&a(b)&&b.is("br"))return b.remove(),1}var e=c.endContainer.getChild(c.endOffset),
g=c.endContainer.getChild(c.endOffset-1);e&&d(e,b[b.length-1]);g&&d(g,b[0])&&(c.setEnd(c.endContainer,c.endOffset-1),c.collapse())}var l=CKEDITOR.dtd,r={p:1,div:1,h1:1,h2:1,h3:1,h4:1,h5:1,h6:1,ul:1,ol:1,li:1,pre:1,dl:1,blockquote:1},I={p:1,div:1,h1:1,h2:1,h3:1,h4:1,h5:1,h6:1},M=CKEDITOR.tools.extend({},l.$inline);delete M.br;return function(r,D,R,q){var x=r.editor,v=!1,y;"unfiltered_html"==D&&(D="html",v=!0);if(!q.checkReadOnly()){var A=(new CKEDITOR.dom.elementPath(q.startContainer,q.root)).blockLimit||
q.root;D={type:D,dontFilter:v,editable:r,editor:x,range:q,blockLimit:A,mergeCandidates:[],zombies:[]};var v=D.range,A=D.mergeCandidates,u="html"===D.type,w,T,F,W,K;"text"==D.type&&v.shrink(CKEDITOR.SHRINK_ELEMENT,!0,!1)&&(T=CKEDITOR.dom.element.createFromHtml("\x3cspan\x3e\x26nbsp;\x3c/span\x3e",v.document),v.insertNode(T),v.setStartAfter(T));F=new CKEDITOR.dom.elementPath(v.startContainer);D.endPath=W=new CKEDITOR.dom.elementPath(v.endContainer);if(!v.collapsed){w=W.block||W.blockLimit;var Z=v.getCommonAncestor();
w&&!w.equals(Z)&&!w.contains(Z)&&v.checkEndOfBlock()&&D.zombies.push(w);v.deleteContents()}for(;(K=e(v))&&a(K)&&K.isBlockBoundary()&&F.contains(K);)v.moveToPosition(K,CKEDITOR.POSITION_BEFORE_END);h(v,D.blockLimit,F,W);T&&(v.setEndBefore(T),v.collapse(),T.remove());T=v.startPath();if(w=T.contains(f,!1,1))y=v.splitElement(w),D.inlineStylesRoot=w,D.inlineStylesPeak=T.lastElement;T=v.createBookmark();u&&(g(w),g(y));(w=T.startNode.getPrevious(c))&&a(w)&&f(w)&&A.push(w);(w=T.startNode.getNext(c))&&a(w)&&
f(w)&&A.push(w);for(w=T.startNode;(w=w.getParent())&&f(w);)A.push(w);v.moveToBookmark(T);y=r.getHtml();y=""===y||y.match(L);x.enterMode===CKEDITOR.ENTER_DIV&&y&&((x=r.getFirst())&&x.remove(),q.setStartAt(r,CKEDITOR.POSITION_AFTER_START),q.collapse(!0));if(r=R){r=D.range;if("text"==D.type&&D.inlineStylesRoot){q=D.inlineStylesPeak;x=q.getDocument().createText("{cke-peak}");for(y=D.inlineStylesRoot.getParent();!q.equals(y);)x=x.appendTo(q.clone()),q=q.getParent();R=x.getOuterHtml().split("{cke-peak}").join(R)}q=
D.blockLimit.getName();if(/^\s+|\s+$/.test(R)&&"span"in CKEDITOR.dtd[q]){var J='\x3cspan data-cke-marker\x3d"1"\x3e\x26nbsp;\x3c/span\x3e';R=J+R+J}R=D.editor.dataProcessor.toHtml(R,{context:null,fixForBody:!1,protectedWhitespaces:!!J,dontFilter:D.dontFilter,filter:D.editor.activeFilter,enterMode:D.editor.activeEnterMode});q=r.document.createElement("body");q.setHtml(R);J&&(q.getFirst().remove(),q.getLast().remove());if((J=r.startPath().block)&&(1!=J.getChildCount()||!J.getBogus()))a:{var Q;if(1==
q.getChildCount()&&a(Q=q.getFirst())&&Q.is(I)&&!Q.hasAttribute("contenteditable")){J=Q.getElementsByTag("*");r=0;for(y=J.count();r<y;r++)if(x=J.getItem(r),!x.is(M))break a;Q.moveChildren(Q.getParent(1));Q.remove()}}D.dataWrapper=q;r=R}if(r){Q=D.range;r=Q.document;q=D.blockLimit;y=0;var G,J=[],ca,P;R=T=0;var U,x=Q.startContainer;K=D.endPath.elements[0];var S,v=K.getPosition(x),A=!!K.getCommonAncestor(x)&&v!=CKEDITOR.POSITION_IDENTICAL&&!(v&CKEDITOR.POSITION_CONTAINS+CKEDITOR.POSITION_IS_CONTAINED),
x=b(D.dataWrapper,D);for(D.editor.enterMode!==CKEDITOR.ENTER_BR&&k(x,Q);y<x.length;y++){v=x[y];if(u=v.isLineBreak)u=Q,w=q,W=F=void 0,v.hasBlockSibling?u=1:(F=u.startContainer.getAscendant(l.$block,1))&&F.is({div:1,p:1})?(W=F.getPosition(w),W==CKEDITOR.POSITION_IDENTICAL||W==CKEDITOR.POSITION_CONTAINS?u=0:(w=u.splitElement(F),u.moveToPosition(w,CKEDITOR.POSITION_AFTER_START),u=1)):u=0;if(u)R=0<y;else{u=Q.startPath();!v.isBlock&&p(D.editor,u.block,u.blockLimit)&&(P=m(D.editor))&&(P=r.createElement(P),
P.appendBogus(),Q.insertNode(P),CKEDITOR.env.needsBrFiller&&(G=P.getBogus())&&G.remove(),Q.moveToPosition(P,CKEDITOR.POSITION_BEFORE_END));if((u=Q.startPath().block)&&!u.equals(ca)){if(G=u.getBogus())G.remove(),J.push(u);ca=u}v.firstNotAllowed&&(T=1);if(T&&v.isElement){u=Q.startContainer;for(w=null;u&&!l[u.getName()][v.name];){if(u.equals(q)){u=null;break}w=u;u=u.getParent()}if(u)w&&(U=Q.splitElement(w),D.zombies.push(U),D.zombies.push(w));else{w=q.getName();S=!y;u=y==x.length-1;w=d(v.node,w);F=[];
W=w.length;for(var Z=0,Y=void 0,da=0,fa=-1;Z<W;Z++)Y=w[Z]," "==Y?(da||S&&!Z||(F.push(new CKEDITOR.dom.text(" ")),fa=F.length),da=1):(F.push(Y),da=0);u&&fa==F.length&&F.pop();S=F}}if(S){for(;u=S.pop();)Q.insertNode(u);S=0}else Q.insertNode(v.node);v.lastNotAllowed&&y<x.length-1&&((U=A?K:U)&&Q.setEndAt(U,CKEDITOR.POSITION_AFTER_START),T=0);Q.collapse()}}1!=x.length?G=!1:(G=x[0],G=G.isElement&&"false"==G.node.getAttribute("contenteditable"));G&&(R=!0,u=x[0].node,Q.setStartAt(u,CKEDITOR.POSITION_BEFORE_START),
Q.setEndAt(u,CKEDITOR.POSITION_AFTER_END));D.dontMoveCaret=R;D.bogusNeededBlocks=J}G=D.range;var ba;S=D.bogusNeededBlocks;for(ca=G.createBookmark();P=D.zombies.pop();)P.getParent()&&(U=G.clone(),U.moveToElementEditStart(P),U.removeEmptyBlocksAtEnd());if(S)for(;P=S.pop();)CKEDITOR.env.needsBrFiller?P.appendBogus():P.append(G.document.createText(" "));for(;P=D.mergeCandidates.pop();)P.mergeSiblings();CKEDITOR.env.webkit&&G.startPath()&&(P=G.startPath(),P.block?P.block.$.normalize():P.blockLimit&&P.blockLimit.$.normalize());
G.moveToBookmark(ca);if(!D.dontMoveCaret){for(P=e(G);P&&a(P)&&!P.is(l.$empty);){if(P.isBlockBoundary())G.moveToPosition(P,CKEDITOR.POSITION_BEFORE_END);else{if(f(P)&&P.getHtml().match(/(\s|&nbsp;)$/g)){ba=null;break}ba=G.clone();ba.moveToPosition(P,CKEDITOR.POSITION_BEFORE_END)}P=P.getLast(c)}ba&&G.moveToRange(ba)}}}}();J=function(){function a(b){b=new CKEDITOR.dom.walker(b);b.guard=function(a,b){if(b)return!1;if(a.type==CKEDITOR.NODE_ELEMENT)return a.is(CKEDITOR.dtd.$tableContent)};b.evaluator=function(a){return a.type==
CKEDITOR.NODE_ELEMENT};return b}function b(a,c,d){c=a.getDocument().createElement(c);a.append(c,d);return c}function c(a){var b=a.count(),d;for(b;0<b--;)d=a.getItem(b),CKEDITOR.tools.trim(d.getHtml())||(d.appendBogus(),CKEDITOR.env.ie&&9>CKEDITOR.env.version&&d.getChildCount()&&d.getFirst().remove())}return function(d){var e=d.startContainer,g=e.getAscendant("table",1),f=!1;c(g.getElementsByTag("td"));c(g.getElementsByTag("th"));g=d.clone();g.setStart(e,0);g=a(g).lastBackward();g||(g=d.clone(),g.setEndAt(e,
CKEDITOR.POSITION_BEFORE_END),g=a(g).lastForward(),f=!0);g||(g=e);g.is("table")?(d.setStartAt(g,CKEDITOR.POSITION_BEFORE_START),d.collapse(!0),g.remove()):(g.is({tbody:1,thead:1,tfoot:1})&&(g=b(g,"tr",f)),g.is("tr")&&(g=b(g,g.getParent().is("thead")?"th":"td",f)),(e=g.getBogus())&&e.remove(),d.moveToPosition(g,f?CKEDITOR.POSITION_AFTER_START:CKEDITOR.POSITION_BEFORE_END))}}();G=function(){function a(b){b=new CKEDITOR.dom.walker(b);b.guard=function(a,b){if(b)return!1;if(a.type==CKEDITOR.NODE_ELEMENT)return a.is(CKEDITOR.dtd.$list)||
a.is(CKEDITOR.dtd.$listItem)};b.evaluator=function(a){return a.type==CKEDITOR.NODE_ELEMENT&&a.is(CKEDITOR.dtd.$listItem)};return b}return function(b){var c=b.startContainer,d=!1,e;e=b.clone();e.setStart(c,0);e=a(e).lastBackward();e||(e=b.clone(),e.setEndAt(c,CKEDITOR.POSITION_BEFORE_END),e=a(e).lastForward(),d=!0);e||(e=c);e.is(CKEDITOR.dtd.$list)?(b.setStartAt(e,CKEDITOR.POSITION_BEFORE_START),b.collapse(!0),e.remove()):((c=e.getBogus())&&c.remove(),b.moveToPosition(e,d?CKEDITOR.POSITION_AFTER_START:
CKEDITOR.POSITION_BEFORE_END),b.select())}}();q={eol:{detect:function(a,b){var c=a.range,d=c.clone(),e=c.clone(),g=new CKEDITOR.dom.elementPath(c.startContainer,b),f=new CKEDITOR.dom.elementPath(c.endContainer,b);d.collapse(1);e.collapse();g.block&&d.checkBoundaryOfElement(g.block,CKEDITOR.END)&&(c.setStartAfter(g.block),a.prependEolBr=1);f.block&&e.checkBoundaryOfElement(f.block,CKEDITOR.START)&&(c.setEndBefore(f.block),a.appendEolBr=1)},fix:function(a,b){var c=b.getDocument(),d;a.appendEolBr&&(d=
this.createEolBr(c),a.fragment.append(d));!a.prependEolBr||d&&!d.getPrevious()||a.fragment.append(this.createEolBr(c),1)},createEolBr:function(a){return a.createElement("br",{attributes:{"data-cke-eol":1}})}},bogus:{exclude:function(a){var b=a.range.getBoundaryNodes(),c=b.startNode,b=b.endNode;!b||!l(b)||c&&c.equals(b)||a.range.setEndBefore(b)}},tree:{rebuild:function(a,b){var c=a.range,d=c.getCommonAncestor(),e=new CKEDITOR.dom.elementPath(d,b),g=new CKEDITOR.dom.elementPath(c.startContainer,b),
c=new CKEDITOR.dom.elementPath(c.endContainer,b),f;d.type==CKEDITOR.NODE_TEXT&&(d=d.getParent());if(e.blockLimit.is({tr:1,table:1})){var h=e.contains("table").getParent();f=function(a){return!a.equals(h)}}else if(e.block&&e.block.is(CKEDITOR.dtd.$listItem)&&(g=g.contains(CKEDITOR.dtd.$list),c=c.contains(CKEDITOR.dtd.$list),!g.equals(c))){var r=e.contains(CKEDITOR.dtd.$list).getParent();f=function(a){return!a.equals(r)}}f||(f=function(a){return!a.equals(e.block)&&!a.equals(e.blockLimit)});this.rebuildFragment(a,
b,d,f)},rebuildFragment:function(a,b,c,d){for(var e;c&&!c.equals(b)&&d(c);)e=c.clone(0,1),a.fragment.appendTo(e),a.fragment=e,c=c.getParent()}},cell:{shrink:function(a){a=a.range;var b=a.startContainer,c=a.endContainer,d=a.startOffset,e=a.endOffset;b.type==CKEDITOR.NODE_ELEMENT&&b.equals(c)&&b.is("tr")&&++d==e&&a.shrink(CKEDITOR.SHRINK_TEXT)}}};u=function(){function a(b,c){var d=b.getParent();if(d.is(CKEDITOR.dtd.$inline))b[c?"insertBefore":"insertAfter"](d)}function b(c,d,e){a(d);a(e,1);for(var g;g=
e.getNext();)g.insertAfter(d),d=g;v(c)&&c.remove()}function c(a,b){var d=new CKEDITOR.dom.range(a);d.setStartAfter(b.startNode);d.setEndBefore(b.endNode);return d}return{list:{detectMerge:function(a,b){var d=c(b,a.bookmark),e=d.startPath(),g=d.endPath(),f=e.contains(CKEDITOR.dtd.$list),h=g.contains(CKEDITOR.dtd.$list);a.mergeList=f&&h&&f.getParent().equals(h.getParent())&&!f.equals(h);a.mergeListItems=e.block&&g.block&&e.block.is(CKEDITOR.dtd.$listItem)&&g.block.is(CKEDITOR.dtd.$listItem);if(a.mergeList||
a.mergeListItems)d=d.clone(),d.setStartBefore(a.bookmark.startNode),d.setEndAfter(a.bookmark.endNode),a.mergeListBookmark=d.createBookmark()},merge:function(a,c){if(a.mergeListBookmark){var d=a.mergeListBookmark.startNode,e=a.mergeListBookmark.endNode,g=new CKEDITOR.dom.elementPath(d,c),f=new CKEDITOR.dom.elementPath(e,c);if(a.mergeList){var h=g.contains(CKEDITOR.dtd.$list),k=f.contains(CKEDITOR.dtd.$list);h.equals(k)||(k.moveChildren(h),k.remove())}a.mergeListItems&&(g=g.contains(CKEDITOR.dtd.$listItem),
f=f.contains(CKEDITOR.dtd.$listItem),g.equals(f)||b(f,d,e));d.remove();e.remove()}}},block:{detectMerge:function(a,b){if(!a.tableContentsRanges&&!a.mergeListBookmark){var c=new CKEDITOR.dom.range(b);c.setStartBefore(a.bookmark.startNode);c.setEndAfter(a.bookmark.endNode);a.mergeBlockBookmark=c.createBookmark()}},merge:function(a,c){if(a.mergeBlockBookmark&&!a.purgeTableBookmark){var d=a.mergeBlockBookmark.startNode,e=a.mergeBlockBookmark.endNode,g=new CKEDITOR.dom.elementPath(d,c),f=new CKEDITOR.dom.elementPath(e,
c),g=g.block,f=f.block;g&&f&&!g.equals(f)&&b(f,d,e);d.remove();e.remove()}}},table:function(){function a(c){var e=[],g,f=new CKEDITOR.dom.walker(c),h=c.startPath().contains(d),k=c.endPath().contains(d),t={};f.guard=function(a,f){if(a.type==CKEDITOR.NODE_ELEMENT){var B="visited_"+(f?"out":"in");if(a.getCustomData(B))return;CKEDITOR.dom.element.setMarker(t,a,B,1)}if(f&&h&&a.equals(h))g=c.clone(),g.setEndAt(h,CKEDITOR.POSITION_BEFORE_END),e.push(g);else if(!f&&k&&a.equals(k))g=c.clone(),g.setStartAt(k,
CKEDITOR.POSITION_AFTER_START),e.push(g);else{if(B=!f)B=a.type==CKEDITOR.NODE_ELEMENT&&a.is(d)&&(!h||b(a,h))&&(!k||b(a,k));if(!B&&(B=f))if(a.is(d))var B=h&&h.getAscendant("table",!0),I=k&&k.getAscendant("table",!0),m=a.getAscendant("table",!0),B=B&&B.contains(m)||I&&I.contains(m);else B=void 0;B&&(g=c.clone(),g.selectNodeContents(a),e.push(g))}};f.lastForward();CKEDITOR.dom.element.clearAllMarkers(t);return e}function b(a,c){var d=CKEDITOR.POSITION_CONTAINS+CKEDITOR.POSITION_IS_CONTAINED,e=a.getPosition(c);
return e===CKEDITOR.POSITION_IDENTICAL?!1:0===(e&d)}var d={td:1,th:1,caption:1};return{detectPurge:function(a){var b=a.range,c=b.clone();c.enlarge(CKEDITOR.ENLARGE_ELEMENT);var c=new CKEDITOR.dom.walker(c),e=0;c.evaluator=function(a){a.type==CKEDITOR.NODE_ELEMENT&&a.is(d)&&++e};c.checkForward();if(1<e){var c=b.startPath().contains("table"),g=b.endPath().contains("table");c&&g&&b.checkBoundaryOfElement(c,CKEDITOR.START)&&b.checkBoundaryOfElement(g,CKEDITOR.END)&&(b=a.range.clone(),b.setStartBefore(c),
b.setEndAfter(g),a.purgeTableBookmark=b.createBookmark())}},detectRanges:function(e,g){var f=c(g,e.bookmark),h=f.clone(),k,n,m=f.getCommonAncestor();m.is(CKEDITOR.dtd.$tableContent)&&!m.is(d)&&(m=m.getAscendant("table",!0));n=m;m=new CKEDITOR.dom.elementPath(f.startContainer,n);n=new CKEDITOR.dom.elementPath(f.endContainer,n);m=m.contains("table");n=n.contains("table");if(m||n)m&&n&&b(m,n)?(e.tableSurroundingRange=h,h.setStartAt(m,CKEDITOR.POSITION_AFTER_END),h.setEndAt(n,CKEDITOR.POSITION_BEFORE_START),
h=f.clone(),h.setEndAt(m,CKEDITOR.POSITION_AFTER_END),k=f.clone(),k.setStartAt(n,CKEDITOR.POSITION_BEFORE_START),k=a(h).concat(a(k))):m?n||(e.tableSurroundingRange=h,h.setStartAt(m,CKEDITOR.POSITION_AFTER_END),f.setEndAt(m,CKEDITOR.POSITION_AFTER_END)):(e.tableSurroundingRange=h,h.setEndAt(n,CKEDITOR.POSITION_BEFORE_START),f.setStartAt(n,CKEDITOR.POSITION_AFTER_START)),e.tableContentsRanges=k?k:a(f)},deleteRanges:function(a){for(var b;b=a.tableContentsRanges.pop();)b.extractContents(),v(b.startContainer)&&
b.startContainer.appendBogus();a.tableSurroundingRange&&a.tableSurroundingRange.extractContents()},purge:function(a){if(a.purgeTableBookmark){var b=a.doc,c=a.range.clone(),b=b.createElement("p");b.insertBefore(a.purgeTableBookmark.startNode);c.moveToBookmark(a.purgeTableBookmark);c.deleteContents();a.range.moveToPosition(b,CKEDITOR.POSITION_AFTER_START)}}}}(),detectExtractMerge:function(a){return!(a.range.startPath().contains(CKEDITOR.dtd.$listItem)&&a.range.endPath().contains(CKEDITOR.dtd.$listItem))},
fixUneditableRangePosition:function(a){a.startContainer.getDtd()["#"]||a.moveToClosestEditablePosition(null,!0)},autoParagraph:function(a,b){var c=b.startPath(),d;p(a,c.block,c.blockLimit)&&(d=m(a))&&(d=b.document.createElement(d),d.appendBogus(),b.insertNode(d),b.moveToPosition(d,CKEDITOR.POSITION_AFTER_START))}}}()})();
(function(){function a(a){return CKEDITOR.plugins.widget&&CKEDITOR.plugins.widget.isDomWidget(a)}function d(b,c){if(0===b.length||a(b[0].getEnclosedNode()))return!1;var d,e;if((d=!c&&1===b.length)&&!(d=b[0].collapsed)){var g=b[0];d=g.startContainer.getAscendant({td:1,th:1},!0);var f=g.endContainer.getAscendant({td:1,th:1},!0);e=CKEDITOR.tools.trim;d&&d.equals(f)&&!d.findOne("td, th, tr, tbody, table")?(g=g.cloneContents(),d=g.getFirst()?e(g.getFirst().getText())!==e(d.getText()):!0):d=!1}if(d)return!1;
for(e=0;e<b.length;e++)if(d=b[e]._getTableElement(),!d)return!1;return!0}function b(a){function b(a){a=a.find("td, th");var c=[],d;for(d=0;d<a.count();d++)c.push(a.getItem(d));return c}var c=[],d,e;for(e=0;e<a.length;e++)d=a[e]._getTableElement(),d.is&&d.is({td:1,th:1})?c.push(d):c=c.concat(b(d));return c}function c(a){a=b(a);var c="",d=[],e,g;for(g=0;g<a.length;g++)e&&!e.equals(a[g].getAscendant("tr"))?(c+=d.join("\t")+"\n",e=a[g].getAscendant("tr"),d=[]):0===g&&(e=a[g].getAscendant("tr")),d.push(a[g].getText());
return c+=d.join("\t")}function f(a){var b=this.root.editor,d=b.getSelection(1);this.reset();G=!0;d.root.once("selectionchange",function(a){a.cancel()},null,null,0);d.selectRanges([a[0]]);d=this._.cache;d.ranges=new CKEDITOR.dom.rangeList(a);d.type=CKEDITOR.SELECTION_TEXT;d.selectedElement=a[0]._getTableElement();d.selectedText=c(a);d.nativeSel=null;this.isFake=1;this.rev=L++;b._.fakeSelection=this;G=!1;this.root.fire("selectionchange")}function e(){var b=this._.fakeSelection,c;if(b){c=this.getSelection(1);
var e;if(!(e=!c)&&(e=!c.isHidden())){e=b;var g=c.getRanges(),f=e.getRanges(),h=g.length&&g[0]._getTableElement()&&g[0]._getTableElement().getAscendant("table",!0),k=f.length&&f[0]._getTableElement()&&f[0]._getTableElement().getAscendant("table",!0),m=1===g.length&&g[0]._getTableElement()&&g[0]._getTableElement().is("table"),M=1===f.length&&f[0]._getTableElement()&&f[0]._getTableElement().is("table");if(a(e.getSelectedElement()))e=!1;else{var n=1===g.length&&g[0].collapsed,f=d(g,!!CKEDITOR.env.webkit)&&
d(f);h=h&&k?h.equals(k)||k.contains(h):!1;h&&(n||f)?(m&&!M&&e.selectRanges(g),e=!0):e=!1}e=!e}e&&(b.reset(),b=0)}if(!b&&(b=c||this.getSelection(1),!b||b.getType()==CKEDITOR.SELECTION_NONE))return;this.fire("selectionCheck",b);c=this.elementPath();c.compare(this._.selectionPreviousPath)||(e=this._.selectionPreviousPath&&this._.selectionPreviousPath.blockLimit.equals(c.blockLimit),!CKEDITOR.env.webkit&&!CKEDITOR.env.gecko||e||(this._.previousActive=this.document.getActive()),this._.selectionPreviousPath=
c,this.fire("selectionChange",{selection:b,path:c}))}function k(){u=!0;q||(h.call(this),q=CKEDITOR.tools.setTimeout(h,200,this))}function h(){q=null;u&&(CKEDITOR.tools.setTimeout(e,0,this),u=!1)}function p(a){return K(a)||a.type==CKEDITOR.NODE_ELEMENT&&!a.is(CKEDITOR.dtd.$empty)?!0:!1}function m(a){function b(c,d){return c&&c.type!=CKEDITOR.NODE_TEXT?a.clone()["moveToElementEdit"+(d?"End":"Start")](c):!1}if(!(a.root instanceof CKEDITOR.editable))return!1;var c=a.startContainer,d=a.getPreviousNode(p,
null,c),e=a.getNextNode(p,null,c);return b(d)||b(e,1)||!(d||e||c.type==CKEDITOR.NODE_ELEMENT&&c.isBlockBoundary()&&c.getBogus())?!0:!1}function g(a){x(a,!1);var b=a.getDocument().createText(w);a.setCustomData("cke-fillingChar",b);return b}function x(a,b){var c=a&&a.removeCustomData("cke-fillingChar");if(c){if(!1!==b){var d=a.getDocument().getSelection().getNative(),e=d&&"None"!=d.type&&d.getRangeAt(0),g=w.length;if(c.getLength()>g&&e&&e.intersectsNode(c.$)){var f=[{node:d.anchorNode,offset:d.anchorOffset},
{node:d.focusNode,offset:d.focusOffset}];d.anchorNode==c.$&&d.anchorOffset>g&&(f[0].offset-=g);d.focusNode==c.$&&d.focusOffset>g&&(f[1].offset-=g)}}c.setText(y(c.getText(),1));f&&(c=a.getDocument().$,d=c.getSelection(),c=c.createRange(),c.setStart(f[0].node,f[0].offset),c.collapse(!0),d.removeAllRanges(),d.addRange(c),d.extend(f[1].node,f[1].offset))}}function y(a,b){return b?a.replace(J,function(a,b){return b?" ":""}):a.replace(w,"")}function A(a,b){var c=b&&CKEDITOR.tools.htmlEncode(b)||"\x26nbsp;",
c=CKEDITOR.dom.element.createFromHtml('\x3cdiv data-cke-hidden-sel\x3d"1" data-cke-temp\x3d"1" style\x3d"'+(CKEDITOR.env.ie&&14>CKEDITOR.env.version?"display:none":"position:fixed;top:0;left:-1000px;width:0;height:0;overflow:hidden;")+'"\x3e'+c+"\x3c/div\x3e",a.document);a.fire("lockSnapshot");a.editable().append(c);var d=a.getSelection(1),e=a.createRange(),g=d.root.on("selectionchange",function(a){a.cancel()},null,null,0);e.setStartAt(c,CKEDITOR.POSITION_AFTER_START);e.setEndAt(c,CKEDITOR.POSITION_BEFORE_END);
d.selectRanges([e]);g.removeListener();a.fire("unlockSnapshot");a._.hiddenSelectionContainer=c}function F(b){var c={37:1,39:1,8:1,46:1};return function(d){var e=d.data.getKeystroke();if(c[e]){var g=b.getSelection(),f=g.getRanges()[0];g.isCollapsed()&&(f=f[38>e?"getPreviousEditableNode":"getNextEditableNode"]())&&f.type==CKEDITOR.NODE_ELEMENT&&"false"==f.getAttribute("contenteditable")&&(g=g.getStartElement(),!g.isBlockBoundary()||""!==(void 0===g.$.textContent?g.$.innerText:g.$.textContent)||a(g.getFirst())||
8!==e&&46!==e||(g.remove(),b.fire("saveSnapshot")),b.getSelection().fake(f),d.data.preventDefault(),d.cancel())}}}function v(a){for(var b=0;b<a.length;b++){var c=a[b];c.getCommonAncestor().isReadOnly()&&a.splice(b,1);if(!c.collapsed){if(c.startContainer.isReadOnly())for(var d=c.startContainer,e;d&&!((e=d.type==CKEDITOR.NODE_ELEMENT)&&d.is("body")||!d.isReadOnly());)e&&"false"==d.getAttribute("contentEditable")&&c.setStartAfter(d),d=d.getParent();d=c.startContainer;e=c.endContainer;var g=c.startOffset,
f=c.endOffset,h=c.clone();d&&d.type==CKEDITOR.NODE_TEXT&&(g>=d.getLength()?h.setStartAfter(d):h.setStartBefore(d));e&&e.type==CKEDITOR.NODE_TEXT&&(f?h.setEndAfter(e):h.setEndBefore(e));d=new CKEDITOR.dom.walker(h);d.evaluator=function(d){if(d.type==CKEDITOR.NODE_ELEMENT&&d.isReadOnly()){var e=c.clone();c.setEndBefore(d);c.collapsed&&a.splice(b--,1);d.getPosition(h.endContainer)&CKEDITOR.POSITION_CONTAINS||(e.setStartAfter(d),e.collapsed||a.splice(b+1,0,e));return!0}return!1};d.next()}}return a}var l=
"function"!=typeof window.getSelection,L=1,w=CKEDITOR.tools.repeat("​",7),J=new RegExp(w+"( )?","g"),G,q,u,K=CKEDITOR.dom.walker.invisible(1),E=function(){function a(b){return function(a){var c=a.editor.createRange();c.moveToClosestEditablePosition(a.selected,b)&&a.editor.getSelection().selectRanges([c]);return!1}}function b(a){return function(b){var c=b.editor,d=c.createRange(),e;if(!c.readOnly)return(e=d.moveToClosestEditablePosition(b.selected,a))||(e=d.moveToClosestEditablePosition(b.selected,
!a)),e&&c.getSelection().selectRanges([d]),c.fire("saveSnapshot"),b.selected.remove(),e||(d.moveToElementEditablePosition(c.editable()),c.getSelection().selectRanges([d])),c.fire("saveSnapshot"),!1}}var c=a(),d=a(1);return{37:c,38:c,39:d,40:d,8:b(),46:b(1)}}();CKEDITOR.on("instanceCreated",function(a){function b(){var a=c.getSelection();a&&a.removeAllRanges()}var c=a.editor;c.on("contentDom",function(){function a(){q=new CKEDITOR.dom.selection(c.getSelection());q.lock()}function b(){h.removeListener("mouseup",
b);m.removeListener("mouseup",b);var a=CKEDITOR.document.$.selection,c=a.createRange();"None"!=a.type&&c.parentElement()&&c.parentElement().ownerDocument==f.$&&c.select()}function d(a){var b,c;b=(b=this.document.getActive())?"input"===b.getName()||"textarea"===b.getName():!1;b||(b=this.getSelection(1),(c=g(b))&&!c.equals(n)&&(b.selectElement(c),a.data.preventDefault()))}function g(a){a=a.getRanges()[0];return a?(a=a.startContainer.getAscendant(function(a){return a.type==CKEDITOR.NODE_ELEMENT&&a.hasAttribute("contenteditable")},
!0))&&"false"===a.getAttribute("contenteditable")?a:null:null}var f=c.document,h=CKEDITOR.document,n=c.editable(),t=f.getBody(),m=f.getDocumentElement(),B=n.isInline(),p,q;CKEDITOR.env.gecko&&n.attachListener(n,"focus",function(a){a.removeListener();0!==p&&(a=c.getSelection().getNative())&&a.isCollapsed&&a.anchorNode==n.$&&(a=c.createRange(),a.moveToElementEditStart(n),a.select())},null,null,-2);n.attachListener(n,CKEDITOR.env.webkit||CKEDITOR.env.gecko?"focusin":"focus",function(){if(p&&(CKEDITOR.env.webkit||
CKEDITOR.env.gecko)){p=c._.previousActive&&c._.previousActive.equals(f.getActive());var a=null!=c._.previousScrollTop&&c._.previousScrollTop!=n.$.scrollTop;CKEDITOR.env.webkit&&p&&a&&(n.$.scrollTop=c._.previousScrollTop)}c.unlockSelection(p);p=0},null,null,-1);n.attachListener(n,"mousedown",function(){p=0});if(CKEDITOR.env.ie||CKEDITOR.env.gecko||B)l?n.attachListener(n,"beforedeactivate",a,null,null,-1):n.attachListener(c,"selectionCheck",a,null,null,-1),n.attachListener(n,CKEDITOR.env.webkit||CKEDITOR.env.gecko?
"focusout":"blur",function(){var a=q&&(q.isFake||2>q.getRanges().length);CKEDITOR.env.gecko&&!B&&a||(c.lockSelection(q),p=1)},null,null,-1),n.attachListener(n,"mousedown",function(){p=0});if(CKEDITOR.env.ie&&!B){var v;n.attachListener(n,"mousedown",function(a){2==a.data.$.button&&((a=c.document.getSelection())&&a.getType()!=CKEDITOR.SELECTION_NONE||(v=c.window.getScrollPosition()))});n.attachListener(n,"mouseup",function(a){2==a.data.$.button&&v&&(c.document.$.documentElement.scrollLeft=v.x,c.document.$.documentElement.scrollTop=
v.y);v=null});if("BackCompat"!=f.$.compatMode){if(CKEDITOR.env.ie7Compat||CKEDITOR.env.ie6Compat){var u,w;m.on("mousedown",function(a){function b(a){a=a.data.$;if(u){var c=t.$.createTextRange();try{c.moveToPoint(a.clientX,a.clientY)}catch(d){}u.setEndPoint(0>w.compareEndPoints("StartToStart",c)?"EndToEnd":"StartToStart",c);u.select()}}function c(){m.removeListener("mousemove",b);h.removeListener("mouseup",c);m.removeListener("mouseup",c);u.select()}a=a.data;if(a.getTarget().is("html")&&a.$.y<m.$.clientHeight&&
a.$.x<m.$.clientWidth){u=t.$.createTextRange();try{u.moveToPoint(a.$.clientX,a.$.clientY)}catch(d){}w=u.duplicate();m.on("mousemove",b);h.on("mouseup",c);m.on("mouseup",c)}})}if(7<CKEDITOR.env.version&&11>CKEDITOR.env.version)m.on("mousedown",function(a){a.data.getTarget().is("html")&&(h.on("mouseup",b),m.on("mouseup",b))})}}n.attachListener(n,"selectionchange",e,c);n.attachListener(n,"keyup",k,c);n.attachListener(n,"touchstart",k,c);n.attachListener(n,"touchend",k,c);CKEDITOR.env.ie&&n.attachListener(n,
"keydown",d,c);n.attachListener(n,CKEDITOR.env.webkit||CKEDITOR.env.gecko?"focusin":"focus",function(){c.forceNextSelectionCheck();c.selectionChange(1)});if(B&&(CKEDITOR.env.webkit||CKEDITOR.env.gecko)){var y;n.attachListener(n,"mousedown",function(){y=1});n.attachListener(f.getDocumentElement(),"mouseup",function(){y&&k.call(c);y=0})}else n.attachListener(CKEDITOR.env.ie?n:f.getDocumentElement(),"mouseup",k,c);CKEDITOR.env.webkit&&n.attachListener(f,"keydown",function(a){switch(a.data.getKey()){case 13:case 33:case 34:case 35:case 36:case 37:case 39:case 8:case 45:case 46:n.hasFocus&&
x(n)}},null,null,-1);n.attachListener(n,"keydown",F(c),null,null,-1)});c.on("setData",function(){c.unlockSelection();CKEDITOR.env.webkit&&b()});c.on("contentDomUnload",function(){c.unlockSelection()});if(CKEDITOR.env.ie9Compat)c.on("beforeDestroy",b,null,null,9);c.on("dataReady",function(){delete c._.fakeSelection;delete c._.hiddenSelectionContainer;c.selectionChange(1)});c.on("loadSnapshot",function(){var a=CKEDITOR.dom.walker.nodeType(CKEDITOR.NODE_ELEMENT),b=c.editable().getLast(a);b&&b.hasAttribute("data-cke-hidden-sel")&&
(b.remove(),CKEDITOR.env.gecko&&(a=c.editable().getFirst(a))&&a.is("br")&&a.getAttribute("_moz_editor_bogus_node")&&a.remove())},null,null,100);c.on("key",function(a){if("wysiwyg"==c.mode){var b=c.getSelection();if(b.isFake){var d=E[a.data.keyCode];if(d)return d({editor:c,selected:b.getSelectedElement(),selection:b,keyEvent:a})}}})});if(CKEDITOR.env.webkit)CKEDITOR.on("instanceReady",function(a){var b=a.editor;b.on("selectionChange",function(){var a=b.editable(),c=a.getCustomData("cke-fillingChar");
c&&(c.getCustomData("ready")?(x(a),a.editor.fire("selectionCheck")):c.setCustomData("ready",1))},null,null,-1);b.on("beforeSetMode",function(){x(b.editable())},null,null,-1);b.on("getSnapshot",function(a){a.data&&(a.data=y(a.data))},b,null,20);b.on("toDataFormat",function(a){a.data.dataValue=y(a.data.dataValue)},null,null,0)});CKEDITOR.editor.prototype.selectionChange=function(a){(a?e:k).call(this)};CKEDITOR.editor.prototype.getSelection=function(a){return!this._.savedSelection&&!this._.fakeSelection||
a?(a=this.editable())&&"wysiwyg"==this.mode&&"recreating"!==this.status?new CKEDITOR.dom.selection(a):null:this._.savedSelection||this._.fakeSelection};CKEDITOR.editor.prototype.getSelectedRanges=function(a){var b=this.getSelection();return b&&b.getRanges(a)||[]};CKEDITOR.editor.prototype.lockSelection=function(a){a=a||this.getSelection(1);return a.getType()!=CKEDITOR.SELECTION_NONE?(!a.isLocked&&a.lock(),this._.savedSelection=a,!0):!1};CKEDITOR.editor.prototype.unlockSelection=function(a){var b=
this._.savedSelection;return b?(b.unlock(a),delete this._.savedSelection,!0):!1};CKEDITOR.editor.prototype.forceNextSelectionCheck=function(){delete this._.selectionPreviousPath};CKEDITOR.dom.document.prototype.getSelection=function(){return new CKEDITOR.dom.selection(this)};CKEDITOR.dom.range.prototype.select=function(){var a=this.root instanceof CKEDITOR.editable?this.root.editor.getSelection():new CKEDITOR.dom.selection(this.root);a.selectRanges([this]);return a};CKEDITOR.SELECTION_NONE=1;CKEDITOR.SELECTION_TEXT=
2;CKEDITOR.SELECTION_ELEMENT=3;CKEDITOR.dom.selection=function(a){if(a instanceof CKEDITOR.dom.selection){var b=a;a=a.root}var c=a instanceof CKEDITOR.dom.element;this.rev=b?b.rev:L++;this.document=a instanceof CKEDITOR.dom.document?a:a.getDocument();this.root=c?a:this.document.getBody();this.isLocked=0;this._={cache:{}};if(b)return CKEDITOR.tools.extend(this._.cache,b._.cache),this.isFake=b.isFake,this.isLocked=b.isLocked,this;a=this.getNative();var d,e;if(a)if(a.getRangeAt)d=(e=a.rangeCount&&a.getRangeAt(0))&&
new CKEDITOR.dom.node(e.commonAncestorContainer);else{try{e=a.createRange()}catch(g){}d=e&&CKEDITOR.dom.element.get(e.item&&e.item(0)||e.parentElement())}if(!d||d.type!=CKEDITOR.NODE_ELEMENT&&d.type!=CKEDITOR.NODE_TEXT||!this.root.equals(d)&&!this.root.contains(d))this._.cache.type=CKEDITOR.SELECTION_NONE,this._.cache.startElement=null,this._.cache.selectedElement=null,this._.cache.selectedText="",this._.cache.ranges=new CKEDITOR.dom.rangeList;return this};var C={img:1,hr:1,li:1,table:1,tr:1,td:1,
th:1,embed:1,object:1,ol:1,ul:1,a:1,input:1,form:1,select:1,textarea:1,button:1,fieldset:1,thead:1,tfoot:1};CKEDITOR.tools.extend(CKEDITOR.dom.selection,{_removeFillingCharSequenceString:y,_createFillingCharSequenceNode:g,FILLING_CHAR_SEQUENCE:w});CKEDITOR.dom.selection.prototype={getNative:function(){return void 0!==this._.cache.nativeSel?this._.cache.nativeSel:this._.cache.nativeSel=l?this.document.$.selection:this.document.getWindow().$.getSelection()},getType:l?function(){var a=this._.cache;if(a.type)return a.type;
var b=CKEDITOR.SELECTION_NONE;try{var c=this.getNative(),d=c.type;"Text"==d&&(b=CKEDITOR.SELECTION_TEXT);"Control"==d&&(b=CKEDITOR.SELECTION_ELEMENT);c.createRange().parentElement()&&(b=CKEDITOR.SELECTION_TEXT)}catch(e){}return a.type=b}:function(){var a=this._.cache;if(a.type)return a.type;var b=CKEDITOR.SELECTION_TEXT,c=this.getNative();if(!c||!c.rangeCount)b=CKEDITOR.SELECTION_NONE;else if(1==c.rangeCount){var c=c.getRangeAt(0),d=c.startContainer;d==c.endContainer&&1==d.nodeType&&1==c.endOffset-
c.startOffset&&C[d.childNodes[c.startOffset].nodeName.toLowerCase()]&&(b=CKEDITOR.SELECTION_ELEMENT)}return a.type=b},getRanges:function(){var a=l?function(){function a(b){return(new CKEDITOR.dom.node(b)).getIndex()}var b=function(b,c){b=b.duplicate();b.collapse(c);var d=b.parentElement();if(!d.hasChildNodes())return{container:d,offset:0};for(var e=d.children,g,f,h=b.duplicate(),k=0,m=e.length-1,t=-1,l,p;k<=m;)if(t=Math.floor((k+m)/2),g=e[t],h.moveToElementText(g),l=h.compareEndPoints("StartToStart",
b),0<l)m=t-1;else if(0>l)k=t+1;else return{container:d,offset:a(g)};if(-1==t||t==e.length-1&&0>l){h.moveToElementText(d);h.setEndPoint("StartToStart",b);h=h.text.replace(/(\r\n|\r)/g,"\n").length;e=d.childNodes;if(!h)return g=e[e.length-1],g.nodeType!=CKEDITOR.NODE_TEXT?{container:d,offset:e.length}:{container:g,offset:g.nodeValue.length};for(d=e.length;0<h&&0<d;)f=e[--d],f.nodeType==CKEDITOR.NODE_TEXT&&(p=f,h-=f.nodeValue.length);return{container:p,offset:-h}}h.collapse(0<l?!0:!1);h.setEndPoint(0<
l?"StartToStart":"EndToStart",b);h=h.text.replace(/(\r\n|\r)/g,"\n").length;if(!h)return{container:d,offset:a(g)+(0<l?0:1)};for(;0<h;)try{f=g[0<l?"previousSibling":"nextSibling"],f.nodeType==CKEDITOR.NODE_TEXT&&(h-=f.nodeValue.length,p=f),g=f}catch(q){return{container:d,offset:a(g)}}return{container:p,offset:0<l?-h:p.nodeValue.length+h}};return function(){var a=this.getNative(),c=a&&a.createRange(),d=this.getType();if(!a)return[];if(d==CKEDITOR.SELECTION_TEXT)return a=new CKEDITOR.dom.range(this.root),
d=b(c,!0),a.setStart(new CKEDITOR.dom.node(d.container),d.offset),d=b(c),a.setEnd(new CKEDITOR.dom.node(d.container),d.offset),a.endContainer.getPosition(a.startContainer)&CKEDITOR.POSITION_PRECEDING&&a.endOffset<=a.startContainer.getIndex()&&a.collapse(),[a];if(d==CKEDITOR.SELECTION_ELEMENT){for(var d=[],e=0;e<c.length;e++){for(var g=c.item(e),f=g.parentNode,h=0,a=new CKEDITOR.dom.range(this.root);h<f.childNodes.length&&f.childNodes[h]!=g;h++);a.setStart(new CKEDITOR.dom.node(f),h);a.setEnd(new CKEDITOR.dom.node(f),
h+1);d.push(a)}return d}return[]}}():function(){var a=[],b,c=this.getNative();if(!c)return a;for(var d=0;d<c.rangeCount;d++){var e=c.getRangeAt(d);b=new CKEDITOR.dom.range(this.root);b.setStart(new CKEDITOR.dom.node(e.startContainer),e.startOffset);b.setEnd(new CKEDITOR.dom.node(e.endContainer),e.endOffset);a.push(b)}return a};return function(b){var c=this._.cache,d=c.ranges;d||(c.ranges=d=new CKEDITOR.dom.rangeList(a.call(this)));return b?v(new CKEDITOR.dom.rangeList(d.slice())):d}}(),getStartElement:function(){var a=
this._.cache;if(void 0!==a.startElement)return a.startElement;var b;switch(this.getType()){case CKEDITOR.SELECTION_ELEMENT:return this.getSelectedElement();case CKEDITOR.SELECTION_TEXT:var c=this.getRanges()[0];if(c){if(c.collapsed)b=c.startContainer,b.type!=CKEDITOR.NODE_ELEMENT&&(b=b.getParent());else{for(c.optimize();b=c.startContainer,c.startOffset==(b.getChildCount?b.getChildCount():b.getLength())&&!b.isBlockBoundary();)c.setStartAfter(b);b=c.startContainer;if(b.type!=CKEDITOR.NODE_ELEMENT)return b.getParent();
if((b=b.getChild(c.startOffset))&&b.type==CKEDITOR.NODE_ELEMENT)for(c=b.getFirst();c&&c.type==CKEDITOR.NODE_ELEMENT;)b=c,c=c.getFirst();else b=c.startContainer}b=b.$}}return a.startElement=b?new CKEDITOR.dom.element(b):null},getSelectedElement:function(){var a=this._.cache;if(void 0!==a.selectedElement)return a.selectedElement;var b=this,c=CKEDITOR.tools.tryThese(function(){return b.getNative().createRange().item(0)},function(){for(var a=b.getRanges()[0].clone(),c,d,e=2;e&&!((c=a.getEnclosedNode())&&
c.type==CKEDITOR.NODE_ELEMENT&&C[c.getName()]&&(d=c));e--)a.shrink(CKEDITOR.SHRINK_ELEMENT);return d&&d.$});return a.selectedElement=c?new CKEDITOR.dom.element(c):null},getSelectedText:function(){var a=this._.cache;if(void 0!==a.selectedText)return a.selectedText;var b=this.getNative(),b=l?"Control"==b.type?"":b.createRange().text:b.toString();return a.selectedText=b},lock:function(){this.getRanges();this.getStartElement();this.getSelectedElement();this.getSelectedText();this._.cache.nativeSel=null;
this.isLocked=1},unlock:function(a){if(this.isLocked){if(a)var b=this.getSelectedElement(),c=this.getRanges(),e=this.isFake;this.isLocked=0;this.reset();a&&(a=b||c[0]&&c[0].getCommonAncestor())&&a.getAscendant("body",1)&&((a=this.root.editor)&&a.plugins.tableselection&&a.plugins.tableselection.isSupportedEnvironment(a)&&d(c)?f.call(this,c):e?this.fake(b):b&&2>c.length?this.selectElement(b):this.selectRanges(c))}},reset:function(){this._.cache={};this.isFake=0;var a=this.root.editor;if(a&&a._.fakeSelection)if(this.rev==
a._.fakeSelection.rev){delete a._.fakeSelection;var b=a._.hiddenSelectionContainer;if(b){var c=a.checkDirty();a.fire("lockSnapshot");b.remove();a.fire("unlockSnapshot");!c&&a.resetDirty()}delete a._.hiddenSelectionContainer}else CKEDITOR.warn("selection-fake-reset");this.rev=L++},selectElement:function(a){var b=new CKEDITOR.dom.range(this.root);b.setStartBefore(a);b.setEndAfter(a);this.selectRanges([b])},selectRanges:function(a){var b=this.root.editor,c=b&&b._.hiddenSelectionContainer;this.reset();
if(c)for(var c=this.root,e,h=0;h<a.length;++h)e=a[h],e.endContainer.equals(c)&&(e.endOffset=Math.min(e.endOffset,c.getChildCount()));if(a.length)if(this.isLocked){var k=CKEDITOR.document.getActive();this.unlock();this.selectRanges(a);this.lock();k&&!k.equals(this.root)&&k.focus()}else{var r;a:{var p,M;if(1==a.length&&!(M=a[0]).collapsed&&(r=M.getEnclosedNode())&&r.type==CKEDITOR.NODE_ELEMENT&&(M=M.clone(),M.shrink(CKEDITOR.SHRINK_ELEMENT,!0),(p=M.getEnclosedNode())&&p.type==CKEDITOR.NODE_ELEMENT&&
(r=p),"false"==r.getAttribute("contenteditable")))break a;r=void 0}if(r)this.fake(r);else if(b&&b.plugins.tableselection&&b.plugins.tableselection.isSupportedEnvironment(b)&&d(a)&&!G&&!a[0]._getTableElement({table:1}).hasAttribute("data-cke-tableselection-ignored"))f.call(this,a);else{if(l){p=CKEDITOR.dom.walker.whitespaces(!0);r=/\ufeff|\u00a0/;M={table:1,tbody:1,tr:1};1<a.length&&(b=a[a.length-1],a[0].setEnd(b.endContainer,b.endOffset));b=a[0];a=b.collapsed;var n,D,q;if((c=b.getEnclosedNode())&&
c.type==CKEDITOR.NODE_ELEMENT&&c.getName()in C&&(!c.is("a")||!c.getText()))try{q=c.$.createControlRange();q.addElement(c.$);q.select();return}catch(v){}if(b.startContainer.type==CKEDITOR.NODE_ELEMENT&&b.startContainer.getName()in M||b.endContainer.type==CKEDITOR.NODE_ELEMENT&&b.endContainer.getName()in M)b.shrink(CKEDITOR.NODE_ELEMENT,!0),a=b.collapsed;q=b.createBookmark();M=q.startNode;a||(k=q.endNode);q=b.document.$.body.createTextRange();q.moveToElementText(M.$);q.moveStart("character",1);k?(r=
b.document.$.body.createTextRange(),r.moveToElementText(k.$),q.setEndPoint("EndToEnd",r),q.moveEnd("character",-1)):(n=M.getNext(p),D=M.hasAscendant("pre"),n=!(n&&n.getText&&n.getText().match(r))&&(D||!M.hasPrevious()||M.getPrevious().is&&M.getPrevious().is("br")),D=b.document.createElement("span"),D.setHtml("\x26#65279;"),D.insertBefore(M),n&&b.document.createText("﻿").insertBefore(M));b.setStartBefore(M);M.remove();a?(n?(q.moveStart("character",-1),q.select(),b.document.$.selection.clear()):q.select(),
b.moveToPosition(D,CKEDITOR.POSITION_BEFORE_START),D.remove()):(b.setEndBefore(k),k.remove(),q.select())}else{k=this.getNative();if(!k)return;this.removeAllRanges();for(q=0;q<a.length;q++){if(q<a.length-1&&(n=a[q],D=a[q+1],r=n.clone(),r.setStart(n.endContainer,n.endOffset),r.setEnd(D.startContainer,D.startOffset),!r.collapsed&&(r.shrink(CKEDITOR.NODE_ELEMENT,!0),b=r.getCommonAncestor(),r=r.getEnclosedNode(),b.isReadOnly()||r&&r.isReadOnly()))){D.setStart(n.startContainer,n.startOffset);a.splice(q--,
1);continue}b=a[q];D=this.document.$.createRange();b.collapsed&&CKEDITOR.env.webkit&&m(b)&&(r=g(this.root),b.insertNode(r),(n=r.getNext())&&!r.getPrevious()&&n.type==CKEDITOR.NODE_ELEMENT&&"br"==n.getName()?(x(this.root),b.moveToPosition(n,CKEDITOR.POSITION_BEFORE_START)):b.moveToPosition(r,CKEDITOR.POSITION_AFTER_END));D.setStart(b.startContainer.$,b.startOffset);try{D.setEnd(b.endContainer.$,b.endOffset)}catch(u){if(0<=u.toString().indexOf("NS_ERROR_ILLEGAL_VALUE"))b.collapse(1),D.setEnd(b.endContainer.$,
b.endOffset);else throw u;}k.addRange(D)}}this.reset();this.root.fire("selectionchange")}}},fake:function(a,b){var c=this.root.editor;void 0===b&&a.hasAttribute("aria-label")&&(b=a.getAttribute("aria-label"));this.reset();A(c,b);var d=this._.cache,e=new CKEDITOR.dom.range(this.root);e.setStartBefore(a);e.setEndAfter(a);d.ranges=new CKEDITOR.dom.rangeList(e);d.selectedElement=d.startElement=a;d.type=CKEDITOR.SELECTION_ELEMENT;d.selectedText=d.nativeSel=null;this.isFake=1;this.rev=L++;c._.fakeSelection=
this;this.root.fire("selectionchange")},isHidden:function(){var a=this.getCommonAncestor();a&&a.type==CKEDITOR.NODE_TEXT&&(a=a.getParent());return!(!a||!a.data("cke-hidden-sel"))},isInTable:function(a){return d(this.getRanges(),a)},isCollapsed:function(){var a=this.getRanges();return 1===a.length&&a[0].collapsed},createBookmarks:function(a){a=this.getRanges().createBookmarks(a);this.isFake&&(a.isFake=1);return a},createBookmarks2:function(a){a=this.getRanges().createBookmarks2(a);this.isFake&&(a.isFake=
1);return a},selectBookmarks:function(a){for(var b=[],c,e=0;e<a.length;e++){var g=new CKEDITOR.dom.range(this.root);g.moveToBookmark(a[e]);b.push(g)}a.isFake&&(c=d(b)?b[0]._getTableElement():b[0].getEnclosedNode(),c&&c.type==CKEDITOR.NODE_ELEMENT||(CKEDITOR.warn("selection-not-fake"),a.isFake=0));a.isFake&&!d(b)?this.fake(c):this.selectRanges(b);return this},getCommonAncestor:function(){var a=this.getRanges();return a.length?a[0].startContainer.getCommonAncestor(a[a.length-1].endContainer):null},
scrollIntoView:function(){this.getType()!=CKEDITOR.SELECTION_NONE&&this.getRanges()[0].scrollIntoView()},removeAllRanges:function(){if(this.getType()!=CKEDITOR.SELECTION_NONE){var a=this.getNative();try{a&&a[l?"empty":"removeAllRanges"]()}catch(b){}this.reset()}}}})();"use strict";CKEDITOR.STYLE_BLOCK=1;CKEDITOR.STYLE_INLINE=2;CKEDITOR.STYLE_OBJECT=3;
(function(){function a(a,b){for(var c,d;(a=a.getParent())&&!a.equals(b);)if(a.getAttribute("data-nostyle"))c=a;else if(!d){var e=a.getAttribute("contentEditable");"false"==e?c=a:"true"==e&&(d=1)}return c}function d(a,b,c,d){return(a.getPosition(b)|d)==d&&(!c.childRule||c.childRule(a))}function b(c){var e=c.document;if(c.collapsed)e=L(this,e),c.insertNode(e),c.moveToPosition(e,CKEDITOR.POSITION_BEFORE_END);else{var g=this.element,h=this._.definition,k,m=h.ignoreReadonly,l=m||h.includeReadonly;null==
l&&(l=c.root.getCustomData("cke_includeReadonly"));var p=CKEDITOR.dtd[g];p||(k=!0,p=CKEDITOR.dtd.span);c.enlarge(CKEDITOR.ENLARGE_INLINE,1);c.trim();var q=c.createBookmark(),t=q.startNode,v=q.endNode,x=t,u;if(!m){var y=c.getCommonAncestor(),m=a(t,y),y=a(v,y);m&&(x=m.getNextSourceNode(!0));y&&(v=y)}for(x.getPosition(v)==CKEDITOR.POSITION_FOLLOWING&&(x=0);x;){m=!1;if(x.equals(v))x=null,m=!0;else{var w=x.type==CKEDITOR.NODE_ELEMENT?x.getName():null,y=w&&"false"==x.getAttribute("contentEditable"),A=w&&
-1!==CKEDITOR.tools.array.indexOf(CKEDITOR.style.unstylableElements,w),A=w&&(x.getAttribute("data-nostyle")||A);if(w&&x.data("cke-bookmark")||x.type===CKEDITOR.NODE_COMMENT){x=x.getNextSourceNode(!0);continue}if(y&&l&&CKEDITOR.dtd.$block[w])for(var C=x,B=f(C),E=void 0,G=B.length,H=0,C=G&&new CKEDITOR.dom.range(C.getDocument());H<G;++H){var E=B[H],J=CKEDITOR.filter.instances[E.data("cke-filter")];if(J?J.check(this):1)C.selectNodeContents(E),b.call(this,C)}B=w?!p[w]||A?0:y&&!l?0:d(x,v,h,N):1;if(B)if(E=
x.getParent(),B=h,G=g,H=k,!E||!(E.getDtd()||CKEDITOR.dtd.span)[G]&&!H||B.parentRule&&!B.parentRule(E))m=!0;else{if(u||w&&CKEDITOR.dtd.$removeEmpty[w]&&(x.getPosition(v)|N)!=N||(u=c.clone(),u.setStartBefore(x)),w=x.type,w==CKEDITOR.NODE_TEXT||y||w==CKEDITOR.NODE_ELEMENT&&!x.getChildCount()){for(var w=x,K;(m=!w.getNext(z))&&(K=w.getParent(),p[K.getName()])&&d(K,t,h,O);)w=K;u.setEndAfter(w)}}else m=!0;x=x.getNextSourceNode(A||y)}if(m&&u&&!u.collapsed){for(var m=L(this,e),y=m.hasAttributes(),A=u.getCommonAncestor(),
w={},B={},E={},G={},U,S,Y;m&&A;){if(A.getName()==g){for(U in h.attributes)!G[U]&&(Y=A.getAttribute(S))&&(m.getAttribute(U)==Y?B[U]=1:G[U]=1);for(S in h.styles)!E[S]&&(Y=A.getStyle(S))&&(m.getStyle(S)==Y?w[S]=1:E[S]=1)}A=A.getParent()}for(U in B)m.removeAttribute(U);for(S in w)m.removeStyle(S);y&&!m.hasAttributes()&&(m=null);m?(u.extractContents().appendTo(m),u.insertNode(m),F.call(this,m),m.mergeSiblings(),CKEDITOR.env.ie||m.$.normalize()):(m=new CKEDITOR.dom.element("span"),u.extractContents().appendTo(m),
u.insertNode(m),F.call(this,m),m.remove(!0));u=null}}c.moveToBookmark(q);c.shrink(CKEDITOR.SHRINK_TEXT);c.shrink(CKEDITOR.NODE_ELEMENT,!0)}}function c(a){function b(){for(var a=new CKEDITOR.dom.elementPath(d.getParent()),c=new CKEDITOR.dom.elementPath(l.getParent()),e=null,g=null,f=0;f<a.elements.length;f++){var h=a.elements[f];if(h==a.block||h==a.blockLimit)break;p.checkElementRemovable(h,!0)&&(e=h)}for(f=0;f<c.elements.length;f++){h=c.elements[f];if(h==c.block||h==c.blockLimit)break;p.checkElementRemovable(h,
!0)&&(g=h)}g&&l.breakParent(g);e&&d.breakParent(e)}a.enlarge(CKEDITOR.ENLARGE_INLINE,1);var c=a.createBookmark(),d=c.startNode,e=this._.definition.alwaysRemoveElement;if(a.collapsed){for(var g=new CKEDITOR.dom.elementPath(d.getParent(),a.root),f,h=0,k;h<g.elements.length&&(k=g.elements[h])&&k!=g.block&&k!=g.blockLimit;h++)if(this.checkElementRemovable(k)){var m;!e&&a.collapsed&&(a.checkBoundaryOfElement(k,CKEDITOR.END)||(m=a.checkBoundaryOfElement(k,CKEDITOR.START)))?(f=k,f.match=m?"start":"end"):
(k.mergeSiblings(),k.is(this.element)?A.call(this,k):v(k,G(this)[k.getName()]))}if(f){e=d;for(h=0;;h++){k=g.elements[h];if(k.equals(f))break;else if(k.match)continue;else k=k.clone();k.append(e);e=k}e["start"==f.match?"insertBefore":"insertAfter"](f)}}else{var l=c.endNode,p=this;b();for(g=d;!g.equals(l);)f=g.getNextSourceNode(),g.type==CKEDITOR.NODE_ELEMENT&&this.checkElementRemovable(g)&&(g.getName()==this.element?A.call(this,g):v(g,G(this)[g.getName()]),f.type==CKEDITOR.NODE_ELEMENT&&f.contains(d)&&
(b(),f=d.getNext())),g=f}a.moveToBookmark(c);a.shrink(CKEDITOR.NODE_ELEMENT,!0)}function f(a){var b=[];a.forEach(function(a){if("true"==a.getAttribute("contenteditable"))return b.push(a),!1},CKEDITOR.NODE_ELEMENT,!0);return b}function e(a){var b=a.getEnclosedNode()||a.getCommonAncestor(!1,!0);(a=(new CKEDITOR.dom.elementPath(b,a.root)).contains(this.element,1))&&!a.isReadOnly()&&w(a,this)}function k(a){var b=a.getCommonAncestor(!0,!0);if(a=(new CKEDITOR.dom.elementPath(b,a.root)).contains(this.element,
1)){var b=this._.definition,c=b.attributes;if(c)for(var d in c)a.removeAttribute(d,c[d]);if(b.styles)for(var e in b.styles)b.styles.hasOwnProperty(e)&&a.removeStyle(e)}}function h(a){var b=a.createBookmark(!0),c=a.createIterator();c.enforceRealBlocks=!0;this._.enterMode&&(c.enlargeBr=this._.enterMode!=CKEDITOR.ENTER_BR);for(var d,e=a.document,g;d=c.getNextParagraph();)!d.isReadOnly()&&(c.activeFilter?c.activeFilter.check(this):1)&&(g=L(this,e,d),m(d,g));a.moveToBookmark(b)}function p(a){var b=a.createBookmark(1),
c=a.createIterator();c.enforceRealBlocks=!0;c.enlargeBr=this._.enterMode!=CKEDITOR.ENTER_BR;for(var d,e;d=c.getNextParagraph();)this.checkElementRemovable(d)&&(d.is("pre")?((e=this._.enterMode==CKEDITOR.ENTER_BR?null:a.document.createElement(this._.enterMode==CKEDITOR.ENTER_P?"p":"div"))&&d.copyAttributes(e),m(d,e)):A.call(this,d));a.moveToBookmark(b)}function m(a,b){var c=!b;c&&(b=a.getDocument().createElement("div"),a.copyAttributes(b));var d=b&&b.is("pre"),e=a.is("pre"),f=!d&&e;if(d&&!e){e=b;(f=
a.getBogus())&&f.remove();f=a.getHtml();f=x(f,/(?:^[ \t\n\r]+)|(?:[ \t\n\r]+$)/g,"");f=f.replace(/[ \t\r\n]*(<br[^>]*>)[ \t\r\n]*/gi,"$1");f=f.replace(/([ \t\n\r]+|&nbsp;)/g," ");f=f.replace(/<br\b[^>]*>/gi,"\n");if(CKEDITOR.env.ie){var h=a.getDocument().createElement("div");h.append(e);e.$.outerHTML="\x3cpre\x3e"+f+"\x3c/pre\x3e";e.copyAttributes(h.getFirst());e=h.getFirst().remove()}else e.setHtml(f);b=e}else f?b=y(c?[a.getHtml()]:g(a),b):a.moveChildren(b);b.replace(a);if(d){var c=b,k;(k=c.getPrevious(H))&&
k.type==CKEDITOR.NODE_ELEMENT&&k.is("pre")&&(d=x(k.getHtml(),/\n$/,"")+"\n\n"+x(c.getHtml(),/^\n/,""),CKEDITOR.env.ie?c.$.outerHTML="\x3cpre\x3e"+d+"\x3c/pre\x3e":c.setHtml(d),k.remove())}else c&&l(b)}function g(a){var b=[];x(a.getOuterHtml(),/(\S\s*)\n(?:\s|(<span[^>]+data-cke-bookmark.*?\/span>))*\n(?!$)/gi,function(a,b,c){return b+"\x3c/pre\x3e"+c+"\x3cpre\x3e"}).replace(/<pre\b.*?>([\s\S]*?)<\/pre>/gi,function(a,c){b.push(c)});return b}function x(a,b,c){var d="",e="";a=a.replace(/(^<span[^>]+data-cke-bookmark.*?\/span>)|(<span[^>]+data-cke-bookmark.*?\/span>$)/gi,
function(a,b,c){b&&(d=b);c&&(e=c);return""});return d+a.replace(b,c)+e}function y(a,b){var c;1<a.length&&(c=new CKEDITOR.dom.documentFragment(b.getDocument()));for(var d=0;d<a.length;d++){var e=a[d],e=e.replace(/(\r\n|\r)/g,"\n"),e=x(e,/^[ \t]*\n/,""),e=x(e,/\n$/,""),e=x(e,/^[ \t]+|[ \t]+$/g,function(a,b){return 1==a.length?"\x26nbsp;":b?" "+CKEDITOR.tools.repeat("\x26nbsp;",a.length-1):CKEDITOR.tools.repeat("\x26nbsp;",a.length-1)+" "}),e=e.replace(/\n/g,"\x3cbr\x3e"),e=e.replace(/[ \t]{2,}/g,function(a){return CKEDITOR.tools.repeat("\x26nbsp;",
a.length-1)+" "});if(c){var g=b.clone();g.setHtml(e);c.append(g)}else b.setHtml(e)}return c||b}function A(a,b){var c=this._.definition,d=c.attributes,c=c.styles,e=G(this)[a.getName()],g=CKEDITOR.tools.isEmpty(d)&&CKEDITOR.tools.isEmpty(c),f;for(f in d)if("class"!=f&&!this._.definition.fullMatch||a.getAttribute(f)==q(f,d[f]))b&&"data-"==f.slice(0,5)||(g=a.hasAttribute(f),a.removeAttribute(f));for(var h in c)this._.definition.fullMatch&&a.getStyle(h)!=q(h,c[h],!0)||(g=g||!!a.getStyle(h),a.removeStyle(h));
v(a,e,E[a.getName()]);g&&(this._.definition.alwaysRemoveElement?l(a,1):!CKEDITOR.dtd.$block[a.getName()]||this._.enterMode==CKEDITOR.ENTER_BR&&!a.hasAttributes()?l(a):a.renameNode(this._.enterMode==CKEDITOR.ENTER_P?"p":"div"))}function F(a){for(var b=G(this),c=a.getElementsByTag(this.element),d,e=c.count();0<=--e;)d=c.getItem(e),d.isReadOnly()||A.call(this,d,!0);for(var g in b)if(g!=this.element)for(c=a.getElementsByTag(g),e=c.count()-1;0<=e;e--)d=c.getItem(e),d.isReadOnly()||v(d,b[g])}function v(a,
b,c){if(b=b&&b.attributes)for(var d=0;d<b.length;d++){var e=b[d][0],g;if(g=a.getAttribute(e)){var f=b[d][1];(null===f||f.test&&f.test(g)||"string"==typeof f&&g==f)&&a.removeAttribute(e)}}c||l(a)}function l(a,b){if(!a.hasAttributes()||b)if(CKEDITOR.dtd.$block[a.getName()]){var c=a.getPrevious(H),d=a.getNext(H);!c||c.type!=CKEDITOR.NODE_TEXT&&c.isBlockBoundary({br:1})||a.append("br",1);!d||d.type!=CKEDITOR.NODE_TEXT&&d.isBlockBoundary({br:1})||a.append("br");a.remove(!0)}else c=a.getFirst(),d=a.getLast(),
a.remove(!0),c&&(c.type==CKEDITOR.NODE_ELEMENT&&c.mergeSiblings(),d&&!c.equals(d)&&d.type==CKEDITOR.NODE_ELEMENT&&d.mergeSiblings())}function L(a,b,c){var d;d=a.element;"*"==d&&(d="span");d=new CKEDITOR.dom.element(d,b);c&&c.copyAttributes(d);d=w(d,a);b.getCustomData("doc_processing_style")&&d.hasAttribute("id")?d.removeAttribute("id"):b.setCustomData("doc_processing_style",1);return d}function w(a,b){var c=b._.definition,d=c.attributes,c=CKEDITOR.style.getStyleText(c);if(d)for(var e in d)a.setAttribute(e,
d[e]);c&&a.setAttribute("style",c);a.getDocument().removeCustomData("doc_processing_style");return a}function J(a,b){for(var c in a)a[c]=a[c].replace(B,function(a,c){return b[c]})}function G(a){if(a._.overrides)return a._.overrides;var b=a._.overrides={},c=a._.definition.overrides;if(c){CKEDITOR.tools.isArray(c)||(c=[c]);for(var d=0;d<c.length;d++){var e=c[d],g,f;"string"==typeof e?g=e.toLowerCase():(g=e.element?e.element.toLowerCase():a.element,f=e.attributes);e=b[g]||(b[g]={});if(f){var e=e.attributes=
e.attributes||[],h;for(h in f)e.push([h.toLowerCase(),f[h]])}}}return b}function q(a,b,c){var d=new CKEDITOR.dom.element("span");d[c?"setStyle":"setAttribute"](a,b);return d[c?"getStyle":"getAttribute"](a)}function u(a,b){function c(a,b){return"font-family"==b.toLowerCase()?a.replace(/["']/g,""):a}"string"==typeof a&&(a=CKEDITOR.tools.parseCssText(a));"string"==typeof b&&(b=CKEDITOR.tools.parseCssText(b,!0));for(var d in a)if(!(d in b)||c(b[d],d)!=c(a[d],d)&&"inherit"!=a[d]&&"inherit"!=b[d])return!1;
return!0}function K(a,b,c){var d=a.getRanges();b=b?this.removeFromRange:this.applyToRange;for(var e,g=d.createIterator();e=g.getNextRange();)b.call(this,e,c);a.selectRanges(d)}var E={address:1,div:1,h1:1,h2:1,h3:1,h4:1,h5:1,h6:1,p:1,pre:1,section:1,header:1,footer:1,nav:1,article:1,aside:1,figure:1,dialog:1,hgroup:1,time:1,meter:1,menu:1,command:1,keygen:1,output:1,progress:1,details:1,datagrid:1,datalist:1},C={a:1,blockquote:1,embed:1,hr:1,img:1,li:1,object:1,ol:1,table:1,td:1,tr:1,th:1,ul:1,dl:1,
dt:1,dd:1,form:1,audio:1,video:1},t=/\s*(?:;\s*|$)/,B=/#\((.+?)\)/g,z=CKEDITOR.dom.walker.bookmark(0,1),H=CKEDITOR.dom.walker.whitespaces(1);CKEDITOR.style=function(a,b){if("string"==typeof a.type)return new CKEDITOR.style.customHandlers[a.type](a);var c=a.attributes;c&&c.style&&(a.styles=CKEDITOR.tools.extend({},a.styles,CKEDITOR.tools.parseCssText(c.style)),delete c.style);b&&(a=CKEDITOR.tools.clone(a),J(a.attributes,b),J(a.styles,b));c=this.element=a.element?"string"==typeof a.element?a.element.toLowerCase():
a.element:"*";this.type=a.type||(E[c]?CKEDITOR.STYLE_BLOCK:C[c]?CKEDITOR.STYLE_OBJECT:CKEDITOR.STYLE_INLINE);"object"==typeof this.element&&(this.type=CKEDITOR.STYLE_OBJECT);this._={definition:a}};CKEDITOR.style.prototype={apply:function(a){if(a instanceof CKEDITOR.dom.document)return K.call(this,a.getSelection());if(this.checkApplicable(a.elementPath(),a)){var b=this._.enterMode;b||(this._.enterMode=a.activeEnterMode);K.call(this,a.getSelection(),0,a);this._.enterMode=b}},remove:function(a){if(a instanceof
CKEDITOR.dom.document)return K.call(this,a.getSelection(),1);if(this.checkApplicable(a.elementPath(),a)){var b=this._.enterMode;b||(this._.enterMode=a.activeEnterMode);K.call(this,a.getSelection(),1,a);this._.enterMode=b}},applyToRange:function(a){this.applyToRange=this.type==CKEDITOR.STYLE_INLINE?b:this.type==CKEDITOR.STYLE_BLOCK?h:this.type==CKEDITOR.STYLE_OBJECT?e:null;return this.applyToRange(a)},removeFromRange:function(a){this.removeFromRange=this.type==CKEDITOR.STYLE_INLINE?c:this.type==CKEDITOR.STYLE_BLOCK?
p:this.type==CKEDITOR.STYLE_OBJECT?k:null;return this.removeFromRange(a)},applyToObject:function(a){w(a,this)},checkActive:function(a,b){switch(this.type){case CKEDITOR.STYLE_BLOCK:return this.checkElementRemovable(a.block||a.blockLimit,!0,b);case CKEDITOR.STYLE_OBJECT:case CKEDITOR.STYLE_INLINE:for(var c=a.elements,d=0,e;d<c.length;d++)if(e=c[d],this.type!=CKEDITOR.STYLE_INLINE||e!=a.block&&e!=a.blockLimit){if(this.type==CKEDITOR.STYLE_OBJECT){var g=e.getName();if(!("string"==typeof this.element?
g==this.element:g in this.element))continue}if(this.checkElementRemovable(e,!0,b))return!0}}return!1},checkApplicable:function(a,b,c){b&&b instanceof CKEDITOR.filter&&(c=b);if(c&&!c.check(this))return!1;switch(this.type){case CKEDITOR.STYLE_OBJECT:return!!a.contains(this.element);case CKEDITOR.STYLE_BLOCK:return!!a.blockLimit.getDtd()[this.element]}return!0},checkElementMatch:function(a,b){var c=this._.definition;if(!a||!c.ignoreReadonly&&a.isReadOnly())return!1;var d=a.getName();if("string"==typeof this.element?
d==this.element:d in this.element){if(!b&&!a.hasAttributes())return!0;if(d=c._AC)c=d;else{var d={},e=0,g=c.attributes;if(g)for(var f in g)e++,d[f]=g[f];if(f=CKEDITOR.style.getStyleText(c))d.style||e++,d.style=f;d._length=e;c=c._AC=d}if(c._length){for(var h in c)if("_length"!=h)if(d=a.getAttribute(h)||"","style"==h?u(c[h],d):c[h]==d){if(!b)return!0}else if(b)return!1;if(b)return!0}else return!0}return!1},checkElementRemovable:function(a,b,c){if(this.checkElementMatch(a,b,c))return!0;if(b=G(this)[a.getName()]){var d;
if(!(b=b.attributes))return!0;for(c=0;c<b.length;c++)if(d=b[c][0],d=a.getAttribute(d)){var e=b[c][1];if(null===e)return!0;if("string"==typeof e){if(d==e)return!0}else if(e.test(d))return!0}}return!1},buildPreview:function(a){var b=this._.definition,c=[],d=b.element;"bdo"==d&&(d="span");var c=["\x3c",d],e=b.attributes;if(e)for(var g in e)c.push(" ",g,'\x3d"',e[g],'"');(e=CKEDITOR.style.getStyleText(b))&&c.push(' style\x3d"',e,'"');c.push("\x3e",a||b.name,"\x3c/",d,"\x3e");return c.join("")},getDefinition:function(){return this._.definition}};
CKEDITOR.style.getStyleText=function(a){var b=a._ST;if(b)return b;var b=a.styles,c=a.attributes&&a.attributes.style||"",d="";c.length&&(c=c.replace(t,";"));for(var e in b){var g=b[e],f=(e+":"+g).replace(t,";");"inherit"==g?d+=f:c+=f}c.length&&(c=CKEDITOR.tools.normalizeCssText(c,!0));return a._ST=c+d};CKEDITOR.style.customHandlers={};CKEDITOR.style.unstylableElements=[];CKEDITOR.style.addCustomHandler=function(a){var b=function(a){this._={definition:a};this.setup&&this.setup(a)};b.prototype=CKEDITOR.tools.extend(CKEDITOR.tools.prototypedCopy(CKEDITOR.style.prototype),
{assignedTo:CKEDITOR.STYLE_OBJECT},a,!0);return this.customHandlers[a.type]=b};var N=CKEDITOR.POSITION_PRECEDING|CKEDITOR.POSITION_IDENTICAL|CKEDITOR.POSITION_IS_CONTAINED,O=CKEDITOR.POSITION_FOLLOWING|CKEDITOR.POSITION_IDENTICAL|CKEDITOR.POSITION_IS_CONTAINED})();CKEDITOR.styleCommand=function(a,d){this.requiredContent=this.allowedContent=this.style=a;CKEDITOR.tools.extend(this,d,!0)};
CKEDITOR.styleCommand.prototype.exec=function(a){a.focus();this.state==CKEDITOR.TRISTATE_OFF?a.applyStyle(this.style):this.state==CKEDITOR.TRISTATE_ON&&a.removeStyle(this.style)};CKEDITOR.stylesSet=new CKEDITOR.resourceManager("","stylesSet");CKEDITOR.addStylesSet=CKEDITOR.tools.bind(CKEDITOR.stylesSet.add,CKEDITOR.stylesSet);CKEDITOR.loadStylesSet=function(a,d,b){CKEDITOR.stylesSet.addExternal(a,d,"");CKEDITOR.stylesSet.load(a,b)};
CKEDITOR.tools.extend(CKEDITOR.editor.prototype,{attachStyleStateChange:function(a,d){var b=this._.styleStateChangeCallbacks;b||(b=this._.styleStateChangeCallbacks=[],this.on("selectionChange",function(a){for(var d=0;d<b.length;d++){var e=b[d],k=e.style.checkActive(a.data.path,this)?CKEDITOR.TRISTATE_ON:CKEDITOR.TRISTATE_OFF;e.fn.call(this,k)}}));b.push({style:a,fn:d})},applyStyle:function(a){a.apply(this)},removeStyle:function(a){a.remove(this)},getStylesSet:function(a){if(this._.stylesDefinitions)a(this._.stylesDefinitions);
else{var d=this,b=d.config.stylesCombo_stylesSet||d.config.stylesSet;if(!1===b)a(null);else if(b instanceof Array)d._.stylesDefinitions=b,a(b);else{b||(b="default");var b=b.split(":"),c=b[0];CKEDITOR.stylesSet.addExternal(c,b[1]?b.slice(1).join(":"):CKEDITOR.getUrl("styles.js"),"");CKEDITOR.stylesSet.load(c,function(b){d._.stylesDefinitions=b[c];a(d._.stylesDefinitions)})}}}});
(function(){if(window.Promise)CKEDITOR.tools.promise=Promise;else{var a=CKEDITOR.getUrl("vendor/promise.js");if("function"===typeof window.define&&window.define.amd&&"function"===typeof window.require)return window.require([a],function(a){CKEDITOR.tools.promise=a});CKEDITOR.scriptLoader.load(a,function(d){if(!d)return CKEDITOR.error("no-vendor-lib",{path:a});if("undefined"!==typeof window.ES6Promise)return CKEDITOR.tools.promise=ES6Promise})}})();
(function(){function a(a,f,e){a.once("selectionCheck",function(a){if(!d){var c=a.data.getRanges()[0];e.equals(c)?a.cancel():f.equals(c)&&(b=!0)}},null,null,-1)}var d=!0,b=!1;CKEDITOR.dom.selection.setupEditorOptimization=function(a){a.on("selectionCheck",function(a){a.data&&!b&&a.data.optimizeInElementEnds();b=!1});a.on("contentDom",function(){var b=a.editable();b&&(b.attachListener(b,"keydown",function(a){this._.shiftPressed=a.data.$.shiftKey},this),b.attachListener(b,"keyup",function(a){this._.shiftPressed=
a.data.$.shiftKey},this))})};CKEDITOR.dom.selection.prototype.optimizeInElementEnds=function(){var b=this.getRanges()[0],f=this.root.editor,e;if(this.root.editor._.shiftPressed||this.isFake||b.isCollapsed||b.startContainer.equals(b.endContainer)||(b.endContainer.is?b.endContainer.is("li"):b.endContainer.getParent().is&&b.endContainer.getParent().is("li")))e=!1;else if(0===b.endOffset)e=!0;else{e=b.startContainer.type===CKEDITOR.NODE_TEXT;var k=b.endContainer.type===CKEDITOR.NODE_TEXT,h=e?b.startContainer.getLength():
b.startContainer.getChildCount();e=b.startOffset===h||e^k}e&&(e=b.clone(),b.shrink(CKEDITOR.SHRINK_TEXT,!1,{skipBogus:!CKEDITOR.env.webkit}),d=!1,a(f,b,e),b.select(),d=!0)}})();
(function(){function a(a,c){if(b(a))a=Math.round(c*(parseFloat(a)/100));else if("string"===typeof a&&a.match(/^\d+$/gm)||"string"===typeof a&&a.match(/^\d+(?:deg)?$/gm))a=parseInt(a,10);return a}function d(a,c){b(a)?a=c*(parseFloat(a)/100):"string"===typeof a&&a.match(/^\d?\.\d+/gm)&&(a=parseFloat(a));return a}function b(a){return"string"===typeof a&&a.match(/^((\d*\.\d+)|(\d+))%{1}$/gm)}function c(a,b,c){return!isNaN(a)&&a>=b&&a<=c}function f(a){a=a.toString(16);return 1==a.length?"0"+a:a}CKEDITOR.tools.color=
CKEDITOR.tools.createClass({$:function(a,b){this._.initialColorCode=a;this._.defaultValue=b;this._.parseInput(a)},proto:{getHex:function(){if(!this._.isValidColor)return this._.defaultValue;var a=this._.blendAlphaColor(this._.red,this._.green,this._.blue,this._.alpha);return this._.formatHexString(a[0],a[1],a[2])},getHexWithAlpha:function(){if(!this._.isValidColor)return this._.defaultValue;var a=Math.round(this._.alpha*CKEDITOR.tools.color.MAX_RGB_CHANNEL_VALUE);return this._.formatHexString(this._.red,
this._.green,this._.blue,a)},getRgb:function(){if(!this._.isValidColor)return this._.defaultValue;var a=this._.blendAlphaColor(this._.red,this._.green,this._.blue,this._.alpha);return this._.formatRgbString("rgb",a[0],a[1],a[2])},getRgba:function(){return this._.isValidColor?this._.formatRgbString("rgba",this._.red,this._.green,this._.blue,this._.alpha):this._.defaultValue},getHsl:function(){var a=0===this._.alpha||1===this._.alpha;if(!this._.isValidColor)return this._.defaultValue;this._.type===
CKEDITOR.tools.color.TYPE_HSL&&a?a=[this._.hue,this._.saturation,this._.lightness]:(a=this._.blendAlphaColor(this._.red,this._.green,this._.blue,this._.alpha),a=this._.rgbToHsl(a[0],a[1],a[2]));return this._.formatHslString("hsl",a[0],a[1],a[2])},getHsla:function(){var a;if(!this._.isValidColor)return this._.defaultValue;a=this._.type===CKEDITOR.tools.color.TYPE_HSL?[this._.hue,this._.saturation,this._.lightness]:this._.rgbToHsl(this._.red,this._.green,this._.blue);return this._.formatHslString("hsla",
a[0],a[1],a[2],this._.alpha)},getInitialValue:function(){return this._.initialColorCode}},_:{initialColorCode:"",isValidColor:!0,type:0,hue:0,saturation:0,lightness:0,red:0,green:0,blue:0,alpha:1,blendAlphaColor:function(a,b,c,d){return CKEDITOR.tools.array.map([a,b,c],function(a){return Math.round(CKEDITOR.tools.color.MAX_RGB_CHANNEL_VALUE-d*(CKEDITOR.tools.color.MAX_RGB_CHANNEL_VALUE-a))})},formatHexString:function(a,b,c,d){a="#"+f(a)+f(b)+f(c);void 0!==d&&(a+=f(d));return a.toUpperCase()},formatRgbString:function(a,
b,c,d,f){b=[b,c,d];void 0!==f&&b.push(f);return a+"("+b.join(",")+")"},formatHslString:function(a,b,c,d,f){return a+"("+b+","+c+"%,"+d+"%"+(void 0!==f?","+f:"")+")"},parseInput:function(a){if("string"!==typeof a)this._.isValidColor=!1;else{a=CKEDITOR.tools.trim(a);var b=this._.matchStringToNamedColor(a);b&&(a=b);var b=this._.extractColorChannelsFromHex(a),c=this._.extractColorChannelsFromRgba(a);a=this._.extractColorChannelsFromHsla(a);(a=b||c||a)?(this._.type=a.type,this._.red=a.red,this._.green=
a.green,this._.blue=a.blue,this._.alpha=a.alpha,a.type===CKEDITOR.tools.color.TYPE_HSL&&(this._.hue=a.hue,this._.saturation=a.saturation,this._.lightness=a.lightness)):this._.isValidColor=!1}},matchStringToNamedColor:function(a){return CKEDITOR.tools.color.namedColors[a.toLowerCase()]||null},extractColorChannelsFromHex:function(a){-1===a.indexOf("#")&&(a="#"+a);a.match(CKEDITOR.tools.color.hex3CharsRegExp)&&(a=this._.hex3ToHex6(a));a.match(CKEDITOR.tools.color.hex4CharsRegExp)&&(a=this._.hex4ToHex8(a));
if(!a.match(CKEDITOR.tools.color.hex6CharsRegExp)&&!a.match(CKEDITOR.tools.color.hex8CharsRegExp))return null;a=a.split("");var b=1;a[7]&&a[8]&&(b=parseInt(a[7]+a[8],16),b/=CKEDITOR.tools.color.MAX_RGB_CHANNEL_VALUE,b=Number(b.toFixed(1)));return{type:CKEDITOR.tools.color.TYPE_RGB,red:parseInt(a[1]+a[2],16),green:parseInt(a[3]+a[4],16),blue:parseInt(a[5]+a[6],16),alpha:b}},extractColorChannelsFromRgba:function(b){var c=this._.extractColorChannelsByPattern(b,CKEDITOR.tools.color.rgbRegExp);if(!c||
3>c.length||4<c.length)return null;var f=4===c.length;b=a(c[0],CKEDITOR.tools.color.MAX_RGB_CHANNEL_VALUE);var p=a(c[1],CKEDITOR.tools.color.MAX_RGB_CHANNEL_VALUE),m=a(c[2],CKEDITOR.tools.color.MAX_RGB_CHANNEL_VALUE),g=1;f&&(g=d(c[3],CKEDITOR.tools.color.MAX_ALPHA_CHANNEL_VALUE));c={type:CKEDITOR.tools.color.TYPE_RGB,red:b,green:p,blue:m,alpha:g};return this._.areColorChannelsValid(b,p,m,g)?c:null},extractColorChannelsFromHsla:function(b){var c=this._.extractColorChannelsByPattern(b,CKEDITOR.tools.color.hslRegExp);
if(!c||3>c.length||4<c.length)return null;var f=4===c.length,p=a(c[0],CKEDITOR.tools.color.MAX_HUE_CHANNEL_VALUE),m=d(c[1],CKEDITOR.tools.color.MAX_SATURATION_LIGHTNESS_CHANNEL_VALUE),g=d(c[2],CKEDITOR.tools.color.MAX_SATURATION_LIGHTNESS_CHANNEL_VALUE),x=1;b=this._.hslToRgb(p,m,g);f&&(x=d(c[3],CKEDITOR.tools.color.MAX_ALPHA_CHANNEL_VALUE));b.push(x);c={type:CKEDITOR.tools.color.TYPE_HSL,red:b[0],green:b[1],blue:b[2],alpha:b[3],hue:p,saturation:Math.round(100*m),lightness:Math.round(100*g)};return this._.areColorChannelsValid(b[0],
b[1],b[2],b[3])?c:null},hex3ToHex6:function(a){a=a.split("");return"#"+a[1]+a[1]+a[2]+a[2]+a[3]+a[3]},hex4ToHex8:function(a){return this._.hex3ToHex6(a.substr(0,4))+CKEDITOR.tools.repeat(a[4],2)},extractColorChannelsByPattern:function(a,b){var c=a.match(b);if(!c)return null;var d=-1===c[1].indexOf(",")?/\s/:",",d=c[1].split(d),d=CKEDITOR.tools.array.reduce(d,function(a,b){var c=CKEDITOR.tools.trim(b);return 0===c.length?a:a.concat([c])},[]);c[2]&&(c=CKEDITOR.tools.trim(c[2].replace(/[\/,]/,"")),d.push(c));
return d},areColorChannelsValid:function(a,b,d,f){return c(a,0,CKEDITOR.tools.color.MAX_RGB_CHANNEL_VALUE)&&c(b,0,CKEDITOR.tools.color.MAX_RGB_CHANNEL_VALUE)&&c(d,0,CKEDITOR.tools.color.MAX_RGB_CHANNEL_VALUE)&&c(f,0,CKEDITOR.tools.color.MAX_ALPHA_CHANNEL_VALUE)},hslToRgb:function(a,b,c){var d=function(d){var g=(d+a/30)%12;d=b*Math.min(c,1-c);g=Math.min(g-3,9-g,1);g=Math.max(-1,g);return Math.round((c-d*g)*CKEDITOR.tools.color.MAX_RGB_CHANNEL_VALUE)};return[d(0),d(8),d(4)]},rgbToHsl:function(a,b,c){a/=
CKEDITOR.tools.color.MAX_RGB_CHANNEL_VALUE;b/=CKEDITOR.tools.color.MAX_RGB_CHANNEL_VALUE;var d=c/CKEDITOR.tools.color.MAX_RGB_CHANNEL_VALUE,f=Math.max(a,b,d),g=Math.min(a,b,d);c=f-g;var x=0;switch(f){case a:x=(b-d)/c%6;break;case b:x=(d-a)/c+2;break;case d:x=(a-b)/c+4}a=0===c?0:60*x;b=(f+g)/2;f=0;1!==b&&0!==b&&(f=c/(1-Math.abs(2*b-1)));a=Math.round(a);f=Math.round(100*f);b=Math.round(100*b);return[a,f,b]}},statics:{TYPE_RGB:1,TYPE_HSL:2,MAX_RGB_CHANNEL_VALUE:255,MAX_ALPHA_CHANNEL_VALUE:1,MAX_HUE_CHANNEL_VALUE:360,
MAX_SATURATION_LIGHTNESS_CHANNEL_VALUE:1,hex3CharsRegExp:/#([0-9a-f]{3}$)/gim,hex4CharsRegExp:/#([0-9a-f]{4}$)/gim,hex6CharsRegExp:/#([0-9a-f]{6}$)/gim,hex8CharsRegExp:/#([0-9a-f]{8}$)/gim,rgbRegExp:/rgba?\(([.,\d\s%]*)(\s*\/\s*[\d.%]+)?\s*\)/i,hslRegExp:/hsla?\((\s*(?:[.\d]+(?:deg)?)(?:\s*,?\s*[.\d]+%){2})((?:(?:\s*\/\s*)|(?:\s*,\s*))[\d.]+%?)?\s*\)/i,namedColors:{aliceblue:"#F0F8FF",antiquewhite:"#FAEBD7",aqua:"#00FFFF",aquamarine:"#7FFFD4",azure:"#F0FFFF",beige:"#F5F5DC",bisque:"#FFE4C4",black:"#000000",
blanchedalmond:"#FFEBCD",blue:"#0000FF",blueviolet:"#8A2BE2",brown:"#A52A2A",burlywood:"#DEB887",cadetblue:"#5F9EA0",chartreuse:"#7FFF00",chocolate:"#D2691E",coral:"#FF7F50",cornflowerblue:"#6495ED",cornsilk:"#FFF8DC",crimson:"#DC143C",cyan:"#00FFFF",darkblue:"#00008B",darkcyan:"#008B8B",darkgoldenrod:"#B8860B",darkgray:"#A9A9A9",darkgreen:"#006400",darkgrey:"#A9A9A9",darkkhaki:"#BDB76B",darkmagenta:"#8B008B",darkolivegreen:"#556B2F",darkorange:"#FF8C00",darkorchid:"#9932CC",darkred:"#8B0000",darksalmon:"#E9967A",
darkseagreen:"#8FBC8F",darkslateblue:"#483D8B",darkslategray:"#2F4F4F",darkslategrey:"#2F4F4F",darkturquoise:"#00CED1",darkviolet:"#9400D3",deeppink:"#FF1493",deepskyblue:"#00BFFF",dimgray:"#696969",dimgrey:"#696969",dodgerblue:"#1E90FF",firebrick:"#B22222",floralwhite:"#FFFAF0",forestgreen:"#228B22",fuchsia:"#FF00FF",gainsboro:"#DCDCDC",ghostwhite:"#F8F8FF",gold:"#FFD700",goldenrod:"#DAA520",gray:"#808080",green:"#008000",greenyellow:"#ADFF2F",grey:"#808080",honeydew:"#F0FFF0",hotpink:"#FF69B4",
indianred:"#CD5C5C",indigo:"#4B0082",ivory:"#FFFFF0",khaki:"#F0E68C",lavender:"#E6E6FA",lavenderblush:"#FFF0F5",lawngreen:"#7CFC00",lemonchiffon:"#FFFACD",lightblue:"#ADD8E6",lightcoral:"#F08080",lightcyan:"#E0FFFF",lightgoldenrodyellow:"#FAFAD2",lightgray:"#D3D3D3",lightgreen:"#90EE90",lightgrey:"#D3D3D3",lightpink:"#FFB6C1",lightsalmon:"#FFA07A",lightseagreen:"#20B2AA",lightskyblue:"#87CEFA",lightslategray:"#778899",lightslategrey:"#778899",lightsteelblue:"#B0C4DE",lightyellow:"#FFFFE0",lime:"#00FF00",
limegreen:"#32CD32",linen:"#FAF0E6",magenta:"#FF00FF",maroon:"#800000",mediumaquamarine:"#66CDAA",mediumblue:"#0000CD",mediumorchid:"#BA55D3",mediumpurple:"#9370DB",mediumseagreen:"#3CB371",mediumslateblue:"#7B68EE",mediumspringgreen:"#00FA9A",mediumturquoise:"#48D1CC",mediumvioletred:"#C71585",midnightblue:"#191970",mintcream:"#F5FFFA",mistyrose:"#FFE4E1",moccasin:"#FFE4B5",navajowhite:"#FFDEAD",navy:"#000080",oldlace:"#FDF5E6",olive:"#808000",olivedrab:"#6B8E23",orange:"#FFA500",orangered:"#FF4500",
orchid:"#DA70D6",palegoldenrod:"#EEE8AA",palegreen:"#98FB98",paleturquoise:"#AFEEEE",palevioletred:"#DB7093",papayawhip:"#FFEFD5",peachpuff:"#FFDAB9",peru:"#CD853F",pink:"#FFC0CB",plum:"#DDA0DD",powderblue:"#B0E0E6",purple:"#800080",rebeccapurple:"#663399",red:"#FF0000",rosybrown:"#BC8F8F",royalblue:"#4169E1",saddlebrown:"#8B4513",salmon:"#FA8072",sandybrown:"#F4A460",seagreen:"#2E8B57",seashell:"#FFF5EE",sienna:"#A0522D",silver:"#C0C0C0",skyblue:"#87CEEB",slateblue:"#6A5ACD",slategray:"#708090",
slategrey:"#708090",snow:"#FFFAFA",springgreen:"#00FF7F",steelblue:"#4682B4",tan:"#D2B48C",teal:"#008080",thistle:"#D8BFD8",tomato:"#FF6347",turquoise:"#40E0D0",violet:"#EE82EE",windowtext:"windowtext",wheat:"#F5DEB3",white:"#FFFFFF",whitesmoke:"#F5F5F5",yellow:"#FFFF00",yellowgreen:"#9ACD32"}}});CKEDITOR.tools.style.parse._colors=CKEDITOR.tools.color.namedColors})();CKEDITOR.dom.comment=function(a,d){"string"==typeof a&&(a=(d?d.$:document).createComment(a));CKEDITOR.dom.domObject.call(this,a)};
CKEDITOR.dom.comment.prototype=new CKEDITOR.dom.node;CKEDITOR.tools.extend(CKEDITOR.dom.comment.prototype,{type:CKEDITOR.NODE_COMMENT,getOuterHtml:function(){return"\x3c!--"+this.$.nodeValue+"--\x3e"}});"use strict";
(function(){var a={},d={},b;for(b in CKEDITOR.dtd.$blockLimit)b in CKEDITOR.dtd.$list||(a[b]=1);for(b in CKEDITOR.dtd.$block)b in CKEDITOR.dtd.$blockLimit||b in CKEDITOR.dtd.$empty||(d[b]=1);CKEDITOR.dom.elementPath=function(b,f){var e=null,k=null,h=[],p=b,m;f=f||b.getDocument().getBody();p||(p=f);do if(p.type==CKEDITOR.NODE_ELEMENT){h.push(p);if(!this.lastElement&&(this.lastElement=p,p.is(CKEDITOR.dtd.$object)||"false"==p.getAttribute("contenteditable")))continue;if(p.equals(f))break;if(!k&&(m=p.getName(),
"true"==p.getAttribute("contenteditable")?k=p:!e&&d[m]&&(e=p),a[m])){if(m=!e&&"div"==m){a:{m=p.getChildren();for(var g=0,x=m.count();g<x;g++){var y=m.getItem(g);if(y.type==CKEDITOR.NODE_ELEMENT&&CKEDITOR.dtd.$block[y.getName()]){m=!0;break a}}m=!1}m=!m}m?e=p:k=p}}while(p=p.getParent());k||(k=f);this.block=e;this.blockLimit=k;this.root=f;this.elements=h}})();
CKEDITOR.dom.elementPath.prototype={compare:function(a){var d=this.elements;a=a&&a.elements;if(!a||d.length!=a.length)return!1;for(var b=0;b<d.length;b++)if(!d[b].equals(a[b]))return!1;return!0},contains:function(a,d,b){var c=0,f;"string"==typeof a&&(f=function(b){return b.getName()==a});a instanceof CKEDITOR.dom.element?f=function(b){return b.equals(a)}:CKEDITOR.tools.isArray(a)?f=function(b){return-1<CKEDITOR.tools.indexOf(a,b.getName())}:"function"==typeof a?f=a:"object"==typeof a&&(f=function(b){return b.getName()in
a});var e=this.elements,k=e.length;d&&(b?c+=1:--k);b&&(e=Array.prototype.slice.call(e,0),e.reverse());for(;c<k;c++)if(f(e[c]))return e[c];return null},isContextFor:function(a){var d;return a in CKEDITOR.dtd.$block?(d=this.contains(CKEDITOR.dtd.$intermediate)||this.root.equals(this.block)&&this.block||this.blockLimit,!!d.getDtd()[a]):!0},direction:function(){return(this.block||this.blockLimit||this.root).getDirection(1)}};
CKEDITOR.dom.text=function(a,d){"string"==typeof a&&(a=(d?d.$:document).createTextNode(a));this.$=a};CKEDITOR.dom.text.prototype=new CKEDITOR.dom.node;
CKEDITOR.tools.extend(CKEDITOR.dom.text.prototype,{type:CKEDITOR.NODE_TEXT,getLength:function(){return this.$.nodeValue.length},getText:function(){return this.$.nodeValue},setText:function(a){this.$.nodeValue=a},isEmpty:function(a){var d=this.getText();a&&(d=CKEDITOR.tools.trim(d));return!d||d===CKEDITOR.dom.selection.FILLING_CHAR_SEQUENCE},split:function(a){var d=this.$.parentNode,b=d.childNodes.length,c=this.getLength(),f=this.getDocument(),e=new CKEDITOR.dom.text(this.$.splitText(a),f);d.childNodes.length==
b&&(a>=c?(e=f.createText(""),e.insertAfter(this)):(a=f.createText(""),a.insertAfter(e),a.remove()));return e},substring:function(a,d){return"number"!=typeof d?this.$.nodeValue.substr(a):this.$.nodeValue.substring(a,d)}});
(function(){function a(a,c,d){var e=a.serializable,k=c[d?"endContainer":"startContainer"],h=d?"endOffset":"startOffset",p=e?c.document.getById(a.startNode):a.startNode;a=e?c.document.getById(a.endNode):a.endNode;k.equals(p.getPrevious())?(c.startOffset=c.startOffset-k.getLength()-a.getPrevious().getLength(),k=a.getNext()):k.equals(a.getPrevious())&&(c.startOffset-=k.getLength(),k=a.getNext());k.equals(p.getParent())&&c[h]++;k.equals(a.getParent())&&c[h]++;c[d?"endContainer":"startContainer"]=k;return c}
CKEDITOR.dom.rangeList=function(a){if(a instanceof CKEDITOR.dom.rangeList)return a;a?a instanceof CKEDITOR.dom.range&&(a=[a]):a=[];return CKEDITOR.tools.extend(a,d)};var d={createIterator:function(){var a=this,c=CKEDITOR.dom.walker.bookmark(),d=[],e;return{getNextRange:function(k){e=void 0===e?0:e+1;var h=a[e];if(h&&1<a.length){if(!e)for(var p=a.length-1;0<=p;p--)d.unshift(a[p].createBookmark(!0));if(k)for(var m=0;a[e+m+1];){var g=h.document;k=0;p=g.getById(d[m].endNode);for(g=g.getById(d[m+1].startNode);;){p=
p.getNextSourceNode(!1);if(g.equals(p))k=1;else if(c(p)||p.type==CKEDITOR.NODE_ELEMENT&&p.isBlockBoundary())continue;break}if(!k)break;m++}for(h.moveToBookmark(d.shift());m--;)p=a[++e],p.moveToBookmark(d.shift()),h.setEnd(p.endContainer,p.endOffset)}return h}}},createBookmarks:function(b){for(var c=[],d,e=0;e<this.length;e++){c.push(d=this[e].createBookmark(b,!0));for(var k=e+1;k<this.length;k++)this[k]=a(d,this[k]),this[k]=a(d,this[k],!0)}return c},createBookmarks2:function(a){for(var c=[],d=0;d<
this.length;d++)c.push(this[d].createBookmark2(a));return c},moveToBookmarks:function(a){for(var c=0;c<this.length;c++)this[c].moveToBookmark(a[c])}}})();
(function(){function a(){return CKEDITOR.getUrl(CKEDITOR.skinName.split(",")[1]||"skins/"+CKEDITOR.skinName.split(",")[0]+"/")}function d(b){var c=CKEDITOR.skin["ua_"+b],d=CKEDITOR.env;if(c)for(var c=c.split(",").sort(function(a,b){return a>b?-1:1}),e=0,f;e<c.length;e++)if(f=c[e],d.ie&&(f.replace(/^ie/,"")==d.version||d.quirks&&"iequirks"==f)&&(f="ie"),d[f]){b+="_"+c[e];break}return CKEDITOR.getUrl(a()+b+".css")}function b(a,b){e[a]||(CKEDITOR.document.appendStyleSheet(d(a)),e[a]=1);b&&b()}function c(a){var b=
a.getById(k);b||(b=a.getHead().append("style"),b.setAttribute("id",k),b.setAttribute("type","text/css"));return b}function f(a,b,c){var d,e,f;if(CKEDITOR.env.webkit)for(b=b.split("}").slice(0,-1),e=0;e<b.length;e++)b[e]=b[e].split("{");for(var h=0;h<a.length;h++)if(CKEDITOR.env.webkit)for(e=0;e<b.length;e++){f=b[e][1];for(d=0;d<c.length;d++)f=f.replace(c[d][0],c[d][1]);a[h].$.sheet.addRule(b[e][0],f)}else{f=b;for(d=0;d<c.length;d++)f=f.replace(c[d][0],c[d][1]);CKEDITOR.env.ie&&11>CKEDITOR.env.version?
a[h].$.styleSheet.cssText+=f:a[h].$.innerHTML+=f}}var e={};CKEDITOR.skin={path:a,loadPart:function(c,d){CKEDITOR.skin.name!=CKEDITOR.skinName.split(",")[0]?CKEDITOR.scriptLoader.load(CKEDITOR.getUrl(a()+"skin.js"),function(){b(c,d)}):b(c,d)},getPath:function(a){return CKEDITOR.getUrl(d(a))},icons:{},addIcon:function(a,b,c,d){a=a.toLowerCase();this.icons[a]||(this.icons[a]={path:b,offset:c||0,bgsize:d||"16px"})},getIconStyle:function(a,b,c,d,e){var f;a&&(a=a.toLowerCase(),b&&(f=this.icons[a+"-rtl"]),
f||(f=this.icons[a]));a=c||f&&f.path||"";d=d||f&&f.offset;e=e||f&&f.bgsize||"16px";a&&(a=a.replace(/'/g,"\\'"));return a&&"background-image:url('"+CKEDITOR.getUrl(a)+"');background-position:0 "+d+"px;background-size:"+e+";"}};CKEDITOR.tools.extend(CKEDITOR.editor.prototype,{getUiColor:function(){return this.uiColor},setUiColor:function(a){var b=c(CKEDITOR.document);return(this.setUiColor=function(a){this.uiColor=a;var c=CKEDITOR.skin.chameleon,d="",e="";"function"==typeof c&&(d=c(this,"editor"),e=
c(this,"panel"));a=[[p,a]];f([b],d,a);f(h,e,a)}).call(this,a)}});var k="cke_ui_color",h=[],p=/\$color/g;CKEDITOR.on("instanceLoaded",function(a){if(!CKEDITOR.env.ie||!CKEDITOR.env.quirks){var b=a.editor;a=function(a){a=(a.data[0]||a.data).element.getElementsByTag("iframe").getItem(0).getFrameDocument();if(!a.getById("cke_ui_color")){var d=c(a);h.push(d);b.on("destroy",function(){h=CKEDITOR.tools.array.filter(h,function(a){return d!==a})});(a=b.getUiColor())&&f([d],CKEDITOR.skin.chameleon(b,"panel"),
[[p,a]])}};b.on("panelShow",a);b.on("menuShow",a);b.config.uiColor&&b.setUiColor(b.config.uiColor)}})})();
(function(){var a=CKEDITOR.dom.element.createFromHtml('\x3cdiv style\x3d"width:0;height:0;position:absolute;left:-10000px;border:1px solid;border-color:red blue"\x3e\x3c/div\x3e',CKEDITOR.document);a.appendTo(CKEDITOR.document.getHead());try{var d=a.getComputedStyle("border-top-color"),b=a.getComputedStyle("border-right-color");CKEDITOR.env.hc=!(!d||d!=b)}catch(c){CKEDITOR.env.hc=!1}a.remove();CKEDITOR.env.hc&&(CKEDITOR.env.cssClass+=" cke_hc");CKEDITOR.document.appendStyleText(".cke{visibility:hidden;}");
CKEDITOR.status="loaded";CKEDITOR.fireOnce("loaded");if(a=CKEDITOR._.pending)for(delete CKEDITOR._.pending,d=0;d<a.length;d++)CKEDITOR.editor.prototype.constructor.apply(a[d][0],a[d][1]),CKEDITOR.add(a[d][0])})();CKEDITOR.skin.name="n1theme";CKEDITOR.skin.ua_editor="ie,iequirks,ie8,gecko";CKEDITOR.skin.ua_dialog="ie,iequirks,ie8";CKEDITOR.plugins.add("dialogui",{onLoad:function(){var h=function(b){this._||(this._={});this._["default"]=this._.initValue=b["default"]||"";this._.required=b.required||!1;for(var a=[this._],d=1;d<arguments.length;d++)a.push(arguments[d]);a.push(!0);CKEDITOR.tools.extend.apply(CKEDITOR.tools,a);return this._},r={build:function(b,a,d){return new CKEDITOR.ui.dialog.textInput(b,a,d)}},n={build:function(b,a,d){return new CKEDITOR.ui.dialog[a.type](b,a,d)}},q={isChanged:function(){return this.getValue()!=
this.getInitValue()},reset:function(b){this.setValue(this.getInitValue(),b)},setInitValue:function(){this._.initValue=this.getValue()},resetInitValue:function(){this._.initValue=this._["default"]},getInitValue:function(){return this._.initValue}},v=CKEDITOR.tools.extend({},CKEDITOR.ui.dialog.uiElement.prototype.eventProcessors,{onChange:function(b,a){this._.domOnChangeRegistered||(b.on("load",function(){this.getInputElement().on("change",function(){b.parts.dialog.isVisible()&&this.fire("change",{value:this.getValue()})},
this)},this),this._.domOnChangeRegistered=!0);this.on("change",a)}},!0),x=/^on([A-Z]\w+)/,t=function(b){for(var a in b)(x.test(a)||"title"==a||"type"==a)&&delete b[a];return b},w=function(b){b=b.data.getKeystroke();b==CKEDITOR.SHIFT+CKEDITOR.ALT+36?this.setDirectionMarker("ltr"):b==CKEDITOR.SHIFT+CKEDITOR.ALT+35&&this.setDirectionMarker("rtl")};CKEDITOR.tools.extend(CKEDITOR.ui.dialog,{labeledElement:function(b,a,d,f){if(!(4>arguments.length)){var c=h.call(this,a);c.labelId=CKEDITOR.tools.getNextId()+
"_label";this._.children=[];var e={role:a.role||"presentation"};a.includeLabel&&(e["aria-labelledby"]=c.labelId);CKEDITOR.ui.dialog.uiElement.call(this,b,a,d,"div",null,e,function(){var e=[],g=a.required?" cke_required":"";"horizontal"!=a.labelLayout?e.push('\x3clabel class\x3d"cke_dialog_ui_labeled_label'+g+'" ',' id\x3d"'+c.labelId+'"',c.inputId?' for\x3d"'+c.inputId+'"':"",(a.labelStyle?' style\x3d"'+a.labelStyle+'"':"")+"\x3e",a.required?a.label+'\x3cspan class\x3d"cke_dialog_ui_labeled_required" aria-hidden\x3d"true"\x3e*\x3c/span\x3e':
a.label,"\x3c/label\x3e",'\x3cdiv class\x3d"cke_dialog_ui_labeled_content"',a.controlStyle?' style\x3d"'+a.controlStyle+'"':"",' role\x3d"presentation"\x3e',f.call(this,b,a),"\x3c/div\x3e"):(g={type:"hbox",widths:a.widths,padding:0,children:[{type:"html",html:'\x3clabel class\x3d"cke_dialog_ui_labeled_label'+g+'" id\x3d"'+c.labelId+'" for\x3d"'+c.inputId+'"'+(a.labelStyle?' style\x3d"'+a.labelStyle+'"':"")+"\x3e"+CKEDITOR.tools.htmlEncode(a.label)+"\x3c/label\x3e"},{type:"html",html:'\x3cspan class\x3d"cke_dialog_ui_labeled_content"'+
(a.controlStyle?' style\x3d"'+a.controlStyle+'"':"")+"\x3e"+f.call(this,b,a)+"\x3c/span\x3e"}]},CKEDITOR.dialog._.uiElementBuilders.hbox.build(b,g,e));return e.join("")})}},textInput:function(b,a,d){if(!(3>arguments.length)){h.call(this,a);var f=this._.inputId=CKEDITOR.tools.getNextId()+"_textInput",c={"class":"cke_dialog_ui_input_"+a.type,id:f,type:a.type};a.validate&&(this.validate=a.validate);a.maxLength&&(c.maxlength=a.maxLength);a.size&&(c.size=a.size);a.inputStyle&&(c.style=a.inputStyle);var e=
this,m=!1;b.on("load",function(){e.getInputElement().on("keydown",function(a){13==a.data.getKeystroke()&&(m=!0)});e.getInputElement().on("keyup",function(a){13==a.data.getKeystroke()&&m&&(b.getButton("ok")&&setTimeout(function(){b.getButton("ok").click()},0),m=!1);e.bidi&&w.call(e,a)},null,null,1E3)});CKEDITOR.ui.dialog.labeledElement.call(this,b,a,d,function(){var b=['\x3cdiv class\x3d"cke_dialog_ui_input_',a.type,'" role\x3d"presentation"'];a.width&&b.push('style\x3d"width:'+a.width+'" ');b.push("\x3e\x3cinput ");
c["aria-labelledby"]=this._.labelId;this._.required&&(c["aria-required"]=this._.required);for(var e in c)b.push(e+'\x3d"'+c[e]+'" ');b.push(" /\x3e\x3c/div\x3e");return b.join("")})}},textarea:function(b,a,d){if(!(3>arguments.length)){h.call(this,a);var f=this,c=this._.inputId=CKEDITOR.tools.getNextId()+"_textarea",e={};a.validate&&(this.validate=a.validate);e.rows=a.rows||5;e.cols=a.cols||20;e["class"]="cke_dialog_ui_input_textarea "+(a["class"]||"");"undefined"!=typeof a.inputStyle&&(e.style=a.inputStyle);
a.dir&&(e.dir=a.dir);if(f.bidi)b.on("load",function(){f.getInputElement().on("keyup",w)},f);CKEDITOR.ui.dialog.labeledElement.call(this,b,a,d,function(){e["aria-labelledby"]=this._.labelId;this._.required&&(e["aria-required"]=this._.required);var a=['\x3cdiv class\x3d"cke_dialog_ui_input_textarea" role\x3d"presentation"\x3e\x3ctextarea id\x3d"',c,'" '],b;for(b in e)a.push(b+'\x3d"'+CKEDITOR.tools.htmlEncode(e[b])+'" ');a.push("\x3e",CKEDITOR.tools.htmlEncode(f._["default"]),"\x3c/textarea\x3e\x3c/div\x3e");
return a.join("")})}},checkbox:function(b,a,d){if(!(3>arguments.length)){var f=h.call(this,a,{"default":!!a["default"]});a.validate&&(this.validate=a.validate);CKEDITOR.ui.dialog.uiElement.call(this,b,a,d,"span",null,null,function(){var c=CKEDITOR.tools.extend({},a,{id:a.id?a.id+"_checkbox":CKEDITOR.tools.getNextId()+"_checkbox"},!0),e=[],d=CKEDITOR.tools.getNextId()+"_label",g={"class":"cke_dialog_ui_checkbox_input",type:"checkbox","aria-labelledby":d};t(c);a["default"]&&(g.checked="checked");"undefined"!=
typeof c.inputStyle&&(c.style=c.inputStyle);f.checkbox=new CKEDITOR.ui.dialog.uiElement(b,c,e,"input",null,g);e.push(' \x3clabel id\x3d"',d,'" for\x3d"',g.id,'"'+(a.labelStyle?' style\x3d"'+a.labelStyle+'"':"")+"\x3e",CKEDITOR.tools.htmlEncode(a.label),"\x3c/label\x3e");return e.join("")})}},radio:function(b,a,d){if(!(3>arguments.length)){h.call(this,a);this._["default"]||(this._["default"]=this._.initValue=a.items[0][1]);a.validate&&(this.validate=a.validate);var f=[],c=this;a.role="radiogroup";
a.includeLabel=!0;CKEDITOR.ui.dialog.labeledElement.call(this,b,a,d,function(){for(var e=[],d=[],g=(a.id?a.id:CKEDITOR.tools.getNextId())+"_radio",k=0;k<a.items.length;k++){var l=a.items[k],h=void 0!==l[2]?l[2]:l[0],n=void 0!==l[1]?l[1]:l[0],p=CKEDITOR.tools.getNextId()+"_radio_input",q=p+"_label",p=CKEDITOR.tools.extend({},a,{id:p,title:null,type:null},!0),h=CKEDITOR.tools.extend({},p,{title:h},!0),r={type:"radio","class":"cke_dialog_ui_radio_input",name:g,value:n,"aria-labelledby":q},u=[];c._["default"]==
n&&(r.checked="checked");t(p);t(h);"undefined"!=typeof p.inputStyle&&(p.style=p.inputStyle);p.keyboardFocusable=!0;f.push(new CKEDITOR.ui.dialog.uiElement(b,p,u,"input",null,r));u.push(" ");new CKEDITOR.ui.dialog.uiElement(b,h,u,"label",null,{id:q,"for":r.id},l[0]);e.push(u.join(""))}new CKEDITOR.ui.dialog.hbox(b,f,e,d);return d.join("")});this._.children=f}},button:function(b,a,d){if(arguments.length){"function"==typeof a&&(a=a(b.getParentEditor()));h.call(this,a,{disabled:a.disabled||!1});CKEDITOR.event.implementOn(this);
var f=this;b.on("load",function(){var a=this.getElement();(function(){a.on("click",function(a){f.click();a.data.preventDefault()});a.on("keydown",function(a){a.data.getKeystroke()in{32:1}&&(f.click(),a.data.preventDefault())})})();a.unselectable()},this);var c=CKEDITOR.tools.extend({},a);delete c.style;var e=CKEDITOR.tools.getNextId()+"_label";CKEDITOR.ui.dialog.uiElement.call(this,b,c,d,"a",null,{style:a.style,href:"javascript:void(0)",title:a.label,hidefocus:"true","class":a["class"],role:"button",
"aria-labelledby":e},'\x3cspan id\x3d"'+e+'" class\x3d"cke_dialog_ui_button"\x3e'+CKEDITOR.tools.htmlEncode(a.label)+"\x3c/span\x3e")}},select:function(b,a,d){if(!(3>arguments.length)){var f=h.call(this,a);a.validate&&(this.validate=a.validate);f.inputId=CKEDITOR.tools.getNextId()+"_select";CKEDITOR.ui.dialog.labeledElement.call(this,b,a,d,function(){var c=CKEDITOR.tools.extend({},a,{id:a.id?a.id+"_select":CKEDITOR.tools.getNextId()+"_select"},!0),e=[],d=[],g={id:f.inputId,"class":"cke_dialog_ui_input_select",
"aria-labelledby":this._.labelId};e.push('\x3cdiv class\x3d"cke_dialog_ui_input_',a.type,'" role\x3d"presentation"');a.width&&e.push('style\x3d"width:'+a.width+'" ');e.push("\x3e");void 0!==a.size&&(g.size=a.size);void 0!==a.multiple&&(g.multiple=a.multiple);t(c);for(var k=0,l;k<a.items.length&&(l=a.items[k]);k++)d.push('\x3coption value\x3d"',CKEDITOR.tools.htmlEncode(void 0!==l[1]?l[1]:l[0]).replace(/"/g,"\x26quot;"),'" /\x3e ',CKEDITOR.tools.htmlEncode(l[0]));"undefined"!=typeof c.inputStyle&&
(c.style=c.inputStyle);f.select=new CKEDITOR.ui.dialog.uiElement(b,c,e,"select",null,g,d.join(""));e.push("\x3c/div\x3e");return e.join("")})}},file:function(b,a,d){if(!(3>arguments.length)){void 0===a["default"]&&(a["default"]="");var f=CKEDITOR.tools.extend(h.call(this,a),{definition:a,buttons:[]});a.validate&&(this.validate=a.validate);b.on("load",function(){CKEDITOR.document.getById(f.frameId).getParent().addClass("cke_dialog_ui_input_file")});CKEDITOR.ui.dialog.labeledElement.call(this,b,a,d,
function(){f.frameId=CKEDITOR.tools.getNextId()+"_fileInput";var b=['\x3ciframe frameborder\x3d"0" allowtransparency\x3d"0" class\x3d"cke_dialog_ui_input_file" role\x3d"presentation" id\x3d"',f.frameId,'" title\x3d"',a.label,'" src\x3d"javascript:void('];b.push(CKEDITOR.env.ie?"(function(){"+encodeURIComponent("document.open();("+CKEDITOR.tools.fixDomain+")();document.close();")+"})()":"0");b.push(')"\x3e\x3c/iframe\x3e');return b.join("")})}},fileButton:function(b,a,d){var f=this;if(!(3>arguments.length)){h.call(this,
a);a.validate&&(this.validate=a.validate);var c=CKEDITOR.tools.extend({},a),e=c.onClick;c.className=(c.className?c.className+" ":"")+"cke_dialog_ui_button";c.onClick=function(c){var d=a["for"];c=e?e.call(this,c):!1;!1!==c&&("xhr"!==c&&b.getContentElement(d[0],d[1]).submit(),this.disable())};b.on("load",function(){b.getContentElement(a["for"][0],a["for"][1])._.buttons.push(f)});CKEDITOR.ui.dialog.button.call(this,b,c,d)}},html:function(){var b=/^\s*<[\w:]+\s+([^>]*)?>/,a=/^(\s*<[\w:]+(?:\s+[^>]*)?)((?:.|\r|\n)+)$/,
d=/\/$/;return function(f,c,e){if(!(3>arguments.length)){var m=[],g=c.html;"\x3c"!=g.charAt(0)&&(g="\x3cspan\x3e"+g+"\x3c/span\x3e");var k=c.focus;if(k){var l=this.focus;this.focus=function(){("function"==typeof k?k:l).call(this);this.fire("focus")};c.isFocusable&&(this.isFocusable=this.isFocusable);this.keyboardFocusable=!0}CKEDITOR.ui.dialog.uiElement.call(this,f,c,m,"span",null,null,"");m=m.join("").match(b);g=g.match(a)||["","",""];d.test(g[1])&&(g[1]=g[1].slice(0,-1),g[2]="/"+g[2]);e.push([g[1],
" ",m[1]||"",g[2]].join(""))}}}(),fieldset:function(b,a,d,f,c){var e=c.label;this._={children:a};CKEDITOR.ui.dialog.uiElement.call(this,b,c,f,"fieldset",null,null,function(){var a=[];e&&a.push("\x3clegend"+(c.labelStyle?' style\x3d"'+c.labelStyle+'"':"")+"\x3e"+e+"\x3c/legend\x3e");for(var b=0;b<d.length;b++)a.push(d[b]);return a.join("")})}},!0);CKEDITOR.ui.dialog.html.prototype=new CKEDITOR.ui.dialog.uiElement;CKEDITOR.ui.dialog.labeledElement.prototype=CKEDITOR.tools.extend(new CKEDITOR.ui.dialog.uiElement,
{setLabel:function(b){var a=CKEDITOR.document.getById(this._.labelId);1>a.getChildCount()?(new CKEDITOR.dom.text(b,CKEDITOR.document)).appendTo(a):a.getChild(0).$.nodeValue=b;return this},getLabel:function(){var b=CKEDITOR.document.getById(this._.labelId);return!b||1>b.getChildCount()?"":b.getChild(0).getText()},eventProcessors:v},!0);CKEDITOR.ui.dialog.button.prototype=CKEDITOR.tools.extend(new CKEDITOR.ui.dialog.uiElement,{click:function(){return this._.disabled?!1:this.fire("click",{dialog:this._.dialog})},
enable:function(){this._.disabled=!1;var b=this.getElement();b&&b.removeClass("cke_disabled")},disable:function(){this._.disabled=!0;this.getElement().addClass("cke_disabled")},isVisible:function(){return this.getElement().getFirst().isVisible()},isEnabled:function(){return!this._.disabled},eventProcessors:CKEDITOR.tools.extend({},CKEDITOR.ui.dialog.uiElement.prototype.eventProcessors,{onClick:function(b,a){this.on("click",function(){a.apply(this,arguments)})}},!0),accessKeyUp:function(){this.click()},
accessKeyDown:function(){this.focus()},keyboardFocusable:!0},!0);CKEDITOR.ui.dialog.textInput.prototype=CKEDITOR.tools.extend(new CKEDITOR.ui.dialog.labeledElement,{getInputElement:function(){return CKEDITOR.document.getById(this._.inputId)},focus:function(){var b=this.selectParentTab();setTimeout(function(){var a=b.getInputElement();a&&a.$.focus()},0)},select:function(){var b=this.selectParentTab();setTimeout(function(){var a=b.getInputElement();a&&(a.$.focus(),a.$.select())},0)},accessKeyUp:function(){this.select()},
setValue:function(b){if(this.bidi){var a=b&&b.charAt(0);(a="‪"==a?"ltr":"‫"==a?"rtl":null)&&(b=b.slice(1));this.setDirectionMarker(a)}b||(b="");return CKEDITOR.ui.dialog.uiElement.prototype.setValue.apply(this,arguments)},getValue:function(){var b=CKEDITOR.ui.dialog.uiElement.prototype.getValue.call(this);if(this.bidi&&b){var a=this.getDirectionMarker();a&&(b=("ltr"==a?"‪":"‫")+b)}return b},setDirectionMarker:function(b){var a=this.getInputElement();b?a.setAttributes({dir:b,"data-cke-dir-marker":b}):
this.getDirectionMarker()&&a.removeAttributes(["dir","data-cke-dir-marker"])},getDirectionMarker:function(){return this.getInputElement().data("cke-dir-marker")},keyboardFocusable:!0},q,!0);CKEDITOR.ui.dialog.textarea.prototype=new CKEDITOR.ui.dialog.textInput;CKEDITOR.ui.dialog.select.prototype=CKEDITOR.tools.extend(new CKEDITOR.ui.dialog.labeledElement,{getInputElement:function(){return this._.select.getElement()},add:function(b,a,d){var f=new CKEDITOR.dom.element("option",this.getDialog().getParentEditor().document),
c=this.getInputElement().$;f.$.text=b;f.$.value=void 0===a||null===a?b:a;void 0===d||null===d?CKEDITOR.env.ie?c.add(f.$):c.add(f.$,null):c.add(f.$,d);return this},remove:function(b){this.getInputElement().$.remove(b);return this},clear:function(){for(var b=this.getInputElement().$;0<b.length;)b.remove(0);return this},keyboardFocusable:!0},q,!0);CKEDITOR.ui.dialog.checkbox.prototype=CKEDITOR.tools.extend(new CKEDITOR.ui.dialog.uiElement,{getInputElement:function(){return this._.checkbox.getElement()},
setValue:function(b,a){this.getInputElement().$.checked=b;!a&&this.fire("change",{value:b});return this},getValue:function(){return this.getInputElement().$.checked},accessKeyUp:function(){this.setValue(!this.getValue())},eventProcessors:{onChange:function(b,a){if(!CKEDITOR.env.ie||8<CKEDITOR.env.version)return v.onChange.apply(this,arguments);b.on("load",function(){var a=this._.checkbox.getElement();a.on("propertychange",function(b){b=b.data.$;"checked"==b.propertyName&&this.fire("change",{value:a.$.checked})},
this)},this);this.on("change",a);return null}},keyboardFocusable:!0},q,!0);CKEDITOR.ui.dialog.radio.prototype=CKEDITOR.tools.extend(new CKEDITOR.ui.dialog.uiElement,{setValue:function(b,a){for(var d=this._.children,f,c=0;c<d.length&&(f=d[c]);c++)f.getElement().$.checked=f.getValue()==b;!a&&this.fire("change",{value:b});return this},getValue:function(){for(var b=this._.children,a=0;a<b.length;a++)if(b[a].getElement().$.checked)return b[a].getValue();return null},accessKeyUp:function(){var b=this._.children,
a;for(a=0;a<b.length;a++)if(b[a].getElement().$.checked){b[a].getElement().focus();return}b[0].getElement().focus()},eventProcessors:{onChange:function(b,a){if(!CKEDITOR.env.ie||8<CKEDITOR.env.version)return v.onChange.apply(this,arguments);b.on("load",function(){for(var a=this._.children,b=this,c=0;c<a.length;c++)a[c].getElement().on("propertychange",function(a){a=a.data.$;"checked"==a.propertyName&&this.$.checked&&b.fire("change",{value:this.getAttribute("value")})})},this);this.on("change",a);
return null}}},q,!0);CKEDITOR.ui.dialog.file.prototype=CKEDITOR.tools.extend(new CKEDITOR.ui.dialog.labeledElement,q,{getInputElement:function(){var b=CKEDITOR.document.getById(this._.frameId).getFrameDocument();return 0<b.$.forms.length?new CKEDITOR.dom.element(b.$.forms[0].elements[0]):this.getElement()},submit:function(){this.getInputElement().getParent().$.submit();return this},getAction:function(){return this.getInputElement().getParent().$.action},registerEvents:function(b){var a=/^on([A-Z]\w+)/,
d,f=function(a,b,c,d){a.on("formLoaded",function(){a.getInputElement().on(c,d,a)})},c;for(c in b)if(d=c.match(a))this.eventProcessors[c]?this.eventProcessors[c].call(this,this._.dialog,b[c]):f(this,this._.dialog,d[1].toLowerCase(),b[c]);return this},reset:function(){function b(){d.$.open();var b="";f.size&&(b=f.size-(CKEDITOR.env.ie?7:0));var h=a.frameId+"_input";d.$.write(['\x3chtml dir\x3d"'+g+'" lang\x3d"'+k+'"\x3e\x3chead\x3e\x3ctitle\x3e\x3c/title\x3e\x3c/head\x3e\x3cbody style\x3d"margin: 0; overflow: hidden; background: transparent;"\x3e',
'\x3cform enctype\x3d"multipart/form-data" method\x3d"POST" dir\x3d"'+g+'" lang\x3d"'+k+'" action\x3d"',CKEDITOR.tools.htmlEncode(f.action),'"\x3e\x3clabel id\x3d"',a.labelId,'" for\x3d"',h,'" style\x3d"display:none"\x3e',CKEDITOR.tools.htmlEncode(f.label),'\x3c/label\x3e\x3cinput style\x3d"width:100%" id\x3d"',h,'" aria-labelledby\x3d"',a.labelId,'" type\x3d"file" name\x3d"',CKEDITOR.tools.htmlEncode(f.id||"cke_upload"),'" size\x3d"',CKEDITOR.tools.htmlEncode(0<b?b:""),'" /\x3e\x3c/form\x3e\x3c/body\x3e\x3c/html\x3e\x3cscript\x3e',
CKEDITOR.env.ie?"("+CKEDITOR.tools.fixDomain+")();":"","window.parent.CKEDITOR.tools.callFunction("+e+");","window.onbeforeunload \x3d function() {window.parent.CKEDITOR.tools.callFunction("+m+")}","\x3c/script\x3e"].join(""));d.$.close();for(b=0;b<c.length;b++)c[b].enable()}var a=this._,d=CKEDITOR.document.getById(a.frameId).getFrameDocument(),f=a.definition,c=a.buttons,e=this.formLoadedNumber,m=this.formUnloadNumber,g=a.dialog._.editor.lang.dir,k=a.dialog._.editor.langCode;e||(e=this.formLoadedNumber=
CKEDITOR.tools.addFunction(function(){this.fire("formLoaded")},this),m=this.formUnloadNumber=CKEDITOR.tools.addFunction(function(){this.getInputElement().clearCustomData()},this),this.getDialog()._.editor.on("destroy",function(){CKEDITOR.tools.removeFunction(e);CKEDITOR.tools.removeFunction(m)}));CKEDITOR.env.gecko?setTimeout(b,500):b()},getValue:function(){return this.getInputElement().$.value||""},setInitValue:function(){this._.initValue=""},eventProcessors:{onChange:function(b,a){this._.domOnChangeRegistered||
(this.on("formLoaded",function(){this.getInputElement().on("change",function(){this.fire("change",{value:this.getValue()})},this)},this),this._.domOnChangeRegistered=!0);this.on("change",a)}},keyboardFocusable:!0},!0);CKEDITOR.ui.dialog.fileButton.prototype=new CKEDITOR.ui.dialog.button;CKEDITOR.ui.dialog.fieldset.prototype=CKEDITOR.tools.clone(CKEDITOR.ui.dialog.hbox.prototype);CKEDITOR.dialog.addUIElement("text",r);CKEDITOR.dialog.addUIElement("password",r);CKEDITOR.dialog.addUIElement("tel",r);
CKEDITOR.dialog.addUIElement("textarea",n);CKEDITOR.dialog.addUIElement("checkbox",n);CKEDITOR.dialog.addUIElement("radio",n);CKEDITOR.dialog.addUIElement("button",n);CKEDITOR.dialog.addUIElement("select",n);CKEDITOR.dialog.addUIElement("file",n);CKEDITOR.dialog.addUIElement("fileButton",n);CKEDITOR.dialog.addUIElement("html",n);CKEDITOR.dialog.addUIElement("fieldset",{build:function(b,a,d){for(var f=a.children,c,e=[],h=[],g=0;g<f.length&&(c=f[g]);g++){var k=[];e.push(k);h.push(CKEDITOR.dialog._.uiElementBuilders[c.type].build(b,
c,k))}return new CKEDITOR.ui.dialog[a.type](b,h,e,d,a)}})}});CKEDITOR.DIALOG_RESIZE_NONE=0;CKEDITOR.DIALOG_RESIZE_WIDTH=1;CKEDITOR.DIALOG_RESIZE_HEIGHT=2;CKEDITOR.DIALOG_RESIZE_BOTH=3;CKEDITOR.DIALOG_STATE_IDLE=1;CKEDITOR.DIALOG_STATE_BUSY=2;
(function(){function I(a){a._.tabBarMode=!0;a._.tabs[a._.currentTabId][0].focus();a._.currentFocusIndex=-1}function J(){for(var a=this._.tabIdList.length,b=CKEDITOR.tools.indexOf(this._.tabIdList,this._.currentTabId)+a,c=b-1;c>b-a;c--)if(this._.tabs[this._.tabIdList[c%a]][0].$.offsetHeight)return this._.tabIdList[c%a];return null}function W(){for(var a=this._.tabIdList.length,b=CKEDITOR.tools.indexOf(this._.tabIdList,this._.currentTabId),c=b+1;c<b+a;c++)if(this._.tabs[this._.tabIdList[c%a]][0].$.offsetHeight)return this._.tabIdList[c%
a];return null}function K(a,b){for(var c=a.$.getElementsByTagName("input"),e=0,d=c.length;e<d;e++){var f=new CKEDITOR.dom.element(c[e]);"text"==f.getAttribute("type").toLowerCase()&&(b?(f.setAttribute("value",f.getCustomData("fake_value")||""),f.removeCustomData("fake_value")):(f.setCustomData("fake_value",f.getAttribute("value")),f.setAttribute("value","")))}}function X(a,b){var c=this.getInputElement();c&&(a?c.removeAttribute("aria-invalid"):c.setAttribute("aria-invalid",!0));a||(this.select?this.select():
this.focus());b&&alert(b);this.fire("validated",{valid:a,msg:b})}function Y(){var a=this.getInputElement();a&&a.removeAttribute("aria-invalid")}function Z(a){var b=CKEDITOR.dom.element.createFromHtml(CKEDITOR.addTemplate("dialog",aa).output({id:CKEDITOR.tools.getNextNumber(),editorId:a.id,langDir:a.lang.dir,langCode:a.langCode,editorDialogClass:"cke_editor_"+a.name.replace(/\./g,"\\.")+"_dialog",closeTitle:a.lang.common.close,hidpi:CKEDITOR.env.hidpi?"cke_hidpi":""})),c=b.getChild([0,0,0,0,0]),e=
c.getChild(0),d=c.getChild(1);a.plugins.clipboard&&CKEDITOR.plugins.clipboard.preventDefaultDropOnElement(c);!CKEDITOR.env.ie||CKEDITOR.env.quirks||CKEDITOR.env.edge||(a="javascript:void(function(){"+encodeURIComponent("document.open();("+CKEDITOR.tools.fixDomain+")();document.close();")+"}())",CKEDITOR.dom.element.createFromHtml('\x3ciframe frameBorder\x3d"0" class\x3d"cke_iframe_shim" src\x3d"'+a+'" tabIndex\x3d"-1"\x3e\x3c/iframe\x3e').appendTo(c.getParent()));e.unselectable();d.unselectable();
return{element:b,parts:{dialog:b.getChild(0),title:e,close:d,tabs:c.getChild(2),contents:c.getChild([3,0,0,0]),footer:c.getChild([3,0,1,0])}}}function L(a,b,c){this.element=b;this.focusIndex=c;this.tabIndex=0;this.isFocusable=function(){return!b.getAttribute("disabled")&&b.isVisible()};this.focus=function(){a._.currentFocusIndex=this.focusIndex;this.element.focus()};b.on("keydown",function(a){a.data.getKeystroke()in{32:1,13:1}&&this.fire("click")});b.on("focus",function(){this.fire("mouseover")});
b.on("blur",function(){this.fire("mouseout")})}function ba(a){function b(){a.layout()}var c=CKEDITOR.document.getWindow();c.on("resize",b);a.on("hide",function(){c.removeListener("resize",b)})}function M(a,b){this.dialog=a;for(var c=b.contents,e=0,d;d=c[e];e++)c[e]=d&&new N(a,d);CKEDITOR.tools.extend(this,b)}function N(a,b){this._={dialog:a};CKEDITOR.tools.extend(this,b)}function ca(a){function b(b){var c=a.getSize(),h=a.parts.dialog.getParent().getClientSize(),q=b.data.$.screenX,m=b.data.$.screenY,
r=q-e.x,n=m-e.y;e={x:q,y:m};d.x+=r;d.y+=n;q=d.x+k[3]<g?-k[3]:d.x-k[1]>h.width-c.width-g?h.width-c.width+("rtl"==f.lang.dir?0:k[1]):d.x;c=d.y+k[0]<g?-k[0]:d.y-k[2]>h.height-c.height-g?h.height-c.height+k[2]:d.y;q=Math.floor(q);c=Math.floor(c);a.move(q,c,1);b.data.preventDefault()}function c(){CKEDITOR.document.removeListener("mousemove",b);CKEDITOR.document.removeListener("mouseup",c);if(CKEDITOR.env.ie6Compat){var a=u.getChild(0).getFrameDocument();a.removeListener("mousemove",b);a.removeListener("mouseup",
c)}}var e=null,d=null,f=a.getParentEditor(),g=f.config.dialog_magnetDistance,k=CKEDITOR.skin.margins||[0,0,0,0];"undefined"==typeof g&&(g=20);a.parts.title.on("mousedown",function(f){if(!a._.moved){var g=a._.element;g.getFirst().setStyle("position","absolute");g.removeStyle("display");a._.moved=!0;a.layout()}e={x:f.data.$.screenX,y:f.data.$.screenY};CKEDITOR.document.on("mousemove",b);CKEDITOR.document.on("mouseup",c);d=a.getPosition();CKEDITOR.env.ie6Compat&&(g=u.getChild(0).getFrameDocument(),g.on("mousemove",
b),g.on("mouseup",c));f.data.preventDefault()},a)}function da(a){function b(b){var c="rtl"==f.lang.dir,m=h.width,q=h.height,w=m+(b.data.$.screenX-l.x)*(c?-1:1)*(a._.moved?1:2),A=q+(b.data.$.screenY-l.y)*(a._.moved?1:2),C=a._.element.getFirst(),C=c&&parseInt(C.getComputedStyle("right"),10),v=a.getPosition();v.x=v.x||0;v.y=v.y||0;v.y+A>p.height&&(A=p.height-v.y);(c?C:v.x)+w>p.width&&(w=p.width-(c?C:v.x));A=Math.floor(A);w=Math.floor(w);if(d==CKEDITOR.DIALOG_RESIZE_WIDTH||d==CKEDITOR.DIALOG_RESIZE_BOTH)m=
Math.max(e.minWidth||0,w-g);if(d==CKEDITOR.DIALOG_RESIZE_HEIGHT||d==CKEDITOR.DIALOG_RESIZE_BOTH)q=Math.max(e.minHeight||0,A-k);a.resize(m,q);a._.moved&&O(a,a._.position.x,a._.position.y);a._.moved||a.layout();b.data.preventDefault()}function c(){CKEDITOR.document.removeListener("mouseup",c);CKEDITOR.document.removeListener("mousemove",b);q&&(q.remove(),q=null);if(CKEDITOR.env.ie6Compat){var a=u.getChild(0).getFrameDocument();a.removeListener("mouseup",c);a.removeListener("mousemove",b)}}var e=a.definition,
d=e.resizable;if(d!=CKEDITOR.DIALOG_RESIZE_NONE){var f=a.getParentEditor(),g,k,p,l,h,q,m=CKEDITOR.tools.addFunction(function(d){function e(a){return a.isVisible()}h=a.getSize();var f=a.parts.contents,m=f.$.getElementsByTagName("iframe").length,w=!(CKEDITOR.env.gecko||CKEDITOR.env.ie&&CKEDITOR.env.quirks);m&&(q=CKEDITOR.dom.element.createFromHtml('\x3cdiv class\x3d"cke_dialog_resize_cover" style\x3d"height: 100%; position: absolute; width: 100%; left:0; top:0;"\x3e\x3c/div\x3e'),f.append(q));k=h.height-
a.parts.contents.getFirst(e).getSize("height",w);g=h.width-a.parts.contents.getFirst(e).getSize("width",1);l={x:d.screenX,y:d.screenY};p=CKEDITOR.document.getWindow().getViewPaneSize();CKEDITOR.document.on("mousemove",b);CKEDITOR.document.on("mouseup",c);CKEDITOR.env.ie6Compat&&(f=u.getChild(0).getFrameDocument(),f.on("mousemove",b),f.on("mouseup",c));d.preventDefault&&d.preventDefault()});a.on("load",function(){var b="";d==CKEDITOR.DIALOG_RESIZE_WIDTH?b=" cke_resizer_horizontal":d==CKEDITOR.DIALOG_RESIZE_HEIGHT&&
(b=" cke_resizer_vertical");b=CKEDITOR.dom.element.createFromHtml('\x3cdiv class\x3d"cke_resizer'+b+" cke_resizer_"+f.lang.dir+'" title\x3d"'+CKEDITOR.tools.htmlEncode(f.lang.common.resize)+'" onmousedown\x3d"CKEDITOR.tools.callFunction('+m+', event )"\x3e'+("ltr"==f.lang.dir?"◢":"◣")+"\x3c/div\x3e");a.parts.footer.append(b,1)});f.on("destroy",function(){CKEDITOR.tools.removeFunction(m)})}}function O(a,b,c){var e=a.parts.dialog.getParent().getClientSize(),d=a.getSize(),f=a._.viewportRatio,g=Math.max(e.width-
d.width,0),e=Math.max(e.height-d.height,0);f.width=g?b/g:f.width;f.height=e?c/e:f.height;a._.viewportRatio=f}function H(a){a.data.preventDefault(1)}function P(a){var b=a.config,c=CKEDITOR.skinName||a.config.skin,e=b.dialog_backgroundCoverColor||("moono-lisa"==c?"black":"white"),c=b.dialog_backgroundCoverOpacity,d=b.baseFloatZIndex,b=CKEDITOR.tools.genKey(e,c,d),f=B[b];CKEDITOR.document.getBody().addClass("cke_dialog_open");f?f.show():(d=['\x3cdiv tabIndex\x3d"-1" style\x3d"position: ',CKEDITOR.env.ie6Compat?
"absolute":"fixed","; z-index: ",d,"; top: 0px; left: 0px; ","; width: 100%; height: 100%;",CKEDITOR.env.ie6Compat?"":"background-color: "+e,'" class\x3d"cke_dialog_background_cover"\x3e'],CKEDITOR.env.ie6Compat&&(e="\x3chtml\x3e\x3cbody style\x3d\\'background-color:"+e+";\\'\x3e\x3c/body\x3e\x3c/html\x3e",d.push('\x3ciframe hidefocus\x3d"true" frameborder\x3d"0" id\x3d"cke_dialog_background_iframe" src\x3d"javascript:'),d.push("void((function(){"+encodeURIComponent("document.open();("+CKEDITOR.tools.fixDomain+
")();document.write( '"+e+"' );document.close();")+"})())"),d.push('" style\x3d"position:absolute;left:0;top:0;width:100%;height: 100%;filter: progid:DXImageTransform.Microsoft.Alpha(opacity\x3d0)"\x3e\x3c/iframe\x3e')),d.push("\x3c/div\x3e"),f=CKEDITOR.dom.element.createFromHtml(d.join("")),f.setOpacity(void 0!==c?c:.5),f.on("keydown",H),f.on("keypress",H),f.on("keyup",H),f.appendTo(CKEDITOR.document.getBody()),B[b]=f);a.focusManager.add(f);u=f;CKEDITOR.env.mac&&CKEDITOR.env.webkit||f.focus()}function Q(a){CKEDITOR.document.getBody().removeClass("cke_dialog_open");
u&&(a.focusManager.remove(u),u.hide())}function R(a){var b=a.data.$.ctrlKey||a.data.$.metaKey,c=a.data.$.altKey,e=a.data.$.shiftKey,d=String.fromCharCode(a.data.$.keyCode);(b=x[(b?"CTRL+":"")+(c?"ALT+":"")+(e?"SHIFT+":"")+d])&&b.length&&(b=b[b.length-1],b.keydown&&b.keydown.call(b.uiElement,b.dialog,b.key),a.data.preventDefault())}function S(a){var b=a.data.$.ctrlKey||a.data.$.metaKey,c=a.data.$.altKey,e=a.data.$.shiftKey,d=String.fromCharCode(a.data.$.keyCode);(b=x[(b?"CTRL+":"")+(c?"ALT+":"")+(e?
"SHIFT+":"")+d])&&b.length&&(b=b[b.length-1],b.keyup&&(b.keyup.call(b.uiElement,b.dialog,b.key),a.data.preventDefault()))}function T(a,b,c,e,d){(x[c]||(x[c]=[])).push({uiElement:a,dialog:b,key:c,keyup:d||a.accessKeyUp,keydown:e||a.accessKeyDown})}function ea(a){for(var b in x){for(var c=x[b],e=c.length-1;0<=e;e--)c[e].dialog!=a&&c[e].uiElement!=a||c.splice(e,1);0===c.length&&delete x[b]}}function fa(a,b){a._.accessKeyMap[b]&&a.selectPage(a._.accessKeyMap[b])}function ga(){}var y=CKEDITOR.tools.cssLength,
U,u,V=!1,D=!CKEDITOR.env.ie||CKEDITOR.env.edge,aa='\x3cdiv class\x3d"cke_reset_all cke_dialog_container {editorId} {editorDialogClass} {hidpi}" dir\x3d"{langDir}" style\x3d"'+(D?"display:flex":"")+'" lang\x3d"{langCode}" role\x3d"dialog" aria-labelledby\x3d"cke_dialog_title_{id}"\x3e\x3ctable class\x3d"cke_dialog '+CKEDITOR.env.cssClass+' cke_{langDir}" style\x3d"'+(D?"margin:auto":"position:absolute")+'" role\x3d"presentation"\x3e\x3ctr\x3e\x3ctd role\x3d"presentation"\x3e\x3cdiv class\x3d"cke_dialog_body" role\x3d"presentation"\x3e\x3cdiv id\x3d"cke_dialog_title_{id}" class\x3d"cke_dialog_title" role\x3d"presentation"\x3e\x3c/div\x3e\x3ca id\x3d"cke_dialog_close_button_{id}" class\x3d"cke_dialog_close_button" href\x3d"javascript:void(0)" title\x3d"{closeTitle}" role\x3d"button"\x3e\x3cspan class\x3d"cke_label"\x3eX\x3c/span\x3e\x3c/a\x3e\x3cdiv id\x3d"cke_dialog_tabs_{id}" class\x3d"cke_dialog_tabs" role\x3d"tablist"\x3e\x3c/div\x3e\x3ctable class\x3d"cke_dialog_contents" role\x3d"presentation"\x3e\x3ctr\x3e\x3ctd id\x3d"cke_dialog_contents_{id}" class\x3d"cke_dialog_contents_body" role\x3d"presentation"\x3e\x3c/td\x3e\x3c/tr\x3e\x3ctr\x3e\x3ctd id\x3d"cke_dialog_footer_{id}" class\x3d"cke_dialog_footer" role\x3d"presentation"\x3e\x3c/td\x3e\x3c/tr\x3e\x3c/table\x3e\x3c/div\x3e\x3c/td\x3e\x3c/tr\x3e\x3c/table\x3e\x3c/div\x3e';
CKEDITOR.dialog=function(a,b){function c(){var a=n._.focusList;a.sort(function(a,b){return a.tabIndex!=b.tabIndex?b.tabIndex-a.tabIndex:a.focusIndex-b.focusIndex});for(var b=a.length,c=0;c<b;c++)a[c].focusIndex=c}function e(a){var b=n._.focusList;a=a||0;if(!(1>b.length)){var c=n._.currentFocusIndex;n._.tabBarMode&&0>a&&(c=0);try{b[c].getInputElement().$.blur()}catch(d){}var e=c,f=1<n._.pageCount;do{e+=a;if(f&&!n._.tabBarMode&&(e==b.length||-1==e)){n._.tabBarMode=!0;n._.tabs[n._.currentTabId][0].focus();
n._.currentFocusIndex=-1;return}e=(e+b.length)%b.length;if(e==c)break}while(a&&!b[e].isFocusable());b[e].focus();"text"==b[e].type&&b[e].select()}}function d(b){if(n==CKEDITOR.dialog._.currentTop){var c=b.data.getKeystroke(),d="rtl"==a.lang.dir,g=[37,38,39,40];q=m=0;if(9==c||c==CKEDITOR.SHIFT+9)e(c==CKEDITOR.SHIFT+9?-1:1),q=1;else if(c==CKEDITOR.ALT+121&&!n._.tabBarMode&&1<n.getPageCount())I(n),q=1;else if(-1!=CKEDITOR.tools.indexOf(g,c)&&n._.tabBarMode)c=-1!=CKEDITOR.tools.indexOf([d?39:37,38],c)?
J.call(n):W.call(n),n.selectPage(c),n._.tabs[c][0].focus(),q=1;else if(13!=c&&32!=c||!n._.tabBarMode)if(13==c)c=b.data.getTarget(),c.is("a","button","select","textarea")||c.is("input")&&"button"==c.$.type||((c=this.getButton("ok"))&&CKEDITOR.tools.setTimeout(c.click,0,c),q=1),m=1;else if(27==c)(c=this.getButton("cancel"))?CKEDITOR.tools.setTimeout(c.click,0,c):!1!==this.fire("cancel",{hide:!0}).hide&&this.hide(),m=1;else return;else this.selectPage(this._.currentTabId),this._.tabBarMode=!1,this._.currentFocusIndex=
-1,e(1),q=1;f(b)}}function f(a){q?a.data.preventDefault(1):m&&a.data.stopPropagation()}var g=CKEDITOR.dialog._.dialogDefinitions[b],k=CKEDITOR.tools.clone(U),p=a.config.dialog_buttonsOrder||"OS",l=a.lang.dir,h={},q,m;("OS"==p&&CKEDITOR.env.mac||"rtl"==p&&"ltr"==l||"ltr"==p&&"rtl"==l)&&k.buttons.reverse();g=CKEDITOR.tools.extend(g(a),k);g=CKEDITOR.tools.clone(g);g=new M(this,g);k=Z(a);this._={editor:a,element:k.element,name:b,model:null,contentSize:{width:0,height:0},size:{width:0,height:0},contents:{},
buttons:{},accessKeyMap:{},viewportRatio:{width:.5,height:.5},tabs:{},tabIdList:[],currentTabId:null,currentTabIndex:null,pageCount:0,lastTab:null,tabBarMode:!1,focusList:[],currentFocusIndex:0,hasFocus:!1};this.parts=k.parts;CKEDITOR.tools.setTimeout(function(){a.fire("ariaWidget",this.parts.contents)},0,this);k={top:0,visibility:"hidden"};CKEDITOR.env.ie6Compat&&(k.position="absolute");k["rtl"==l?"right":"left"]=0;this.parts.dialog.setStyles(k);CKEDITOR.event.call(this);this.definition=g=CKEDITOR.fire("dialogDefinition",
{name:b,definition:g,dialog:this},a).definition;if(!("removeDialogTabs"in a._)&&a.config.removeDialogTabs){k=a.config.removeDialogTabs.split(";");for(l=0;l<k.length;l++)if(p=k[l].split(":"),2==p.length){var r=p[0];h[r]||(h[r]=[]);h[r].push(p[1])}a._.removeDialogTabs=h}if(a._.removeDialogTabs&&(h=a._.removeDialogTabs[b]))for(l=0;l<h.length;l++)g.removeContents(h[l]);if(g.onLoad)this.on("load",g.onLoad);if(g.onShow)this.on("show",g.onShow);if(g.onHide)this.on("hide",g.onHide);if(g.onOk)this.on("ok",
function(b){a.fire("saveSnapshot");setTimeout(function(){a.fire("saveSnapshot")},0);!1===g.onOk.call(this,b)&&(b.data.hide=!1)});this.state=CKEDITOR.DIALOG_STATE_IDLE;if(g.onCancel)this.on("cancel",function(a){!1===g.onCancel.call(this,a)&&(a.data.hide=!1)});var n=this,t=function(a){var b=n._.contents,c=!1,d;for(d in b)for(var e in b[d])if(c=a.call(this,b[d][e]))return};this.on("ok",function(a){t(function(b){if(b.validate){var c=b.validate(this),d="string"==typeof c||!1===c;d&&(a.data.hide=!1,a.stop());
X.call(b,!d,"string"==typeof c?c:void 0);return d}})},this,null,0);this.on("cancel",function(b){t(function(c){if(c.isChanged())return a.config.dialog_noConfirmCancel||confirm(a.lang.common.confirmCancel)||(b.data.hide=!1),!0})},this,null,0);this.parts.close.on("click",function(a){!1!==this.fire("cancel",{hide:!0}).hide&&this.hide();a.data.preventDefault()},this);this.changeFocus=e;var z=this._.element;a.focusManager.add(z,1);this.on("show",function(){z.on("keydown",d,this);if(CKEDITOR.env.gecko)z.on("keypress",
f,this)});this.on("hide",function(){z.removeListener("keydown",d);CKEDITOR.env.gecko&&z.removeListener("keypress",f);t(function(a){Y.apply(a)})});this.on("iframeAdded",function(a){(new CKEDITOR.dom.document(a.data.iframe.$.contentWindow.document)).on("keydown",d,this,null,0)});this.on("show",function(){c();var b=1<n._.pageCount;a.config.dialog_startupFocusTab&&b?(n._.tabBarMode=!0,n._.tabs[n._.currentTabId][0].focus(),n._.currentFocusIndex=-1):this._.hasFocus||(this._.currentFocusIndex=b?-1:this._.focusList.length-
1,g.onFocus?(b=g.onFocus.call(this))&&b.focus():e(1))},this,null,4294967295);if(CKEDITOR.env.ie6Compat)this.on("load",function(){var a=this.getElement(),b=a.getFirst();b.remove();b.appendTo(a)},this);ca(this);da(this);(new CKEDITOR.dom.text(g.title,CKEDITOR.document)).appendTo(this.parts.title);for(l=0;l<g.contents.length;l++)(h=g.contents[l])&&this.addPage(h);this.parts.tabs.on("click",function(a){var b=a.data.getTarget();b.hasClass("cke_dialog_tab")&&(b=b.$.id,this.selectPage(b.substring(4,b.lastIndexOf("_"))),
I(this),a.data.preventDefault())},this);l=[];h=CKEDITOR.dialog._.uiElementBuilders.hbox.build(this,{type:"hbox",className:"cke_dialog_footer_buttons",widths:[],children:g.buttons},l).getChild();this.parts.footer.setHtml(l.join(""));for(l=0;l<h.length;l++)this._.buttons[h[l].id]=h[l]};CKEDITOR.dialog.prototype={destroy:function(){this.hide();this._.element.remove()},resize:function(a,b){if(!this._.contentSize||this._.contentSize.width!=a||this._.contentSize.height!=b){CKEDITOR.dialog.fire("resize",
{dialog:this,width:a,height:b},this._.editor);this.fire("resize",{width:a,height:b},this._.editor);this.parts.contents.setStyles({width:a+"px",height:b+"px"});if("rtl"==this._.editor.lang.dir&&this._.position){var c=this.parts.dialog.getParent().getClientSize().width;this._.position.x=c-this._.contentSize.width-parseInt(this._.element.getFirst().getStyle("right"),10)}this._.contentSize={width:a,height:b}}},getSize:function(){var a=this._.element.getFirst();return{width:a.$.offsetWidth||0,height:a.$.offsetHeight||
0}},move:function(a,b,c){var e=this._.element.getFirst(),d="rtl"==this._.editor.lang.dir;CKEDITOR.env.ie&&e.setStyle("zoom","100%");var f=this.parts.dialog.getParent().getClientSize(),g=this.getSize(),k=this._.viewportRatio,p=Math.max(f.width-g.width,0),f=Math.max(f.height-g.height,0);this._.position&&this._.position.x==a&&this._.position.y==b?(a=Math.floor(p*k.width),b=Math.floor(f*k.height)):O(this,a,b);this._.position={x:a,y:b};d&&(a=p-a);b={top:(0<b?b:0)+"px"};b[d?"right":"left"]=(0<a?a:0)+"px";
e.setStyles(b);c&&(this._.moved=1)},getPosition:function(){return CKEDITOR.tools.extend({},this._.position)},show:function(){var a=this._.element,b=this.definition,c=CKEDITOR.document.getBody(),e=this._.editor.config.baseFloatZIndex;a.getParent()&&a.getParent().equals(c)?a.setStyle("display",D?"flex":"block"):a.appendTo(c);this.resize(this._.contentSize&&this._.contentSize.width||b.width||b.minWidth,this._.contentSize&&this._.contentSize.height||b.height||b.minHeight);this.reset();null===this._.currentTabId&&
this.selectPage(this.definition.contents[0].id);null===CKEDITOR.dialog._.currentZIndex&&(CKEDITOR.dialog._.currentZIndex=e);this._.element.getFirst().setStyle("z-index",CKEDITOR.dialog._.currentZIndex+=10);this.getElement().setStyle("z-index",CKEDITOR.dialog._.currentZIndex);null===CKEDITOR.dialog._.currentTop?(CKEDITOR.dialog._.currentTop=this,this._.parentDialog=null,P(this._.editor)):CKEDITOR.dialog._.currentTop!==this&&(this._.parentDialog=CKEDITOR.dialog._.currentTop,c=this._.parentDialog.getElement().getFirst(),
c.$.style.zIndex-=Math.floor(e/2),this._.parentDialog.getElement().setStyle("z-index",c.$.style.zIndex),CKEDITOR.dialog._.currentTop=this);a.on("keydown",R);a.on("keyup",S);this._.hasFocus=!1;for(var d in b.contents)if(b.contents[d]){var a=b.contents[d],e=this._.tabs[a.id],c=a.requiredContent,f=0;if(e){for(var g in this._.contents[a.id]){var k=this._.contents[a.id][g];"hbox"!=k.type&&"vbox"!=k.type&&k.getInputElement()&&(k.requiredContent&&!this._.editor.activeFilter.check(k.requiredContent)?k.disable():
(k.enable(),f++))}!f||c&&!this._.editor.activeFilter.check(c)?e[0].addClass("cke_dialog_tab_disabled"):e[0].removeClass("cke_dialog_tab_disabled")}}CKEDITOR.tools.setTimeout(function(){this.layout();ba(this);this.parts.dialog.setStyle("visibility","");this.fireOnce("load",{});CKEDITOR.ui.fire("ready",this);this.fire("show",{});this._.editor.fire("dialogShow",this);this._.parentDialog||this._.editor.focusManager.lock();this.foreach(function(a){a.setInitValue&&a.setInitValue()})},100,this)},layout:function(){var a=
this.parts.dialog;if(this._.moved||!D){var b=this.getSize(),c=CKEDITOR.document.getWindow().getViewPaneSize(),e;this._.moved&&this._.position?(e=this._.position.x,b=this._.position.y):(e=(c.width-b.width)/2,b=(c.height-b.height)/2);CKEDITOR.env.ie6Compat||(a.setStyle("position","absolute"),a.removeStyle("margin"));e=Math.floor(e);b=Math.floor(b);this.move(e,b)}},foreach:function(a){for(var b in this._.contents)for(var c in this._.contents[b])a.call(this,this._.contents[b][c]);return this},reset:function(){var a=
function(a){a.reset&&a.reset(1)};return function(){this.foreach(a);return this}}(),setupContent:function(){var a=arguments;this.foreach(function(b){b.setup&&b.setup.apply(b,a)})},commitContent:function(){var a=arguments;this.foreach(function(b){CKEDITOR.env.ie&&this._.currentFocusIndex==b.focusIndex&&b.getInputElement().$.blur();b.commit&&b.commit.apply(b,a)})},hide:function(){if(this.parts.dialog.isVisible()){this.fire("hide",{});this._.editor.fire("dialogHide",this);this.selectPage(this._.tabIdList[0]);
var a=this._.element;a.setStyle("display","none");this.parts.dialog.setStyle("visibility","hidden");for(ea(this);CKEDITOR.dialog._.currentTop!=this;)CKEDITOR.dialog._.currentTop.hide();if(this._.parentDialog){var b=this._.parentDialog.getElement().getFirst(),c=parseInt(b.$.style.zIndex,10)+Math.floor(this._.editor.config.baseFloatZIndex/2);this._.parentDialog.getElement().setStyle("z-index",c);b.setStyle("z-index",c)}else Q(this._.editor);if(CKEDITOR.dialog._.currentTop=this._.parentDialog)CKEDITOR.dialog._.currentZIndex-=
10;else{CKEDITOR.dialog._.currentZIndex=null;a.removeListener("keydown",R);a.removeListener("keyup",S);var e=this._.editor;e.focus();setTimeout(function(){e.focusManager.unlock();CKEDITOR.env.iOS&&e.window.focus()},0)}delete this._.parentDialog;this.foreach(function(a){a.resetInitValue&&a.resetInitValue()});this.setState(CKEDITOR.DIALOG_STATE_IDLE)}},addPage:function(a){if(!a.requiredContent||this._.editor.filter.check(a.requiredContent)){for(var b=[],c=a.label?' title\x3d"'+CKEDITOR.tools.htmlEncode(a.label)+
'"':"",e=CKEDITOR.dialog._.uiElementBuilders.vbox.build(this,{type:"vbox",className:"cke_dialog_page_contents",children:a.elements,expand:!!a.expand,padding:a.padding,style:a.style||"width: 100%;"},b),d=this._.contents[a.id]={},f=e.getChild(),g=0;e=f.shift();)e.notAllowed||"hbox"==e.type||"vbox"==e.type||g++,d[e.id]=e,"function"==typeof e.getChild&&f.push.apply(f,e.getChild());g||(a.hidden=!0);b=CKEDITOR.dom.element.createFromHtml(b.join(""));b.setAttribute("role","tabpanel");b.setStyle("min-height",
"100%");e=CKEDITOR.env;d="cke_"+a.id+"_"+CKEDITOR.tools.getNextNumber();c=CKEDITOR.dom.element.createFromHtml(['\x3ca class\x3d"cke_dialog_tab"',0<this._.pageCount?" cke_last":"cke_first",c,a.hidden?' style\x3d"display:none"':"",' id\x3d"',d,'"',e.gecko&&!e.hc?"":' href\x3d"javascript:void(0)"',' tabIndex\x3d"-1" hidefocus\x3d"true" role\x3d"tab"\x3e',a.label,"\x3c/a\x3e"].join(""));b.setAttribute("aria-labelledby",d);this._.tabs[a.id]=[c,b];this._.tabIdList.push(a.id);!a.hidden&&this._.pageCount++;
this._.lastTab=c;this.updateStyle();b.setAttribute("name",a.id);b.appendTo(this.parts.contents);c.unselectable();this.parts.tabs.append(c);a.accessKey&&(T(this,this,"CTRL+"+a.accessKey,ga,fa),this._.accessKeyMap["CTRL+"+a.accessKey]=a.id)}},selectPage:function(a){if(this._.currentTabId!=a&&!this._.tabs[a][0].hasClass("cke_dialog_tab_disabled")&&!1!==this.fire("selectPage",{page:a,currentPage:this._.currentTabId})){for(var b in this._.tabs){var c=this._.tabs[b][0],e=this._.tabs[b][1];b!=a&&(c.removeClass("cke_dialog_tab_selected"),
c.removeAttribute("aria-selected"),e.hide());e.setAttribute("aria-hidden",b!=a)}var d=this._.tabs[a];d[0].addClass("cke_dialog_tab_selected");d[0].setAttribute("aria-selected",!0);CKEDITOR.env.ie6Compat||CKEDITOR.env.ie7Compat?(K(d[1]),d[1].show(),setTimeout(function(){K(d[1],1)},0)):d[1].show();this._.currentTabId=a;this._.currentTabIndex=CKEDITOR.tools.indexOf(this._.tabIdList,a)}},updateStyle:function(){this.parts.dialog[(1===this._.pageCount?"add":"remove")+"Class"]("cke_single_page")},hidePage:function(a){var b=
this._.tabs[a]&&this._.tabs[a][0];b&&1!=this._.pageCount&&b.isVisible()&&(a==this._.currentTabId&&this.selectPage(J.call(this)),b.hide(),this._.pageCount--,this.updateStyle())},showPage:function(a){if(a=this._.tabs[a]&&this._.tabs[a][0])a.show(),this._.pageCount++,this.updateStyle()},getElement:function(){return this._.element},getName:function(){return this._.name},getContentElement:function(a,b){var c=this._.contents[a];return c&&c[b]},getValueOf:function(a,b){return this.getContentElement(a,b).getValue()},
setValueOf:function(a,b,c){return this.getContentElement(a,b).setValue(c)},getButton:function(a){return this._.buttons[a]},click:function(a){return this._.buttons[a].click()},disableButton:function(a){return this._.buttons[a].disable()},enableButton:function(a){return this._.buttons[a].enable()},getPageCount:function(){return this._.pageCount},getParentEditor:function(){return this._.editor},getSelectedElement:function(){return this.getParentEditor().getSelection().getSelectedElement()},addFocusable:function(a,
b){if("undefined"==typeof b)b=this._.focusList.length,this._.focusList.push(new L(this,a,b));else{this._.focusList.splice(b,0,new L(this,a,b));for(var c=b+1;c<this._.focusList.length;c++)this._.focusList[c].focusIndex++}},setState:function(a){if(this.state!=a){this.state=a;if(a==CKEDITOR.DIALOG_STATE_BUSY){if(!this.parts.spinner){var b=this.getParentEditor().lang.dir,c={attributes:{"class":"cke_dialog_spinner"},styles:{"float":"rtl"==b?"right":"left"}};c.styles["margin-"+("rtl"==b?"left":"right")]=
"8px";this.parts.spinner=CKEDITOR.document.createElement("div",c);this.parts.spinner.setHtml("\x26#8987;");this.parts.spinner.appendTo(this.parts.title,1)}this.parts.spinner.show();this.getButton("ok")&&this.getButton("ok").disable()}else a==CKEDITOR.DIALOG_STATE_IDLE&&(this.parts.spinner&&this.parts.spinner.hide(),this.getButton("ok")&&this.getButton("ok").enable());this.fire("state",a)}},getModel:function(a){return this._.model?this._.model:this.definition.getModel?this.definition.getModel(a):null},
setModel:function(a){this._.model=a},getMode:function(a){if(this.definition.getMode)return this.definition.getMode(a);a=this.getModel(a);return!a||a instanceof CKEDITOR.dom.element&&!a.getParent()?CKEDITOR.dialog.CREATION_MODE:CKEDITOR.dialog.EDITING_MODE}};CKEDITOR.tools.extend(CKEDITOR.dialog,{CREATION_MODE:0,EDITING_MODE:1,add:function(a,b){this._.dialogDefinitions[a]&&"function"!=typeof b||(this._.dialogDefinitions[a]=b)},exists:function(a){return!!this._.dialogDefinitions[a]},getCurrent:function(){return CKEDITOR.dialog._.currentTop},
isTabEnabled:function(a,b,c){a=a.config.removeDialogTabs;return!(a&&a.match(new RegExp("(?:^|;)"+b+":"+c+"(?:$|;)","i")))},okButton:function(){var a=function(a,c){c=c||{};return CKEDITOR.tools.extend({id:"ok",type:"button",label:a.lang.common.ok,"class":"cke_dialog_ui_button_ok",onClick:function(a){a=a.data.dialog;!1!==a.fire("ok",{hide:!0}).hide&&a.hide()}},c,!0)};a.type="button";a.override=function(b){return CKEDITOR.tools.extend(function(c){return a(c,b)},{type:"button"},!0)};return a}(),cancelButton:function(){var a=
function(a,c){c=c||{};return CKEDITOR.tools.extend({id:"cancel",type:"button",label:a.lang.common.cancel,"class":"cke_dialog_ui_button_cancel",onClick:function(a){a=a.data.dialog;!1!==a.fire("cancel",{hide:!0}).hide&&a.hide()}},c,!0)};a.type="button";a.override=function(b){return CKEDITOR.tools.extend(function(c){return a(c,b)},{type:"button"},!0)};return a}(),addUIElement:function(a,b){this._.uiElementBuilders[a]=b}});CKEDITOR.dialog._={uiElementBuilders:{},dialogDefinitions:{},currentTop:null,currentZIndex:null};
CKEDITOR.event.implementOn(CKEDITOR.dialog);CKEDITOR.event.implementOn(CKEDITOR.dialog.prototype);U={resizable:CKEDITOR.DIALOG_RESIZE_BOTH,minWidth:600,minHeight:400,buttons:[CKEDITOR.dialog.okButton,CKEDITOR.dialog.cancelButton]};var E=function(a,b,c){for(var e=0,d;d=a[e];e++)if(d.id==b||c&&d[c]&&(d=E(d[c],b,c)))return d;return null},F=function(a,b,c,e,d){if(c){for(var f=0,g;g=a[f];f++){if(g.id==c)return a.splice(f,0,b),b;if(e&&g[e]&&(g=F(g[e],b,c,e,!0)))return g}if(d)return null}a.push(b);return b},
G=function(a,b,c){for(var e=0,d;d=a[e];e++){if(d.id==b)return a.splice(e,1);if(c&&d[c]&&(d=G(d[c],b,c)))return d}return null};M.prototype={getContents:function(a){return E(this.contents,a)},getButton:function(a){return E(this.buttons,a)},addContents:function(a,b){return F(this.contents,a,b)},addButton:function(a,b){return F(this.buttons,a,b)},removeContents:function(a){G(this.contents,a)},removeButton:function(a){G(this.buttons,a)}};N.prototype={get:function(a){return E(this.elements,a,"children")},
add:function(a,b){return F(this.elements,a,b,"children")},remove:function(a){G(this.elements,a,"children")}};var B={},x={};(function(){CKEDITOR.ui.dialog={uiElement:function(a,b,c,e,d,f,g){if(!(4>arguments.length)){var k=(e.call?e(b):e)||"div",p=["\x3c",k," "],l=(d&&d.call?d(b):d)||{},h=(f&&f.call?f(b):f)||{},q=(g&&g.call?g.call(this,a,b):g)||"",m=this.domId=h.id||CKEDITOR.tools.getNextId()+"_uiElement";b.requiredContent&&!a.getParentEditor().filter.check(b.requiredContent)&&(l.display="none",this.notAllowed=
!0);h.id=m;var r={};b.type&&(r["cke_dialog_ui_"+b.type]=1);b.className&&(r[b.className]=1);b.disabled&&(r.cke_disabled=1);for(var n=h["class"]&&h["class"].split?h["class"].split(" "):[],m=0;m<n.length;m++)n[m]&&(r[n[m]]=1);n=[];for(m in r)n.push(m);h["class"]=n.join(" ");b.title&&(h.title=b.title);r=(b.style||"").split(";");b.align&&(n=b.align,l["margin-left"]="left"==n?0:"auto",l["margin-right"]="right"==n?0:"auto");for(m in l)r.push(m+":"+l[m]);b.hidden&&r.push("display:none");for(m=r.length-1;0<=
m;m--)""===r[m]&&r.splice(m,1);0<r.length&&(h.style=(h.style?h.style+"; ":"")+r.join("; "));for(m in h)p.push(m+'\x3d"'+CKEDITOR.tools.htmlEncode(h[m])+'" ');p.push("\x3e",q,"\x3c/",k,"\x3e");c.push(p.join(""));(this._||(this._={})).dialog=a;"boolean"==typeof b.isChanged&&(this.isChanged=function(){return b.isChanged});"function"==typeof b.isChanged&&(this.isChanged=b.isChanged);"function"==typeof b.setValue&&(this.setValue=CKEDITOR.tools.override(this.setValue,function(a){return function(c){a.call(this,
b.setValue.call(this,c))}}));"function"==typeof b.getValue&&(this.getValue=CKEDITOR.tools.override(this.getValue,function(a){return function(){return b.getValue.call(this,a.call(this))}}));CKEDITOR.event.implementOn(this);this.registerEvents(b);this.accessKeyUp&&this.accessKeyDown&&b.accessKey&&T(this,a,"CTRL+"+b.accessKey);var t=this;a.on("load",function(){var b=t.getInputElement();if(b){var c=t.type in{checkbox:1,ratio:1}&&CKEDITOR.env.ie&&8>CKEDITOR.env.version?"cke_dialog_ui_focused":"";b.on("focus",
function(){a._.tabBarMode=!1;a._.hasFocus=!0;t.fire("focus");c&&this.addClass(c)});b.on("blur",function(){t.fire("blur");c&&this.removeClass(c)})}});CKEDITOR.tools.extend(this,b);this.keyboardFocusable&&(this.tabIndex=b.tabIndex||0,this.focusIndex=a._.focusList.push(this)-1,this.on("focus",function(){a._.currentFocusIndex=t.focusIndex}))}},hbox:function(a,b,c,e,d){if(!(4>arguments.length)){this._||(this._={});var f=this._.children=b,g=d&&d.widths||null,k=d&&d.height||null,p,l={role:"presentation"};
d&&d.align&&(l.align=d.align);CKEDITOR.ui.dialog.uiElement.call(this,a,d||{type:"hbox"},e,"table",{},l,function(){var a=['\x3ctbody\x3e\x3ctr class\x3d"cke_dialog_ui_hbox"\x3e'];for(p=0;p<c.length;p++){var b="cke_dialog_ui_hbox_child",e=[];0===p&&(b="cke_dialog_ui_hbox_first");p==c.length-1&&(b="cke_dialog_ui_hbox_last");a.push('\x3ctd class\x3d"',b,'" role\x3d"presentation" ');g?g[p]&&e.push("width:"+y(g[p])):e.push("width:"+Math.floor(100/c.length)+"%");k&&e.push("height:"+y(k));d&&void 0!==d.padding&&
e.push("padding:"+y(d.padding));CKEDITOR.env.ie&&CKEDITOR.env.quirks&&f[p].align&&e.push("text-align:"+f[p].align);0<e.length&&a.push('style\x3d"'+e.join("; ")+'" ');a.push("\x3e",c[p],"\x3c/td\x3e")}a.push("\x3c/tr\x3e\x3c/tbody\x3e");return a.join("")})}},vbox:function(a,b,c,e,d){if(!(3>arguments.length)){this._||(this._={});var f=this._.children=b,g=d&&d.width||null,k=d&&d.heights||null;CKEDITOR.ui.dialog.uiElement.call(this,a,d||{type:"vbox"},e,"div",null,{role:"presentation"},function(){var b=
['\x3ctable role\x3d"presentation" cellspacing\x3d"0" border\x3d"0" '];b.push('style\x3d"');d&&d.expand&&b.push("height:100%;");b.push("width:"+y(g||"100%"),";");CKEDITOR.env.webkit&&b.push("float:none;");b.push('"');b.push('align\x3d"',CKEDITOR.tools.htmlEncode(d&&d.align||("ltr"==a.getParentEditor().lang.dir?"left":"right")),'" ');b.push("\x3e\x3ctbody\x3e");for(var e=0;e<c.length;e++){var h=[];b.push('\x3ctr\x3e\x3ctd role\x3d"presentation" ');g&&h.push("width:"+y(g||"100%"));k?h.push("height:"+
y(k[e])):d&&d.expand&&h.push("height:"+Math.floor(100/c.length)+"%");d&&void 0!==d.padding&&h.push("padding:"+y(d.padding));CKEDITOR.env.ie&&CKEDITOR.env.quirks&&f[e].align&&h.push("text-align:"+f[e].align);0<h.length&&b.push('style\x3d"',h.join("; "),'" ');b.push(' class\x3d"cke_dialog_ui_vbox_child"\x3e',c[e],"\x3c/td\x3e\x3c/tr\x3e")}b.push("\x3c/tbody\x3e\x3c/table\x3e");return b.join("")})}}}})();CKEDITOR.ui.dialog.uiElement.prototype={getElement:function(){return CKEDITOR.document.getById(this.domId)},
getInputElement:function(){return this.getElement()},getDialog:function(){return this._.dialog},setValue:function(a,b){this.getInputElement().setValue(a);!b&&this.fire("change",{value:a});return this},getValue:function(){return this.getInputElement().getValue()},isChanged:function(){return!1},selectParentTab:function(){for(var a=this.getInputElement();(a=a.getParent())&&-1==a.$.className.search("cke_dialog_page_contents"););if(!a)return this;a=a.getAttribute("name");this._.dialog._.currentTabId!=
a&&this._.dialog.selectPage(a);return this},focus:function(){this.selectParentTab().getInputElement().focus();return this},registerEvents:function(a){var b=/^on([A-Z]\w+)/,c,e=function(a,b,c,d){b.on("load",function(){a.getInputElement().on(c,d,a)})},d;for(d in a)if(c=d.match(b))this.eventProcessors[d]?this.eventProcessors[d].call(this,this._.dialog,a[d]):e(this,this._.dialog,c[1].toLowerCase(),a[d]);return this},eventProcessors:{onLoad:function(a,b){a.on("load",b,this)},onShow:function(a,b){a.on("show",
b,this)},onHide:function(a,b){a.on("hide",b,this)}},accessKeyDown:function(){this.focus()},accessKeyUp:function(){},disable:function(){var a=this.getElement();this.getInputElement().setAttribute("disabled","true");a.addClass("cke_disabled")},enable:function(){var a=this.getElement();this.getInputElement().removeAttribute("disabled");a.removeClass("cke_disabled")},isEnabled:function(){return!this.getElement().hasClass("cke_disabled")},isVisible:function(){return this.getInputElement().isVisible()},
isFocusable:function(){return this.isEnabled()&&this.isVisible()?!0:!1}};CKEDITOR.ui.dialog.hbox.prototype=CKEDITOR.tools.extend(new CKEDITOR.ui.dialog.uiElement,{getChild:function(a){if(1>arguments.length)return this._.children.concat();a.splice||(a=[a]);return 2>a.length?this._.children[a[0]]:this._.children[a[0]]&&this._.children[a[0]].getChild?this._.children[a[0]].getChild(a.slice(1,a.length)):null}},!0);CKEDITOR.ui.dialog.vbox.prototype=new CKEDITOR.ui.dialog.hbox;(function(){var a={build:function(a,
c,e){for(var d=c.children,f,g=[],k=[],p=0;p<d.length&&(f=d[p]);p++){var l=[];g.push(l);k.push(CKEDITOR.dialog._.uiElementBuilders[f.type].build(a,f,l))}return new CKEDITOR.ui.dialog[c.type](a,k,g,e,c)}};CKEDITOR.dialog.addUIElement("hbox",a);CKEDITOR.dialog.addUIElement("vbox",a)})();CKEDITOR.dialogCommand=function(a,b){this.dialogName=a;CKEDITOR.tools.extend(this,b,!0)};CKEDITOR.dialogCommand.prototype={exec:function(a){var b=this.tabId;a.openDialog(this.dialogName,function(a){b&&a.selectPage(b)})},
canUndo:!1,editorFocus:1};(function(){var a=/^\d*$/,b=/^\d*(?:\.\d+)?$/,c=/^(((\d*(\.\d+))|(\d*))(px|\%)?)?$/,e=/^(((\d*(\.\d+))|(\d*))(px|em|ex|in|cm|mm|pt|pc|\%)?)?$/i,d=/^(--|-?([a-zA-Z_]|\\))(\\|[a-zA-Z0-9-_])*\s*?:\s*?[^:;]+$/;CKEDITOR.VALIDATE_OR=1;CKEDITOR.VALIDATE_AND=2;CKEDITOR.dialog.validate={functions:function(){var a=arguments;return function(b){this&&this.getValue&&(b=this.getValue());var c,d=CKEDITOR.VALIDATE_AND,e=[],h;for(h=0;h<a.length;h++)if("function"==typeof a[h])e.push(a[h]);
else break;h<a.length&&"string"==typeof a[h]&&(c=a[h],h++);h<a.length&&"number"==typeof a[h]&&(d=a[h]);h=d==CKEDITOR.VALIDATE_AND;for(var q=0;q<e.length;q++){var m=!0===e[q](b);h=d==CKEDITOR.VALIDATE_AND?h&&m:h||m}return h?!0:c}},regex:function(a,b){return this.functions(function(b){return a.test(b)},b)},notEmpty:function(a){return this.functions(function(a){return 0<a.replace(RegExp("^[\\u0020\\u00a0\\u1680\\u202f\\u205f\\u3000\\u2000-\\u200a\\s]+|[\\u0020\\u00a0\\u1680\\u202f\\u205f\\u3000\\u2000-\\u200a\\s]+$",
"g"),"").length},a)},integer:function(b){return this.regex(a,b)},number:function(a){return this.regex(b,a)},cssLength:function(a){return this.functions(function(a){return e.test(CKEDITOR.tools.trim(a))},a)},htmlLength:function(a){return this.functions(function(a){return c.test(CKEDITOR.tools.trim(a))},a)},inlineStyle:function(a){return this.functions(function(a){a=CKEDITOR.tools.trim(a).split(";");""===a[a.length-1]&&a.pop();return CKEDITOR.tools.array.every(a,function(a){return d.test(CKEDITOR.tools.trim(a))})},
a)},equals:function(a,b){return this.functions(function(b){return b==a},b)},notEqual:function(a,b){return this.functions(function(b){return b!=a},b)}};CKEDITOR.on("instanceDestroyed",function(a){if(CKEDITOR.tools.isEmpty(CKEDITOR.instances)){for(var b;b=CKEDITOR.dialog._.currentTop;)b.hide();for(var c in B)B[c].remove();B={}}a=a.editor._.storedDialogs;for(var d in a)a[d].destroy()})})();CKEDITOR.tools.extend(CKEDITOR.editor.prototype,{openDialog:function(a,b,c){var e=null,d=CKEDITOR.dialog._.dialogDefinitions[a];
null===CKEDITOR.dialog._.currentTop&&P(this);if("function"==typeof d)d=this._.storedDialogs||(this._.storedDialogs={}),e=d[a]||(d[a]=new CKEDITOR.dialog(this,a)),e.setModel(c),b&&b.call(e,e),e.show();else{if("failed"==d)throw Q(this),Error('[CKEDITOR.dialog.openDialog] Dialog "'+a+'" failed when loading definition.');"string"==typeof d&&CKEDITOR.scriptLoader.load(CKEDITOR.getUrl(d),function(){"function"!=typeof CKEDITOR.dialog._.dialogDefinitions[a]&&(CKEDITOR.dialog._.dialogDefinitions[a]="failed");
this.openDialog(a,b,c)},this,0,1)}CKEDITOR.skin.loadPart("dialog");if(e)e.once("hide",function(){e.setModel(null)},null,null,999);return e}});CKEDITOR.plugins.add("dialog",{requires:"dialogui",init:function(a){V||(CKEDITOR.document.appendStyleSheet(this.path+"styles/dialog.css"),V=!0);a.on("doubleclick",function(b){b.data.dialog&&a.openDialog(b.data.dialog)},null,null,999)}})})();CKEDITOR.plugins.add("about",{requires:"dialog",init:function(a){var b=a.addCommand("about",new CKEDITOR.dialogCommand("about"));b.modes={wysiwyg:1,source:1};b.canUndo=!1;b.readOnly=1;a.ui.addButton&&a.ui.addButton("About",{label:a.lang.about.dlgTitle,command:"about",toolbar:"about"});CKEDITOR.dialog.add("about",this.path+"dialogs/about.js")}});CKEDITOR.plugins.add("basicstyles",{init:function(a){var f=0,d=function(h,d,c,b){if(b){b=new CKEDITOR.style(b);var g=e[c];g.unshift(b);a.attachStyleStateChange(b,function(b){!a.readOnly&&a.getCommand(c).setState(b)});a.addCommand(c,new CKEDITOR.styleCommand(b,{contentForms:g}));a.ui.addButton&&a.ui.addButton(h,{isToggle:!0,label:d,command:c,toolbar:"basicstyles,"+(f+=10)})}},e={bold:["strong","b",["span",function(a){a=a.styles["font-weight"];return"bold"==a||700<=+a}]],italic:["em","i",["span",function(a){return"italic"==
a.styles["font-style"]}]],underline:["u",["span",function(a){return"underline"==a.styles["text-decoration"]}]],strike:["s","strike",["span",function(a){return"line-through"==a.styles["text-decoration"]}]],subscript:["sub"],superscript:["sup"]},c=a.config,b=a.lang.basicstyles;d("Bold",b.bold,"bold",c.coreStyles_bold);d("Italic",b.italic,"italic",c.coreStyles_italic);d("Underline",b.underline,"underline",c.coreStyles_underline);d("Strike",b.strike,"strike",c.coreStyles_strike);d("Subscript",b.subscript,
"subscript",c.coreStyles_subscript);d("Superscript",b.superscript,"superscript",c.coreStyles_superscript);a.setKeystroke([[CKEDITOR.CTRL+66,"bold"],[CKEDITOR.CTRL+73,"italic"],[CKEDITOR.CTRL+85,"underline"]])},afterInit:function(a){if(a.config.coreStyles_toggleSubSup){var f=a.getCommand("subscript"),d=a.getCommand("superscript");if(f&&d)a.on("afterCommandExec",function(e){e=e.data.name;if("subscript"===e||"superscript"===e){var c="subscript"===e?d:f;("subscript"===e?f:d).state===CKEDITOR.TRISTATE_ON&&
c.state===CKEDITOR.TRISTATE_ON&&(c.exec(a),a.fire("updateSnapshot"))}})}}});CKEDITOR.config.coreStyles_bold={element:"strong",overrides:"b"};CKEDITOR.config.coreStyles_italic={element:"em",overrides:"i"};CKEDITOR.config.coreStyles_underline={element:"u"};CKEDITOR.config.coreStyles_strike={element:"s",overrides:"strike"};CKEDITOR.config.coreStyles_subscript={element:"sub"};CKEDITOR.config.coreStyles_superscript={element:"sup"};CKEDITOR.config.coreStyles_toggleSubSup=!1;(function(){function q(b,a){CKEDITOR.tools.extend(this,a,{editor:b,id:"cke-"+CKEDITOR.tools.getUniqueId(),area:b._.notificationArea});a.type||(this.type="info");this.element=this._createElement();b.plugins.clipboard&&CKEDITOR.plugins.clipboard.preventDefaultDropOnElement(this.element)}function r(b){var a=this;this.editor=b;this.notifications=[];this.element=this._createElement();this._uiBuffer=CKEDITOR.tools.eventsBuffer(10,this._layout,this);this._changeBuffer=CKEDITOR.tools.eventsBuffer(500,this._layout,
this);b.on("destroy",function(){a._removeListeners();a.element.remove()})}CKEDITOR.plugins.add("notification",{init:function(b){function a(b){var a=new CKEDITOR.dom.element("div");a.setStyles({position:"fixed","margin-left":"-9999px"});a.setAttributes({"aria-live":"assertive","aria-atomic":"true"});a.setText(b);CKEDITOR.document.getBody().append(a);setTimeout(function(){a.remove()},100)}b._.notificationArea=new r(b);b.showNotification=function(a,d,e){var f,l;"progress"==d?f=e:l=e;a=new CKEDITOR.plugins.notification(b,
{message:a,type:d,progress:f,duration:l});a.show();return a};b.on("key",function(c){if(27==c.data.keyCode){var d=b._.notificationArea.notifications;d.length&&(a(b.lang.notification.closed),d[d.length-1].hide(),c.cancel())}})}});q.prototype={show:function(){!1!==this.editor.fire("notificationShow",{notification:this})&&(this.area.add(this),this._hideAfterTimeout())},update:function(b){var a=!0;!1===this.editor.fire("notificationUpdate",{notification:this,options:b})&&(a=!1);var c=this.element,d=c.findOne(".cke_notification_message"),
e=c.findOne(".cke_notification_progress"),f=b.type;c.removeAttribute("role");b.progress&&"progress"!=this.type&&(f="progress");f&&(c.removeClass(this._getClass()),c.removeAttribute("aria-label"),this.type=f,c.addClass(this._getClass()),c.setAttribute("aria-label",this.type),"progress"!=this.type||e?"progress"!=this.type&&e&&e.remove():(e=this._createProgressElement(),e.insertBefore(d)));void 0!==b.message&&(this.message=b.message,d.setHtml(this.message));void 0!==b.progress&&(this.progress=b.progress,
e&&e.setStyle("width",this._getPercentageProgress()));a&&b.important&&(c.setAttribute("role","alert"),this.isVisible()||this.area.add(this));this.duration=b.duration;this._hideAfterTimeout()},hide:function(){!1!==this.editor.fire("notificationHide",{notification:this})&&this.area.remove(this)},isVisible:function(){return 0<=CKEDITOR.tools.indexOf(this.area.notifications,this)},_createElement:function(){var b=this,a,c,d=this.editor.lang.common.close;a=new CKEDITOR.dom.element("div");a.addClass("cke_notification");
a.addClass(this._getClass());a.setAttributes({id:this.id,role:"alert","aria-label":this.type});"progress"==this.type&&a.append(this._createProgressElement());c=new CKEDITOR.dom.element("p");c.addClass("cke_notification_message");c.setHtml(this.message);a.append(c);c=CKEDITOR.dom.element.createFromHtml('\x3ca class\x3d"cke_notification_close" href\x3d"javascript:void(0)" title\x3d"'+d+'" role\x3d"button" tabindex\x3d"-1"\x3e\x3cspan class\x3d"cke_label"\x3eX\x3c/span\x3e\x3c/a\x3e');a.append(c);c.on("click",
function(){b.editor.focus();b.hide()});return a},_getClass:function(){return"progress"==this.type?"cke_notification_info":"cke_notification_"+this.type},_createProgressElement:function(){var b=new CKEDITOR.dom.element("span");b.addClass("cke_notification_progress");b.setStyle("width",this._getPercentageProgress());return b},_getPercentageProgress:function(){return Math.round(100*(this.progress||0))+"%"},_hideAfterTimeout:function(){var b=this,a;this._hideTimeoutId&&clearTimeout(this._hideTimeoutId);
if("number"==typeof this.duration)a=this.duration;else if("info"==this.type||"success"==this.type)a="number"==typeof this.editor.config.notification_duration?this.editor.config.notification_duration:5E3;a&&(b._hideTimeoutId=setTimeout(function(){b.hide()},a))}};r.prototype={add:function(b){this.notifications.push(b);this.element.append(b.element);1==this.element.getChildCount()&&(CKEDITOR.document.getBody().append(this.element),this._attachListeners());this._layout()},remove:function(b){var a=CKEDITOR.tools.indexOf(this.notifications,
b);0>a||(this.notifications.splice(a,1),b.element.remove(),this.element.getChildCount()||(this._removeListeners(),this.element.remove()))},_createElement:function(){var b=this.editor,a=b.config,c=new CKEDITOR.dom.element("div");c.addClass("cke_notifications_area");c.setAttribute("id","cke_notifications_area_"+b.name);c.setStyle("z-index",a.baseFloatZIndex-2);return c},_attachListeners:function(){var b=CKEDITOR.document.getWindow(),a=this.editor;b.on("scroll",this._uiBuffer.input);b.on("resize",this._uiBuffer.input);
a.on("change",this._changeBuffer.input);a.on("floatingSpaceLayout",this._layout,this,null,20);a.on("blur",this._layout,this,null,20)},_removeListeners:function(){var b=CKEDITOR.document.getWindow(),a=this.editor;b.removeListener("scroll",this._uiBuffer.input);b.removeListener("resize",this._uiBuffer.input);a.removeListener("change",this._changeBuffer.input);a.removeListener("floatingSpaceLayout",this._layout);a.removeListener("blur",this._layout)},_layout:function(){function b(){a.setStyle("left",
k(n+d.width-g-h))}var a=this.element,c=this.editor,d=c.ui.contentsElement.getClientRect(),e=c.ui.contentsElement.getDocumentPosition(),f,l,u=a.getClientRect(),m,g=this._notificationWidth,h=this._notificationMargin;m=CKEDITOR.document.getWindow();var p=m.getScrollPosition(),t=m.getViewPaneSize(),q=CKEDITOR.document.getBody(),r=q.getDocumentPosition(),k=CKEDITOR.tools.cssLength;g&&h||(m=this.element.getChild(0),g=this._notificationWidth=m.getClientRect().width,h=this._notificationMargin=parseInt(m.getComputedStyle("margin-left"),
10)+parseInt(m.getComputedStyle("margin-right"),10));c.toolbar&&(f=c.ui.space(c.config.toolbarLocation),l=f.getClientRect());f&&f.isVisible()&&l.bottom>d.top&&l.bottom<d.bottom-u.height?a.setStyles({position:"fixed",top:k(l.bottom)}):0<d.top?a.setStyles({position:"absolute",top:k(e.y)}):e.y+d.height-u.height>p.y?a.setStyles({position:"fixed",top:0}):a.setStyles({position:"absolute",top:k(e.y+d.height-u.height)});var n="fixed"==a.getStyle("position")?d.left:"static"!=q.getComputedStyle("position")?
e.x-r.x:e.x;d.width<g+h?e.x+g+h>p.x+t.width?b():a.setStyle("left",k(n)):e.x+g+h>p.x+t.width?a.setStyle("left",k(n)):e.x+d.width/2+g/2+h>p.x+t.width?a.setStyle("left",k(n-e.x+p.x+t.width-g-h)):0>d.left+d.width-g-h?b():0>d.left+d.width/2-g/2?a.setStyle("left",k(n-e.x+p.x)):a.setStyle("left",k(n+d.width/2-g/2-h/2))}};CKEDITOR.plugins.notification=q})();(function(){var c='\x3ca id\x3d"{id}" class\x3d"cke_button cke_button__{name} cke_button_{state} {cls}"'+(CKEDITOR.env.gecko&&!CKEDITOR.env.hc?"":" href\x3d\"javascript:void('{titleJs}')\"")+' title\x3d"{title}" tabindex\x3d"-1" hidefocus\x3d"true" role\x3d"button" aria-labelledby\x3d"{id}_label" aria-describedby\x3d"{id}_description" aria-haspopup\x3d"{hasArrow}" aria-disabled\x3d"{ariaDisabled}"{hasArrowAriaHtml}{toggleAriaHtml}';CKEDITOR.env.gecko&&CKEDITOR.env.mac&&(c+=' onkeypress\x3d"return false;"');
CKEDITOR.env.gecko&&(c+=' onblur\x3d"this.style.cssText \x3d this.style.cssText;"');var l="";CKEDITOR.env.ie&&(l='return false;" onmouseup\x3d"CKEDITOR.tools.getMouseButton(event)\x3d\x3dCKEDITOR.MOUSE_BUTTON_LEFT\x26\x26');var c=c+(' onkeydown\x3d"return CKEDITOR.tools.callFunction({keydownFn},event);" onfocus\x3d"return CKEDITOR.tools.callFunction({focusFn},event);" onclick\x3d"'+l+'CKEDITOR.tools.callFunction({clickFn},this);return false;"\x3e\x3cspan class\x3d"cke_button_icon cke_button__{iconName}_icon" style\x3d"{style}"')+
'\x3e\x26nbsp;\x3c/span\x3e\x3cspan id\x3d"{id}_label" class\x3d"cke_button_label cke_button__{name}_label" aria-hidden\x3d"false"\x3e{label}\x3c/span\x3e\x3cspan id\x3d"{id}_description" class\x3d"cke_button_label" aria-hidden\x3d"false"\x3e{ariaShortcutSpace}{ariaShortcut}\x3c/span\x3e{arrowHtml}\x3c/a\x3e',v=CKEDITOR.addTemplate("buttonArrow",'\x3cspan class\x3d"cke_button_arrow"\x3e'+(CKEDITOR.env.hc?"\x26#9660;":"")+"\x3c/span\x3e"),w=CKEDITOR.addTemplate("button",c);CKEDITOR.plugins.add("button",
{beforeInit:function(a){a.ui.addHandler(CKEDITOR.UI_BUTTON,CKEDITOR.ui.button.handler)}});CKEDITOR.UI_BUTTON="button";CKEDITOR.ui.button=function(a){CKEDITOR.tools.extend(this,a,{isToggle:a.isToggle||!1,title:a.label,click:a.click||function(b){b.execCommand(a.command)}});this._={}};CKEDITOR.ui.button.handler={create:function(a){return new CKEDITOR.ui.button(a)}};CKEDITOR.ui.button.prototype={render:function(a,b){function c(){var f=a.mode;f&&(f=this.modes[f]?void 0!==p[f]?p[f]:CKEDITOR.TRISTATE_OFF:
CKEDITOR.TRISTATE_DISABLED,f=a.readOnly&&!this.readOnly?CKEDITOR.TRISTATE_DISABLED:f,this.setState(f),this.refresh&&this.refresh())}var p=null,q=CKEDITOR.env,r=this._.id=CKEDITOR.tools.getNextId(),g="",d=this.command,l,m,k;this._.editor=a;var e={id:r,button:this,editor:a,focus:function(){CKEDITOR.document.getById(r).focus()},execute:function(){this.button.click(a)},attach:function(a){this.button.attach(a)}},x=CKEDITOR.tools.addFunction(function(a){if(e.onkey)return a=new CKEDITOR.dom.event(a),!1!==
e.onkey(e,a.getKeystroke())}),y=CKEDITOR.tools.addFunction(function(a){var b;e.onfocus&&(b=!1!==e.onfocus(e,new CKEDITOR.dom.event(a)));return b}),u=0;e.clickFn=l=CKEDITOR.tools.addFunction(function(){u&&(a.unlockSelection(1),u=0);e.execute();q.iOS&&a.focus()});this.modes?(p={},a.on("beforeModeUnload",function(){a.mode&&this._.state!=CKEDITOR.TRISTATE_DISABLED&&(p[a.mode]=this._.state)},this),a.on("activeFilterChange",c,this),a.on("mode",c,this),!this.readOnly&&a.on("readOnly",c,this)):d&&(d=a.getCommand(d))&&
(d.on("state",function(){this.setState(d.state)},this),g+=d.state==CKEDITOR.TRISTATE_ON?"on":d.state==CKEDITOR.TRISTATE_DISABLED?"disabled":"off");var n;if(this.directional)a.on("contentDirChanged",function(b){var c=CKEDITOR.document.getById(this._.id),d=c.getFirst();b=b.data;b!=a.lang.dir?c.addClass("cke_"+b):c.removeClass("cke_ltr").removeClass("cke_rtl");d.setAttribute("style",CKEDITOR.skin.getIconStyle(n,"rtl"==b,this.icon,this.iconOffset))},this);d?(m=a.getCommandKeystroke(d))&&(k=CKEDITOR.tools.keystrokeToString(a.lang.common.keyboard,
m)):g+="off";m=this.name||this.command;var h=null,t=this.icon;n=m;this.icon&&!/\./.test(this.icon)?(n=this.icon,t=null):(this.icon&&(h=this.icon),CKEDITOR.env.hidpi&&this.iconHiDpi&&(h=this.iconHiDpi));h?(CKEDITOR.skin.addIcon(h,h),t=null):h=n;g={id:r,name:m,iconName:n,label:this.label,cls:(this.hasArrow?"cke_button_expandable ":"")+(this.className||""),state:g,ariaDisabled:"disabled"==g?"true":"false",title:this.title+(k?" ("+k.display+")":""),ariaShortcutSpace:k?"\x26nbsp;":"",ariaShortcut:k?a.lang.common.keyboardShortcut+
" "+k.aria:"",titleJs:q.gecko&&!q.hc?"":(this.title||"").replace("'",""),hasArrow:"string"===typeof this.hasArrow&&this.hasArrow||(this.hasArrow?"true":"false"),keydownFn:x,focusFn:y,clickFn:l,style:CKEDITOR.skin.getIconStyle(h,"rtl"==a.lang.dir,t,this.iconOffset),arrowHtml:this.hasArrow?v.output():"",hasArrowAriaHtml:this.hasArrow?' aria-expanded\x3d"false"':"",toggleAriaHtml:this.isToggle?'aria-pressed\x3d"false"':""};w.output(g,b);if(this.onRender)this.onRender();return e},setState:function(a){if(this._.state==
a)return!1;this._.state=a;var b=CKEDITOR.document.getById(this._.id);return b?(b.setState(a,"cke_button"),b.setAttribute("aria-disabled",a==CKEDITOR.TRISTATE_DISABLED),this.isToggle&&!this.hasArrow&&b.setAttribute("aria-pressed",a===CKEDITOR.TRISTATE_ON),!0):!1},getState:function(){return this._.state},toFeature:function(a){if(this._.feature)return this._.feature;var b=this;this.allowedContent||this.requiredContent||!this.command||(b=a.getCommand(this.command)||b);return this._.feature=b}};CKEDITOR.ui.prototype.addButton=
function(a,b){this.add(a,CKEDITOR.UI_BUTTON,b)}})();(function(){function D(b){function d(){for(var a=f(),e=CKEDITOR.tools.clone(b.config.toolbarGroups)||v(b),n=0;n<e.length;n++){var g=e[n];if("/"!=g){"string"==typeof g&&(g=e[n]={name:g});var l,d=g.groups;if(d)for(var h=0;h<d.length;h++)l=d[h],(l=a[l])&&c(g,l);(l=a[g.name])&&c(g,l)}}return e}function f(){var a={},c,e,g;for(c in b.ui.items)e=b.ui.items[c],g=e.toolbar||"others",g=g.split(","),e=g[0],g=parseInt(g[1]||-1,10),a[e]||(a[e]=[]),a[e].push({name:c,order:g});for(e in a)a[e]=a[e].sort(function(a,
b){return a.order==b.order?0:0>b.order?-1:0>a.order?1:a.order<b.order?-1:1});return a}function c(c,e){if(e.length){c.items?c.items.push(b.ui.create("-")):c.items=[];for(var d;d=e.shift();)d="string"==typeof d?d:d.name,a&&-1!=CKEDITOR.tools.indexOf(a,d)||(d=b.ui.create(d))&&b.addFeature(d)&&c.items.push(d)}}function h(a){var b=[],e,d,h;for(e=0;e<a.length;++e)d=a[e],h={},"/"==d?b.push(d):CKEDITOR.tools.isArray(d)?(c(h,CKEDITOR.tools.clone(d)),b.push(h)):d.items&&(c(h,CKEDITOR.tools.clone(d.items)),
h.name=d.name,b.push(h));return b}var a=function(a){return a&&"string"===typeof a?a.split(","):a}(b.config.removeButtons),e=b.config.toolbar;"string"==typeof e&&(e=b.config["toolbar_"+e]);return b.toolbar=e?h(e):d()}function v(b){return b._.toolbarGroups||(b._.toolbarGroups=[{name:"document",groups:["mode","document","doctools"]},{name:"clipboard",groups:["clipboard","undo"]},{name:"editing",groups:["find","selection","spellchecker"]},{name:"forms"},"/",{name:"basicstyles",groups:["basicstyles","cleanup"]},
{name:"paragraph",groups:["list","indent","blocks","align","bidi"]},{name:"links"},{name:"insert"},"/",{name:"styles"},{name:"colors"},{name:"tools"},{name:"others"},{name:"about"}])}var z=function(){this.toolbars=[]};z.prototype.focus=function(){for(var b=0,d;d=this.toolbars[b++];)for(var f=0,c;c=d.items[f++];)if(c.focus){c.focus();return}};var E={modes:{wysiwyg:1,source:1},readOnly:1,exec:function(b){b.toolbox&&(CKEDITOR.env.ie||CKEDITOR.env.air?setTimeout(function(){b.toolbox.focus()},100):b.toolbox.focus())}};
CKEDITOR.plugins.add("toolbar",{requires:"button",init:function(b){var d,f=function(c,h){var a,e="rtl"==b.lang.dir,k=b.config.toolbarGroupCycling,q=e?37:39,e=e?39:37,k=void 0===k||k;switch(h){case 9:case CKEDITOR.SHIFT+9:for(;!a||!a.items.length;)if(a=9==h?(a?a.next:c.toolbar.next)||b.toolbox.toolbars[0]:(a?a.previous:c.toolbar.previous)||b.toolbox.toolbars[b.toolbox.toolbars.length-1],a.items.length)for(c=a.items[d?a.items.length-1:0];c&&!c.focus;)(c=d?c.previous:c.next)||(a=0);c&&c.focus();return!1;
case q:a=c;do a=a.next,!a&&k&&(a=c.toolbar.items[0]);while(a&&!a.focus);a?a.focus():f(c,9);return!1;case 40:return c.button&&c.button.hasArrow?c.execute():f(c,40==h?q:e),!1;case e:case 38:a=c;do a=a.previous,!a&&k&&(a=c.toolbar.items[c.toolbar.items.length-1]);while(a&&!a.focus);a?a.focus():(d=1,f(c,CKEDITOR.SHIFT+9),d=0);return!1;case 27:return b.focus(),!1;case 13:case 32:return c.execute(),!1;case CKEDITOR.ALT+122:return b.execCommand("elementsPathFocus"),!1}return!0};b.on("uiSpace",function(c){if(c.data.space==
b.config.toolbarLocation){c.removeListener();b.toolbox=new z;var d=CKEDITOR.tools.getNextId(),a=['\x3cspan id\x3d"',d,'" class\x3d"cke_voice_label"\x3e',b.lang.toolbar.toolbars,"\x3c/span\x3e",'\x3cspan id\x3d"'+b.ui.spaceId("toolbox")+'" class\x3d"cke_toolbox" role\x3d"group" aria-labelledby\x3d"',d,'" onmousedown\x3d"return false;"\x3e'],d=!1!==b.config.toolbarStartupExpanded,e,k;b.config.toolbarCanCollapse&&b.elementMode!=CKEDITOR.ELEMENT_MODE_INLINE&&a.push('\x3cspan class\x3d"cke_toolbox_main"'+
(d?"\x3e":' style\x3d"display:none"\x3e'));for(var q=b.toolbox.toolbars,n=D(b),g=n.length,l=0;l<g;l++){var r,m=0,w,p=n[l],v="/"!==p&&("/"===n[l+1]||l==g-1),x;if(p)if(e&&(a.push("\x3c/span\x3e"),k=e=0),"/"===p)a.push('\x3cspan class\x3d"cke_toolbar_break"\x3e\x3c/span\x3e');else{x=p.items||p;for(var y=0;y<x.length;y++){var t=x[y],A;if(t){var B=function(c){c=c.render(b,a);u=m.items.push(c)-1;0<u&&(c.previous=m.items[u-1],c.previous.next=c);c.toolbar=m;c.onkey=f};if(t.type==CKEDITOR.UI_SEPARATOR)k=e&&
t;else{A=!1!==t.canGroup;if(!m){r=CKEDITOR.tools.getNextId();m={id:r,items:[]};w=p.name&&(b.lang.toolbar.toolbarGroups[p.name]||p.name);a.push('\x3cspan id\x3d"',r,'" class\x3d"cke_toolbar'+(v?' cke_toolbar_last"':'"'),w?' aria-labelledby\x3d"'+r+'_label"':"",' role\x3d"toolbar"\x3e');w&&a.push('\x3cspan id\x3d"',r,'_label" class\x3d"cke_voice_label"\x3e',w,"\x3c/span\x3e");a.push('\x3cspan class\x3d"cke_toolbar_start"\x3e\x3c/span\x3e');var u=q.push(m)-1;0<u&&(m.previous=q[u-1],m.previous.next=m)}A?
e||(a.push('\x3cspan class\x3d"cke_toolgroup" role\x3d"presentation"\x3e'),e=1):e&&(a.push("\x3c/span\x3e"),e=0);k&&(B(k),k=0);B(t)}}}e&&(a.push("\x3c/span\x3e"),k=e=0);m&&a.push('\x3cspan class\x3d"cke_toolbar_end"\x3e\x3c/span\x3e\x3c/span\x3e')}}b.config.toolbarCanCollapse&&a.push("\x3c/span\x3e");if(b.config.toolbarCanCollapse&&b.elementMode!=CKEDITOR.ELEMENT_MODE_INLINE){var C=CKEDITOR.tools.addFunction(function(){b.execCommand("toolbarCollapse")});b.on("destroy",function(){CKEDITOR.tools.removeFunction(C)});
b.addCommand("toolbarCollapse",{readOnly:1,exec:function(a){var b=a.ui.space("toolbar_collapser"),c=b.getPrevious(),d=a.ui.space("contents"),e=c.getParent(),h=parseInt(d.$.style.height,10),g=e.$.offsetHeight,f=b.hasClass("cke_toolbox_collapser_min");f?(c.show(),b.removeClass("cke_toolbox_collapser_min"),b.setAttribute("title",a.lang.toolbar.toolbarCollapse)):(c.hide(),b.addClass("cke_toolbox_collapser_min"),b.setAttribute("title",a.lang.toolbar.toolbarExpand));b.getFirst().setText(f?"▲":"◀");d.setStyle("height",
h-(e.$.offsetHeight-g)+"px");a.fire("resize",{outerHeight:a.container.$.offsetHeight,contentsHeight:d.$.offsetHeight,outerWidth:a.container.$.offsetWidth})},modes:{wysiwyg:1,source:1}});b.setKeystroke(CKEDITOR.ALT+(CKEDITOR.env.ie||CKEDITOR.env.webkit?189:109),"toolbarCollapse");a.push('\x3ca title\x3d"'+(d?b.lang.toolbar.toolbarCollapse:b.lang.toolbar.toolbarExpand)+'" id\x3d"'+b.ui.spaceId("toolbar_collapser")+'" tabIndex\x3d"-1" class\x3d"cke_toolbox_collapser');d||a.push(" cke_toolbox_collapser_min");
a.push('" onclick\x3d"CKEDITOR.tools.callFunction('+C+')"\x3e','\x3cspan class\x3d"cke_arrow"\x3e\x26#9650;\x3c/span\x3e',"\x3c/a\x3e")}a.push("\x3c/span\x3e");c.data.html+=a.join("")}});b.on("destroy",function(){if(this.toolbox){var b,d=0,a,e,f;for(b=this.toolbox.toolbars;d<b.length;d++)for(e=b[d].items,a=0;a<e.length;a++)f=e[a],f.clickFn&&CKEDITOR.tools.removeFunction(f.clickFn),f.keyDownFn&&CKEDITOR.tools.removeFunction(f.keyDownFn)}});b.on("uiReady",function(){var c=b.ui.space("toolbox");c&&b.focusManager.add(c,
1)});b.addCommand("toolbarFocus",E);b.setKeystroke(CKEDITOR.ALT+121,"toolbarFocus");b.ui.add("-",CKEDITOR.UI_SEPARATOR,{});b.ui.addHandler(CKEDITOR.UI_SEPARATOR,{create:function(){return{render:function(b,d){d.push('\x3cspan class\x3d"cke_toolbar_separator" role\x3d"separator"\x3e\x3c/span\x3e');return{}}}}})}});CKEDITOR.ui.prototype.addToolbarGroup=function(b,d,f){var c=v(this.editor),h=0===d,a={name:b};if(f){if(f=CKEDITOR.tools.search(c,function(a){return a.name==f})){!f.groups&&(f.groups=[]);if(d&&
(d=CKEDITOR.tools.indexOf(f.groups,d),0<=d)){f.groups.splice(d+1,0,b);return}h?f.groups.splice(0,0,b):f.groups.push(b);return}d=null}d&&(d=CKEDITOR.tools.indexOf(c,function(a){return a.name==d}));h?c.splice(0,0,b):"number"==typeof d?c.splice(d+1,0,a):c.push(b)}})();CKEDITOR.UI_SEPARATOR="separator";CKEDITOR.config.toolbarLocation="top";(function(){function t(a,b,c){b.type||(b.type="auto");if(c&&!1===a.fire("beforePaste",b)||!b.dataValue&&b.dataTransfer.isEmpty())return!1;b.dataValue||(b.dataValue="");if(CKEDITOR.env.gecko&&"drop"==b.method&&a.toolbox)a.once("afterPaste",function(){a.toolbox.focus();a.focus()});return a.fire("paste",b)}function y(a){function b(){var b=a.editable();if(CKEDITOR.plugins.clipboard.isCustomCopyCutSupported){var c=function(b){a.getSelection().isCollapsed()||(a.readOnly&&"cut"==b.name||n.initPasteDataTransfer(b,
a),b.data.preventDefault())};b.on("copy",c);b.on("cut",c);b.on("cut",function(){a.readOnly||a.extractSelectedHtml()},null,null,999)}b.on(n.mainPasteEvent,function(a){"beforepaste"==n.mainPasteEvent&&u||m(a)});"beforepaste"==n.mainPasteEvent&&(b.on("paste",function(a){w||(g(),a.data.preventDefault(),m(a),e("paste"))}),b.on("contextmenu",f,null,null,0),b.on("beforepaste",function(a){!a.data||a.data.$.ctrlKey||a.data.$.shiftKey||f()},null,null,0));b.on("beforecut",function(){!u&&l(a)});var h;b.attachListener(CKEDITOR.env.ie?
b:a.document.getDocumentElement(),"mouseup",function(){h=setTimeout(p,0)});a.on("destroy",function(){clearTimeout(h)});b.on("keyup",p)}function c(b){return{type:b,canUndo:"cut"==b,startDisabled:!0,fakeKeystroke:"cut"==b?CKEDITOR.CTRL+88:CKEDITOR.CTRL+67,exec:function(){"cut"==this.type&&l();var b;var c=this.type;if(CKEDITOR.env.ie)b=e(c);else try{b=a.document.$.execCommand(c,!1,null)}catch(h){b=!1}b||a.showNotification(a.lang.clipboard[this.type+"Error"]);return b}}}function d(){return{canUndo:!1,
async:!0,fakeKeystroke:CKEDITOR.CTRL+86,exec:function(a,b){function c(b,m){m="undefined"!==typeof m?m:!0;b?(b.method="paste",b.dataTransfer||(b.dataTransfer=n.initPasteDataTransfer()),t(a,b,m)):d&&!a._.forcePasteDialog&&a.showNotification(p,"info",a.config.clipboard_notificationDuration);a._.forcePasteDialog=!1;a.fire("afterCommandExec",{name:"paste",command:h,returnValue:!!b})}b="undefined"!==typeof b&&null!==b?b:{};var h=this,d="undefined"!==typeof b.notification?b.notification:!0,m=b.type,e=CKEDITOR.tools.keystrokeToString(a.lang.common.keyboard,
a.getCommandKeystroke(this)),p="string"===typeof d?d:a.lang.clipboard.pasteNotification.replace(/%1/,'\x3ckbd aria-label\x3d"'+e.aria+'"\x3e'+e.display+"\x3c/kbd\x3e"),e="string"===typeof b?b:b.dataValue;m&&!0!==a.config.forcePasteAsPlainText&&"allow-word"!==a.config.forcePasteAsPlainText?a._.nextPasteType=m:delete a._.nextPasteType;"string"===typeof e?c({dataValue:e}):a.getClipboardData(c)}}}function g(){w=1;setTimeout(function(){w=0},100)}function f(){u=1;setTimeout(function(){u=0},10)}function e(b){var c=
a.document,h=c.getBody(),d=!1,m=function(){d=!0};h.on(b,m);7<CKEDITOR.env.version?c.$.execCommand(b):c.$.selection.createRange().execCommand(b);h.removeListener(b,m);return d}function l(){if(CKEDITOR.env.ie&&!CKEDITOR.env.quirks){var b=a.getSelection(),c,h,d;b.getType()==CKEDITOR.SELECTION_ELEMENT&&(c=b.getSelectedElement())&&(h=b.getRanges()[0],d=a.document.createText(""),d.insertBefore(c),h.setStartBefore(d),h.setEndAfter(c),b.selectRanges([h]),setTimeout(function(){c.getParent()&&(d.remove(),b.selectElement(c))},
0))}}function q(b,c){var h=a.document,d=a.editable(),m=function(a){a.cancel()},e;if(!h.getById("cke_pastebin")){var p=a.getSelection(),g=p.createBookmarks();CKEDITOR.env.ie&&p.root.fire("selectionchange");var k=new CKEDITOR.dom.element(!CKEDITOR.env.webkit&&!d.is("body")||CKEDITOR.env.ie?"div":"body",h);k.setAttributes({id:"cke_pastebin","data-cke-temp":"1"});var f=0,h=h.getWindow();CKEDITOR.env.webkit?(d.append(k),k.addClass("cke_editable"),d.is("body")||(f="static"!=d.getComputedStyle("position")?
d:CKEDITOR.dom.element.get(d.$.offsetParent),f=f.getDocumentPosition().y)):d.getAscendant(CKEDITOR.env.ie?"body":"html",1).append(k);k.setStyles({position:"absolute",top:h.getScrollPosition().y-f+10+"px",width:"1px",height:Math.max(1,h.getViewPaneSize().height-20)+"px",overflow:"hidden",margin:0,padding:0});CKEDITOR.env.safari&&k.setStyles(CKEDITOR.tools.cssVendorPrefix("user-select","text"));(f=k.getParent().isReadOnly())?(k.setOpacity(0),k.setAttribute("contenteditable",!0)):k.setStyle("ltr"==a.config.contentsLangDirection?
"left":"right","-10000px");a.on("selectionChange",m,null,null,0);if(CKEDITOR.env.webkit||CKEDITOR.env.gecko)e=d.once("blur",m,null,null,-100);f&&k.focus();f=new CKEDITOR.dom.range(k);f.selectNodeContents(k);var l=f.select();CKEDITOR.env.ie&&(e=d.once("blur",function(){a.lockSelection(l)}));var n=CKEDITOR.document.getWindow().getScrollPosition().y;setTimeout(function(){CKEDITOR.env.webkit&&(CKEDITOR.document.getBody().$.scrollTop=n);e&&e.removeListener();CKEDITOR.env.ie&&d.focus();p.selectBookmarks(g);
k.remove();var b;CKEDITOR.env.webkit&&(b=k.getFirst())&&b.is&&b.hasClass("Apple-style-span")&&(k=b);a.removeListener("selectionChange",m);c(k.getHtml())},0)}}function v(){if("paste"==n.mainPasteEvent)return a.fire("beforePaste",{type:"auto",method:"paste"}),!1;a.focus();g();var b=a.focusManager;b.lock();if(a.editable().fire(n.mainPasteEvent)&&!e("paste"))return b.unlock(),!1;b.unlock();return!0}function h(b){if("wysiwyg"==a.mode)switch(b.data.keyCode){case CKEDITOR.CTRL+86:case CKEDITOR.SHIFT+45:b=
a.editable();g();"paste"==n.mainPasteEvent&&b.fire("beforepaste");break;case CKEDITOR.CTRL+88:case CKEDITOR.SHIFT+46:a.fire("saveSnapshot"),setTimeout(function(){a.fire("saveSnapshot")},50)}}function m(b){var c={type:"auto",method:"paste",dataTransfer:n.initPasteDataTransfer(b)};c.dataTransfer.cacheData();var h=!1!==a.fire("beforePaste",c);h&&n.canClipboardApiBeTrusted(c.dataTransfer,a)?(b.data.preventDefault(),setTimeout(function(){t(a,c)},0)):q(b,function(b){c.dataValue=b.replace(/<span[^>]+data-cke-bookmark[^<]*?<\/span>/ig,
"");h&&t(a,c)})}function p(){if("wysiwyg"==a.mode){var b=k("paste");a.getCommand("cut").setState(k("cut"));a.getCommand("copy").setState(k("copy"));a.getCommand("paste").setState(b);a.fire("pasteState",b)}}function k(b){var c=a.getSelection(),c=c&&c.getRanges()[0];if((a.readOnly||c&&c.checkReadOnly())&&b in{paste:1,cut:1})return CKEDITOR.TRISTATE_DISABLED;if("paste"==b)return CKEDITOR.TRISTATE_OFF;b=a.getSelection();c=b.getRanges();return b.getType()==CKEDITOR.SELECTION_NONE||1==c.length&&c[0].collapsed?
CKEDITOR.TRISTATE_DISABLED:CKEDITOR.TRISTATE_OFF}var n=CKEDITOR.plugins.clipboard,u=0,w=0;(function(){a.on("key",h);a.on("contentDom",b);a.on("selectionChange",p);if(a.contextMenu){a.contextMenu.addListener(function(){return{cut:k("cut"),copy:k("copy"),paste:k("paste")}});var c=null;a.on("menuShow",function(){c&&(c.removeListener(),c=null);var b=a.contextMenu.findItemByCommandName("paste");b&&b.element&&(c=b.element.on("touchend",function(){a._.forcePasteDialog=!0}))})}if(a.ui.addButton)a.once("instanceReady",
function(){a._.pasteButtons&&CKEDITOR.tools.array.forEach(a._.pasteButtons,function(b){if(b=a.ui.get(b))if(b=CKEDITOR.document.getById(b._.id))b.on("touchend",function(){a._.forcePasteDialog=!0})})})})();(function(){function b(c,h,d,m,e){var p=a.lang.clipboard[h];a.addCommand(h,d);a.ui.addButton&&a.ui.addButton(c,{label:p,command:h,toolbar:"clipboard,"+m});a.addMenuItems&&a.addMenuItem(h,{label:p,command:h,group:"clipboard",order:e})}b("Cut","cut",c("cut"),10,1);b("Copy","copy",c("copy"),20,4);b("Paste",
"paste",d(),30,8);a._.pasteButtons||(a._.pasteButtons=[]);a._.pasteButtons.push("Paste")})();a.getClipboardData=function(b,c){function h(a){a.removeListener();a.cancel();c(a.data)}function d(a){a.removeListener();a.cancel();c({type:e,dataValue:a.data.dataValue,dataTransfer:a.data.dataTransfer,method:"paste"})}var m=!1,e="auto";c||(c=b,b=null);a.on("beforePaste",function(a){a.removeListener();m=!0;e=a.data.type},null,null,1E3);a.on("paste",h,null,null,0);!1===v()&&(a.removeListener("paste",h),a._.forcePasteDialog&&
m&&a.fire("pasteDialog")?(a.on("pasteDialogCommit",d),a.on("dialogHide",function(a){a.removeListener();a.data.removeListener("pasteDialogCommit",d);a.data._.committed||c(null)})):c(null))}}function z(a){if(CKEDITOR.env.webkit){if(!a.match(/^[^<]*$/g)&&!a.match(/^(<div><br( ?\/)?><\/div>|<div>[^<]*<\/div>)*$/gi))return"html"}else if(CKEDITOR.env.ie){if(!a.match(/^([^<]|<br( ?\/)?>)*$/gi)&&!a.match(/^(<p>([^<]|<br( ?\/)?>)*<\/p>|(\r\n))*$/gi))return"html"}else if(CKEDITOR.env.gecko){if(!a.match(/^([^<]|<br( ?\/)?>)*$/gi))return"html"}else return"html";
return"htmlifiedtext"}function A(a,b){function c(a){return CKEDITOR.tools.repeat("\x3c/p\x3e\x3cp\x3e",~~(a/2))+(1==a%2?"\x3cbr\x3e":"")}b=b.replace(/(?!\u3000)\s+/g," ").replace(/> +</g,"\x3e\x3c").replace(/<br ?\/>/gi,"\x3cbr\x3e");b=b.replace(/<\/?[A-Z]+>/g,function(a){return a.toLowerCase()});if(b.match(/^[^<]$/))return b;CKEDITOR.env.webkit&&-1<b.indexOf("\x3cdiv\x3e")&&(b=b.replace(/^(<div>(<br>|)<\/div>)(?!$|(<div>(<br>|)<\/div>))/g,"\x3cbr\x3e").replace(/^(<div>(<br>|)<\/div>){2}(?!$)/g,"\x3cdiv\x3e\x3c/div\x3e"),
b.match(/<div>(<br>|)<\/div>/)&&(b="\x3cp\x3e"+b.replace(/(<div>(<br>|)<\/div>)+/g,function(a){return c(a.split("\x3c/div\x3e\x3cdiv\x3e").length+1)})+"\x3c/p\x3e"),b=b.replace(/<\/div><div>/g,"\x3cbr\x3e"),b=b.replace(/<\/?div>/g,""));CKEDITOR.env.gecko&&a.enterMode!=CKEDITOR.ENTER_BR&&(CKEDITOR.env.gecko&&(b=b.replace(/^<br><br>$/,"\x3cbr\x3e")),-1<b.indexOf("\x3cbr\x3e\x3cbr\x3e")&&(b="\x3cp\x3e"+b.replace(/(<br>){2,}/g,function(a){return c(a.length/4)})+"\x3c/p\x3e"));return B(a,b)}function C(a){function b(){var a=
{},b;for(b in CKEDITOR.dtd)"$"!=b.charAt(0)&&"div"!=b&&"span"!=b&&(a[b]=1);return a}var c={};return{get:function(d){return"plain-text"==d?c.plainText||(c.plainText=new CKEDITOR.filter(a,"br")):"semantic-content"==d?((d=c.semanticContent)||(d=new CKEDITOR.filter(a,{}),d.allow({$1:{elements:b(),attributes:!0,styles:!1,classes:!1}}),d=c.semanticContent=d),d):d?new CKEDITOR.filter(a,d):null}}}function x(a,b,c){b=CKEDITOR.htmlParser.fragment.fromHtml(b);var d=new CKEDITOR.htmlParser.basicWriter;c.applyTo(b,
!0,!1,a.activeEnterMode);b.writeHtml(d);return d.getHtml()}function B(a,b){a.enterMode==CKEDITOR.ENTER_BR?b=b.replace(/(<\/p><p>)+/g,function(a){return CKEDITOR.tools.repeat("\x3cbr\x3e",a.length/7*2)}).replace(/<\/?p>/g,""):a.enterMode==CKEDITOR.ENTER_DIV&&(b=b.replace(/<(\/)?p>/g,"\x3c$1div\x3e"));return b}function D(a){a.data.preventDefault();a.data.$.dataTransfer.dropEffect="none"}function E(a){var b=CKEDITOR.plugins.clipboard;a.on("contentDom",function(){function c(b,c,d){c.select();t(a,{dataTransfer:d,
method:"drop"},1);d.sourceEditor.fire("saveSnapshot");d.sourceEditor.editable().extractHtmlFromRange(b);d.sourceEditor.getSelection().selectRanges([b]);d.sourceEditor.fire("saveSnapshot")}function d(c,d){c.select();t(a,{dataTransfer:d,method:"drop"},1);b.resetDragDataTransfer()}function g(b,c,d){var e={$:b.data.$,target:b.data.getTarget()};c&&(e.dragRange=c);d&&(e.dropRange=d);!1===a.fire(b.name,e)&&b.data.preventDefault()}function f(a){a.type!=CKEDITOR.NODE_ELEMENT&&(a=a.getParent());return a.getChildCount()}
var e=a.editable(),l=CKEDITOR.plugins.clipboard.getDropTarget(a),q=a.ui.space("top"),v=a.ui.space("bottom");b.preventDefaultDropOnElement(q);b.preventDefaultDropOnElement(v);e.attachListener(l,"dragstart",g);e.attachListener(a,"dragstart",b.resetDragDataTransfer,b,null,1);e.attachListener(a,"dragstart",function(c){b.initDragDataTransfer(c,a)},null,null,2);e.attachListener(a,"dragstart",function(){var c=b.dragRange=a.getSelection().getRanges()[0];CKEDITOR.env.ie&&10>CKEDITOR.env.version&&(b.dragStartContainerChildCount=
c?f(c.startContainer):null,b.dragEndContainerChildCount=c?f(c.endContainer):null)},null,null,100);e.attachListener(l,"dragend",g);e.attachListener(a,"dragend",b.initDragDataTransfer,b,null,1);e.attachListener(a,"dragend",b.resetDragDataTransfer,b,null,100);e.attachListener(l,"dragover",function(a){if(CKEDITOR.env.edge)a.data.preventDefault();else{var b=a.data.getTarget();b&&b.is&&b.is("html")?a.data.preventDefault():CKEDITOR.env.ie&&CKEDITOR.plugins.clipboard.isFileApiSupported&&a.data.$.dataTransfer.types.contains("Files")&&
a.data.preventDefault()}});e.attachListener(l,"drop",function(c){if(!c.data.$.defaultPrevented&&(c.data.preventDefault(),!a.readOnly)){var d=c.data.getTarget();if(!d.isReadOnly()||d.type==CKEDITOR.NODE_ELEMENT&&d.is("html")){var d=b.getRangeAtDropPosition(c,a),e=b.dragRange;d&&g(c,e,d)}}},null,null,9999);e.attachListener(a,"drop",b.initDragDataTransfer,b,null,1);e.attachListener(a,"drop",function(h){if(h=h.data){var e=h.dropRange,p=h.dragRange,k=h.dataTransfer;k.getTransferType(a)==CKEDITOR.DATA_TRANSFER_INTERNAL?
setTimeout(function(){b.internalDrop(p,e,k,a)},0):k.getTransferType(a)==CKEDITOR.DATA_TRANSFER_CROSS_EDITORS?c(p,e,k):d(e,k)}},null,null,9999)})}var r;CKEDITOR.plugins.add("clipboard",{requires:"dialog,notification,toolbar",_supportedFileMatchers:[],init:function(a){function b(a){return-1!==CKEDITOR.tools.indexOf(["image/png","image/jpeg","image/gif"],a.type)}function c(b){return CKEDITOR.tools.array.some(a.plugins.clipboard._supportedFileMatchers,function(a){return a(b)})}function d(b){b.length&&
(b=CKEDITOR.tools.array.unique(b),b=CKEDITOR.tools.array.filter(b,function(a){return!!CKEDITOR.tools.trim(a)}),b=g(b.join(", ")),a.showNotification(b,"info",a.config.clipboard_notificationDuration))}function g(b){return b?a.lang.clipboard.fileFormatNotSupportedNotification.replace(/\${formats\}/g,"\x3cem\x3e"+b+"\x3c/em\x3e"):a.lang.clipboard.fileWithoutFormatNotSupportedNotification}function f(a,b){return CKEDITOR.env.ie&&a.data.fileTransferCancel||!(CKEDITOR.env.ie||b&&v!==b.id)?!1:b.isFileTransfer()&&
1===b.getFilesCount()}var e,l=C(a);a.config.forcePasteAsPlainText?e="plain-text":a.config.pasteFilter?e=a.config.pasteFilter:!CKEDITOR.env.webkit||"pasteFilter"in a.config||(e="semantic-content");a.pasteFilter=l.get(e);y(a);E(a);CKEDITOR.dialog.add("paste",CKEDITOR.getUrl(this.path+"dialogs/paste.js"));var q=CKEDITOR.plugins.clipboard.isCustomDataTypesSupported||CKEDITOR.plugins.clipboard.isFileApiSupported,v;CKEDITOR.plugins.clipboard.addFileMatcher(a,b);a.on("paste",function(a){if(q){var b=a.data;
a=b.dataTransfer;if(!b.dataValue){for(var b=[],e=0;e<a.getFilesCount();e++){var k=a.getFile(e);c(k)||b.push(k.type)}d(b)}}},null,null,1);a.on("paste",function(c){if(q&&a.config.clipboard_handleImages){var d=c.data,e=d.dataTransfer;if(!d.dataValue&&f(c,e)&&(e=e.getFile(0),b(e))){var k=new FileReader;k.addEventListener("load",function(){c.data.dataValue='\x3cimg src\x3d"'+k.result+'" /\x3e';a.fire("paste",c.data)},!1);k.addEventListener("abort",function(){CKEDITOR.env.ie&&(c.data.fileTransferCancel=
!0);a.fire("paste",c.data)},!1);k.addEventListener("error",function(){CKEDITOR.env.ie&&(c.data.fileTransferCancel=!0);a.fire("paste",c.data)},!1);k.readAsDataURL(e);v=d.dataTransfer.id;c.stop()}}},null,null,1);a.on("paste",function(b){b.data.dataTransfer||(b.data.dataTransfer=new CKEDITOR.plugins.clipboard.dataTransfer);if(!b.data.dataValue){var c=b.data.dataTransfer,d=c.getData("text/html");if(d)b.data.dataValue=d,b.data.type="html";else if(d=c.getData("text/plain"))b.data.dataValue=a.editable().transformPlainTextToHtml(d),
b.data.type="text"}},null,null,1);a.on("paste",function(a){var b=a.data.dataValue,c=CKEDITOR.dtd.$block;-1<b.indexOf("Apple-")&&(b=b.replace(/<span class="Apple-converted-space">&nbsp;<\/span>/gi," "),"html"!=a.data.type&&(b=b.replace(/<span class="Apple-tab-span"[^>]*>([^<]*)<\/span>/gi,function(a,b){return b.replace(/\t/g,"\x26nbsp;\x26nbsp; \x26nbsp;")})),-1<b.indexOf('\x3cbr class\x3d"Apple-interchange-newline"\x3e')&&(a.data.startsWithEOL=1,a.data.preSniffing="html",b=b.replace(/<br class="Apple-interchange-newline">/,
"")),b=b.replace(/(<[^>]+) class="Apple-[^"]*"/gi,"$1"));if(b.match(/^<[^<]+cke_(editable|contents)/i)){var d,e,f=new CKEDITOR.dom.element("div");for(f.setHtml(b);1==f.getChildCount()&&(d=f.getFirst())&&d.type==CKEDITOR.NODE_ELEMENT&&(d.hasClass("cke_editable")||d.hasClass("cke_contents"));)f=e=d;e&&(b=e.getHtml().replace(/<br>$/i,""))}CKEDITOR.env.ie?b=b.replace(/^&nbsp;(?: |\r\n)?<(\w+)/g,function(b,d){return d.toLowerCase()in c?(a.data.preSniffing="html","\x3c"+d):b}):CKEDITOR.env.webkit?b=b.replace(/<\/(\w+)><div><br><\/div>$/,
function(b,d){return d in c?(a.data.endsWithEOL=1,"\x3c/"+d+"\x3e"):b}):CKEDITOR.env.gecko&&(b=b.replace(/(\s)<br>$/,"$1"));a.data.dataValue=b},null,null,3);a.on("paste",function(b){b=b.data;var c=a._.nextPasteType||b.type,d=b.dataValue,e,f=a.config.clipboard_defaultContentType||"html",g=b.dataTransfer.getTransferType(a)==CKEDITOR.DATA_TRANSFER_EXTERNAL,q=!0===a.config.forcePasteAsPlainText;e="html"==c||"html"==b.preSniffing?"html":z(d);delete a._.nextPasteType;"htmlifiedtext"==e&&(d=A(a.config,d));
if("text"==c&&"html"==e)d=x(a,d,l.get("plain-text"));else if(g&&a.pasteFilter&&!b.dontFilter||q)d=x(a,d,a.pasteFilter);b.startsWithEOL&&(d='\x3cbr data-cke-eol\x3d"1"\x3e'+d);b.endsWithEOL&&(d+='\x3cbr data-cke-eol\x3d"1"\x3e');"auto"==c&&(c="html"==e||"html"==f?"html":"text");b.type=c;b.dataValue=d;delete b.preSniffing;delete b.startsWithEOL;delete b.endsWithEOL},null,null,6);a.on("paste",function(b){b=b.data;b.dataValue&&(a.insertHtml(b.dataValue,b.type,b.range),setTimeout(function(){a.fire("afterPaste")},
0))},null,null,1E3);a.on("pasteDialog",function(b){setTimeout(function(){a.openDialog("paste",b.data)},0)})}});CKEDITOR.plugins.clipboard={addFileMatcher:function(a,b){a.plugins.clipboard._supportedFileMatchers.push(b)},isCustomCopyCutSupported:CKEDITOR.env.ie&&16>CKEDITOR.env.version||CKEDITOR.env.iOS&&605>CKEDITOR.env.version?!1:!0,isCustomDataTypesSupported:!CKEDITOR.env.ie||16<=CKEDITOR.env.version,isFileApiSupported:!CKEDITOR.env.ie||9<CKEDITOR.env.version,mainPasteEvent:CKEDITOR.env.ie&&!CKEDITOR.env.edge?
"beforepaste":"paste",addPasteButton:function(a,b,c){a.ui.addButton&&(a.ui.addButton(b,c),a._.pasteButtons||(a._.pasteButtons=[]),a._.pasteButtons.push(b))},canClipboardApiBeTrusted:function(a,b){return a.getTransferType(b)!=CKEDITOR.DATA_TRANSFER_EXTERNAL||CKEDITOR.env.chrome&&!a.isEmpty()||CKEDITOR.env.gecko&&(a.getData("text/html")||a.getFilesCount())||CKEDITOR.env.safari&&603<=CKEDITOR.env.version&&!CKEDITOR.env.iOS||CKEDITOR.env.iOS&&605<=CKEDITOR.env.version||CKEDITOR.env.edge&&16<=CKEDITOR.env.version?
!0:!1},getDropTarget:function(a){var b=a.editable();return CKEDITOR.env.ie&&9>CKEDITOR.env.version||b.isInline()?b:a.document},fixSplitNodesAfterDrop:function(a,b,c,d){function g(a,c,d){var f=a;f.type==CKEDITOR.NODE_TEXT&&(f=a.getParent());if(f.equals(c)&&d!=c.getChildCount())return a=b.startContainer.getChild(b.startOffset-1),c=b.startContainer.getChild(b.startOffset),a&&a.type==CKEDITOR.NODE_TEXT&&c&&c.type==CKEDITOR.NODE_TEXT&&(d=a.getLength(),a.setText(a.getText()+c.getText()),c.remove(),b.setStart(a,
d),b.collapse(!0)),!0}var f=b.startContainer;"number"==typeof d&&"number"==typeof c&&f.type==CKEDITOR.NODE_ELEMENT&&(g(a.startContainer,f,c)||g(a.endContainer,f,d))},isDropRangeAffectedByDragRange:function(a,b){var c=b.startContainer,d=b.endOffset;return a.endContainer.equals(c)&&a.endOffset<=d||a.startContainer.getParent().equals(c)&&a.startContainer.getIndex()<d||a.endContainer.getParent().equals(c)&&a.endContainer.getIndex()<d?!0:!1},internalDrop:function(a,b,c,d){var g=CKEDITOR.plugins.clipboard,
f=d.editable(),e,l;d.fire("saveSnapshot");d.fire("lockSnapshot",{dontUpdate:1});CKEDITOR.env.ie&&10>CKEDITOR.env.version&&this.fixSplitNodesAfterDrop(a,b,g.dragStartContainerChildCount,g.dragEndContainerChildCount);(l=this.isDropRangeAffectedByDragRange(a,b))||(e=a.createBookmark(!1));g=b.clone().createBookmark(!1);l&&(e=a.createBookmark(!1));a=e.startNode;b=e.endNode;l=g.startNode;b&&a.getPosition(l)&CKEDITOR.POSITION_PRECEDING&&b.getPosition(l)&CKEDITOR.POSITION_FOLLOWING&&l.insertBefore(a);a=d.createRange();
a.moveToBookmark(e);f.extractHtmlFromRange(a,1);b=d.createRange();g.startNode.getCommonAncestor(f)||(g=d.getSelection().createBookmarks()[0]);b.moveToBookmark(g);t(d,{dataTransfer:c,method:"drop",range:b},1);d.fire("unlockSnapshot")},getRangeAtDropPosition:function(a,b){var c=a.data.$,d=c.clientX,g=c.clientY,f=b.getSelection(!0).getRanges()[0],e=b.createRange();if(a.data.testRange)return a.data.testRange;if(document.caretRangeFromPoint&&b.document.$.caretRangeFromPoint(d,g))c=b.document.$.caretRangeFromPoint(d,
g),e.setStart(CKEDITOR.dom.node(c.startContainer),c.startOffset),e.collapse(!0);else if(c.rangeParent)e.setStart(CKEDITOR.dom.node(c.rangeParent),c.rangeOffset),e.collapse(!0);else{if(CKEDITOR.env.ie&&8<CKEDITOR.env.version&&f&&b.editable().hasFocus)return f;if(document.body.createTextRange){b.focus();c=b.document.getBody().$.createTextRange();try{for(var l=!1,q=0;20>q&&!l;q++){if(!l)try{c.moveToPoint(d,g-q),l=!0}catch(r){}if(!l)try{c.moveToPoint(d,g+q),l=!0}catch(h){}}if(l){var m="cke-temp-"+(new Date).getTime();
c.pasteHTML('\x3cspan id\x3d"'+m+'"\x3e​\x3c/span\x3e');var p=b.document.getById(m);e.moveToPosition(p,CKEDITOR.POSITION_BEFORE_START);p.remove()}else{var k=b.document.$.elementFromPoint(d,g),n=new CKEDITOR.dom.element(k),u;if(n.equals(b.editable())||"html"==n.getName())return f&&f.startContainer&&!f.startContainer.equals(b.editable())?f:null;u=n.getClientRect();d<u.left?e.setStartAt(n,CKEDITOR.POSITION_AFTER_START):e.setStartAt(n,CKEDITOR.POSITION_BEFORE_END);e.collapse(!0)}}catch(t){return null}}else return null}return e},
initDragDataTransfer:function(a,b){var c=a.data.$?a.data.$.dataTransfer:null,d=new this.dataTransfer(c,b);"dragstart"===a.name&&d.storeId();c?this.dragData&&d.id==this.dragData.id?d=this.dragData:this.dragData=d:this.dragData?d=this.dragData:this.dragData=d;a.data.dataTransfer=d},resetDragDataTransfer:function(){this.dragData=null},initPasteDataTransfer:function(a,b){if(this.isCustomCopyCutSupported){if(a&&a.data&&a.data.$){var c=a.data.$.clipboardData,d=new this.dataTransfer(c,b);"copy"!==a.name&&
"cut"!==a.name||d.storeId();this.copyCutData&&d.id==this.copyCutData.id?(d=this.copyCutData,d.$=c):this.copyCutData=d;return d}return new this.dataTransfer(null,b)}return new this.dataTransfer(CKEDITOR.env.edge&&a&&a.data.$&&a.data.$.clipboardData||null,b)},preventDefaultDropOnElement:function(a){a&&a.on("dragover",D)}};r=CKEDITOR.plugins.clipboard.isCustomDataTypesSupported?"cke/id":"Text";CKEDITOR.plugins.clipboard.dataTransfer=function(a,b){a&&(this.$=a);this._={metaRegExp:/^<meta.*?>/i,fragmentRegExp:/\s*\x3c!--StartFragment--\x3e|\x3c!--EndFragment--\x3e\s*/g,
types:[],data:{},files:[],nativeHtmlCache:"",normalizeType:function(a){a=a.toLowerCase();return"text"==a||"text/plain"==a?"Text":"url"==a?"URL":"files"===a?"Files":a}};this._.fallbackDataTransfer=new CKEDITOR.plugins.clipboard.fallbackDataTransfer(this);this.id=this.getData(r);this.id||(this.id="Text"==r?"":"cke-"+CKEDITOR.tools.getUniqueId());b&&(this.sourceEditor=b,this.setData("text/html",b.getSelectedHtml(1)),"Text"==r||this.getData("text/plain")||this.setData("text/plain",b.getSelection().getSelectedText()))};
CKEDITOR.DATA_TRANSFER_INTERNAL=1;CKEDITOR.DATA_TRANSFER_CROSS_EDITORS=2;CKEDITOR.DATA_TRANSFER_EXTERNAL=3;CKEDITOR.plugins.clipboard.dataTransfer.prototype={getData:function(a,b){a=this._.normalizeType(a);var c="text/html"==a&&b?this._.nativeHtmlCache:this._.data[a];if(void 0===c||null===c||""===c){if(this._.fallbackDataTransfer.isRequired())c=this._.fallbackDataTransfer.getData(a,b);else try{c=this.$.getData(a)||""}catch(d){c=""}"text/html"!=a||b||(c=this._stripHtml(c))}"Text"==a&&CKEDITOR.env.gecko&&
this.getFilesCount()&&"file://"==c.substring(0,7)&&(c="");if("string"===typeof c)var g=c.indexOf("\x3c/html\x3e"),c=-1!==g?c.substring(0,g+7):c;return c},setData:function(a,b){a=this._.normalizeType(a);"text/html"==a?(this._.data[a]=this._stripHtml(b),this._.nativeHtmlCache=b):this._.data[a]=b;if(CKEDITOR.plugins.clipboard.isCustomDataTypesSupported||"URL"==a||"Text"==a)if("Text"==r&&"Text"==a&&(this.id=b),this._.fallbackDataTransfer.isRequired())this._.fallbackDataTransfer.setData(a,b);else try{this.$.setData(a,
b)}catch(c){}},storeId:function(){"Text"!==r&&this.setData(r,this.id)},getTransferType:function(a){return this.sourceEditor?this.sourceEditor==a?CKEDITOR.DATA_TRANSFER_INTERNAL:CKEDITOR.DATA_TRANSFER_CROSS_EDITORS:CKEDITOR.DATA_TRANSFER_EXTERNAL},cacheData:function(){function a(a){a=b._.normalizeType(a);var c=b.getData(a);"text/html"==a&&(b._.nativeHtmlCache=b.getData(a,!0),c=b._stripHtml(c));c&&(b._.data[a]=c);b._.types.push(a)}if(this.$){var b=this,c,d,g;if(CKEDITOR.plugins.clipboard.isCustomDataTypesSupported){if(this.$.types)for(c=
0;c<this.$.types.length;c++)a(this.$.types[c])}else a("Text"),a("URL");d=this._getImageFromClipboard();if((g=this.$&&this.$.files||null)||d){this._.files=[];if(g&&g.length)for(c=0;c<g.length;c++)this._.files.push(g[c]);0===this._.files.length&&d&&this._.files.push(d)}}},getFilesCount:function(){if(this._.files.length)return this._.files.length;var a=this.$&&this.$.files||null;return a&&a.length?a.length:this._getImageFromClipboard()?1:0},getFile:function(a){if(this._.files.length)return this._.files[a];
var b=this.$&&this.$.files||null;return b&&b.length?b[a]:0===a?this._getImageFromClipboard():void 0},isFileTransfer:function(){var a=this.getTypes(),a=CKEDITOR.tools.array.filter(a,function(a){return"application/x-moz-file"!==a});return 1===a.length&&"files"===a[0].toLowerCase()},isEmpty:function(){var a={},b;if(this.getFilesCount())return!1;CKEDITOR.tools.array.forEach(CKEDITOR.tools.object.keys(this._.data),function(b){a[b]=1});if(this.$)if(CKEDITOR.plugins.clipboard.isCustomDataTypesSupported){if(this.$.types)for(var c=
0;c<this.$.types.length;c++)a[this.$.types[c]]=1}else a.Text=1,a.URL=1;"Text"!=r&&(a[r]=0);for(b in a)if(a[b]&&""!==this.getData(b))return!1;return!0},getTypes:function(){return 0<this._.types.length?this._.types:this.$&&this.$.types?[].slice.call(this.$.types):[]},_getImageFromClipboard:function(){var a;try{if(this.$&&this.$.items&&this.$.items[0]&&(a=this.$.items[0].getAsFile())&&a.type)return a}catch(b){}},_stripHtml:function(a){function b(a){var b=new CKEDITOR.htmlParser,g,f;b.onTagOpen=function(a){"body"===
a&&(g=b._.htmlPartsRegex.lastIndex)};b.onTagClose=function(a){"body"===a&&(f=b._.htmlPartsRegex.lastIndex)};b.parse(a);return"number"!==typeof g||"number"!==typeof f?a:a.substring(g,f).replace(/<\/body\s*>$/gi,"")}a&&a.length&&(a=b(a),a=a.replace(this._.metaRegExp,""),a=a.replace(this._.fragmentRegExp,""));return a}};CKEDITOR.plugins.clipboard.fallbackDataTransfer=function(a){this._dataTransfer=a;this._customDataFallbackType="text/html"};CKEDITOR.plugins.clipboard.fallbackDataTransfer._isCustomMimeTypeSupported=
null;CKEDITOR.plugins.clipboard.fallbackDataTransfer._customTypes=[];CKEDITOR.plugins.clipboard.fallbackDataTransfer.prototype={isRequired:function(){var a=CKEDITOR.plugins.clipboard.fallbackDataTransfer,b=this._dataTransfer.$;if(null===a._isCustomMimeTypeSupported)if(b){a._isCustomMimeTypeSupported=!1;if(CKEDITOR.env.edge&&17<=CKEDITOR.env.version)return!0;try{b.setData("cke/mimetypetest","cke test value"),a._isCustomMimeTypeSupported="cke test value"===b.getData("cke/mimetypetest"),b.clearData("cke/mimetypetest")}catch(c){}}else return!1;
return!a._isCustomMimeTypeSupported},getData:function(a,b){var c=this._getData(this._customDataFallbackType,!0);if(b)return c;var c=this._extractDataComment(c),d=null,d=a===this._customDataFallbackType?c.content:c.data&&c.data[a]?c.data[a]:this._getData(a,!0);return null!==d?d:""},setData:function(a,b){var c=a===this._customDataFallbackType;c&&(b=this._applyDataComment(b,this._getFallbackTypeData()));var d=b,g=this._dataTransfer.$;try{g.setData(a,d),c&&(this._dataTransfer._.nativeHtmlCache=d)}catch(f){if(this._isUnsupportedMimeTypeError(f)){c=
CKEDITOR.plugins.clipboard.fallbackDataTransfer;-1===CKEDITOR.tools.indexOf(c._customTypes,a)&&c._customTypes.push(a);var c=this._getFallbackTypeContent(),e=this._getFallbackTypeData();e[a]=d;try{d=this._applyDataComment(c,e),g.setData(this._customDataFallbackType,d),this._dataTransfer._.nativeHtmlCache=d}catch(l){d=""}}}return d},_getData:function(a,b){var c=this._dataTransfer._.data;if(!b&&c[a])return c[a];try{return this._dataTransfer.$.getData(a)}catch(d){return null}},_getFallbackTypeContent:function(){var a=
this._dataTransfer._.data[this._customDataFallbackType];a||(a=this._extractDataComment(this._getData(this._customDataFallbackType,!0)).content);return a},_getFallbackTypeData:function(){var a=CKEDITOR.plugins.clipboard.fallbackDataTransfer._customTypes,b=this._extractDataComment(this._getData(this._customDataFallbackType,!0)).data||{},c=this._dataTransfer._.data;CKEDITOR.tools.array.forEach(a,function(a){void 0!==c[a]?b[a]=c[a]:void 0!==b[a]&&(b[a]=b[a])},this);return b},_isUnsupportedMimeTypeError:function(a){return a.message&&
-1!==a.message.search(/element not found/gi)},_extractDataComment:function(a){var b={data:null,content:a||""};if(a&&16<a.length){var c;(c=/\x3c!--cke-data:(.*?)--\x3e/g.exec(a))&&c[1]&&(b.data=JSON.parse(decodeURIComponent(c[1])),b.content=a.replace(c[0],""))}return b},_applyDataComment:function(a,b){var c="";b&&CKEDITOR.tools.object.keys(b).length&&(c="\x3c!--cke-data:"+encodeURIComponent(JSON.stringify(b))+"--\x3e");return c+(a&&a.length?a:"")}}})();
CKEDITOR.config.clipboard_notificationDuration=1E4;CKEDITOR.config.clipboard_handleImages=!0;(function(){function x(a,e,b){b=a.config.forceEnterMode||b;if("wysiwyg"==a.mode){e||(e=a.activeEnterMode);var l=a.elementPath();l&&!l.isContextFor("p")&&(e=CKEDITOR.ENTER_BR,b=1);a.fire("saveSnapshot");e==CKEDITOR.ENTER_BR?u(a,e,null,b):r(a,e,null,b);a.fire("saveSnapshot")}}function y(a){a=a.getSelection().getRanges(!0);for(var e=a.length-1;0<e;e--)a[e].deleteContents();return a[0]}function z(a){var e=a.startContainer.getAscendant(function(a){return a.type==CKEDITOR.NODE_ELEMENT&&"true"==a.getAttribute("contenteditable")},
!0);if(a.root.equals(e))return a;e=new CKEDITOR.dom.range(e);e.moveToRange(a);return e}CKEDITOR.plugins.add("enterkey",{init:function(a){a.addCommand("enter",{modes:{wysiwyg:1},editorFocus:!1,exec:function(a){x(a)}});a.addCommand("shiftEnter",{modes:{wysiwyg:1},editorFocus:!1,exec:function(a){x(a,a.activeShiftEnterMode,1)}});a.setKeystroke([[13,"enter"],[CKEDITOR.SHIFT+13,"shiftEnter"]])}});var A=CKEDITOR.dom.walker.whitespaces(),B=CKEDITOR.dom.walker.bookmark(),v,u,r,w;CKEDITOR.plugins.enterkey=
{enterBlock:function(a,e,b,l){function n(a){var b;if(a===CKEDITOR.ENTER_BR||-1===CKEDITOR.tools.indexOf(["td","th"],p.lastElement.getName())||1!==p.lastElement.getChildCount())return!1;a=p.lastElement.getChild(0).clone(!0);(b=a.getBogus())&&b.remove();return a.getText().length?!1:!0}if(b=b||y(a)){b=z(b);var g=b.document,f=b.checkStartOfBlock(),k=b.checkEndOfBlock(),p=a.elementPath(b.startContainer),c=p.block,m=e==CKEDITOR.ENTER_DIV?"div":"p",d;if(c&&f&&k){f=c.getParent();if(f.is("li")&&1<f.getChildCount()){g=
new CKEDITOR.dom.element("li");d=a.createRange();g.insertAfter(f);c.remove();d.setStart(g,0);a.getSelection().selectRanges([d]);return}if(c.is("li")||c.getParent().is("li")){c.is("li")||(c=c.getParent(),f=c.getParent());d=f.getParent();b=!c.hasPrevious();var h=!c.hasNext();l=a.getSelection();var m=l.createBookmarks(),t=c.getDirection(1),k=c.getAttribute("class"),q=c.getAttribute("style"),r=d.getDirection(1)!=t;a=a.enterMode!=CKEDITOR.ENTER_BR||r||q||k;if(d.is("li"))b||h?(b&&h&&f.remove(),c[h?"insertAfter":
"insertBefore"](d)):c.breakParent(d);else{if(a)if(p.block.is("li")?(d=g.createElement(e==CKEDITOR.ENTER_P?"p":"div"),r&&d.setAttribute("dir",t),q&&d.setAttribute("style",q),k&&d.setAttribute("class",k),c.moveChildren(d)):d=p.block,b||h)d[b?"insertBefore":"insertAfter"](f);else c.breakParent(f),d.insertAfter(f);else if(c.appendBogus(!0),b||h)for(;g=c[b?"getFirst":"getLast"]();)g[b?"insertBefore":"insertAfter"](f);else for(c.breakParent(f);g=c.getLast();)g.insertAfter(f);c.remove()}l.selectBookmarks(m);
return}if(c&&c.getParent().is("blockquote")){c.breakParent(c.getParent());c.getPrevious().getFirst(CKEDITOR.dom.walker.invisible(1))||c.getPrevious().remove();c.getNext().getFirst(CKEDITOR.dom.walker.invisible(1))||c.getNext().remove();b.moveToElementEditStart(c);b.select();return}}else if(c&&c.is("pre")&&!k){u(a,e,b,l);return}if(q=b.splitBlock(m)){a=q.previousBlock;c=q.nextBlock;f=q.wasStartOfBlock;k=q.wasEndOfBlock;c?(h=c.getParent(),h.is("li")&&(c.breakParent(h),c.move(c.getNext(),1))):a&&(h=a.getParent())&&
h.is("li")&&(a.breakParent(h),h=a.getNext(),b.moveToElementEditStart(h),a.move(a.getPrevious()));if(f||k)if(n(e))b.moveToElementEditStart(b.getTouchedStartNode());else{if(a){if(a.is("li")||!w.test(a.getName())&&!a.is("pre"))d=a.clone()}else c&&(d=c.clone());d?l&&!d.is("li")&&d.renameNode(m):h&&h.is("li")?d=h:(d=g.createElement(m),a&&(t=a.getDirection())&&d.setAttribute("dir",t));if(g=q.elementPath)for(e=0,l=g.elements.length;e<l;e++){m=g.elements[e];if(m.equals(g.block)||m.equals(g.blockLimit))break;
CKEDITOR.dtd.$removeEmpty[m.getName()]&&(m=m.clone(),d.moveChildren(m),d.append(m))}d.appendBogus();d.getParent()||b.insertNode(d);d.is("li")&&d.removeAttribute("value");!CKEDITOR.env.ie||!f||k&&a.getChildCount()||(b.moveToElementEditStart(k?a:d),b.select());b.moveToElementEditStart(f&&!k?c:d)}else c.is("li")&&(d=b.clone(),d.selectNodeContents(c),d=new CKEDITOR.dom.walker(d),d.evaluator=function(a){return!(B(a)||A(a)||a.type==CKEDITOR.NODE_ELEMENT&&a.getName()in CKEDITOR.dtd.$inline&&!(a.getName()in
CKEDITOR.dtd.$empty))},(h=d.next())&&h.type==CKEDITOR.NODE_ELEMENT&&h.is("ul","ol")&&(CKEDITOR.env.needsBrFiller?g.createElement("br"):g.createText(" ")).insertBefore(h)),c&&b.moveToElementEditStart(c);b.select();b.scrollIntoView()}}},enterBr:function(a,e,b,l){if(b=b||y(a)){var n=b.document,g=b.checkEndOfBlock(),f=new CKEDITOR.dom.elementPath(a.getSelection().getStartElement()),k=f.block,p=k&&f.block.getName();l||"li"!=p?(!l&&g&&w.test(p)?(g=k.getDirection())?(n=n.createElement("div"),n.setAttribute("dir",
g),n.insertAfter(k),b.setStart(n,0)):(n.createElement("br").insertAfter(k),CKEDITOR.env.gecko&&n.createText("").insertAfter(k),b.setStartAt(k.getNext(),CKEDITOR.env.ie?CKEDITOR.POSITION_BEFORE_START:CKEDITOR.POSITION_AFTER_START)):(a="pre"==p&&CKEDITOR.env.ie&&8>CKEDITOR.env.version?n.createText("\r"):n.createElement("br"),b.deleteContents(),b.insertNode(a),CKEDITOR.env.needsBrFiller?(n.createText("﻿").insertAfter(a),g&&(k||f.blockLimit).appendBogus(),a.getNext().$.nodeValue="",b.setStartAt(a.getNext(),
CKEDITOR.POSITION_AFTER_START)):b.setStartAt(a,CKEDITOR.POSITION_AFTER_END)),b.collapse(!0),b.select(),b.scrollIntoView()):r(a,e,b,l)}}};v=CKEDITOR.plugins.enterkey;u=v.enterBr;r=v.enterBlock;w=/^h[1-6]$/})();(function(){function k(a,f){var g={},c=[],e={nbsp:" ",shy:"­",gt:"\x3e",lt:"\x3c",amp:"\x26",apos:"'",quot:'"'};a=a.replace(/\b(nbsp|shy|gt|lt|amp|apos|quot)(?:,|$)/g,function(a,b){var d=f?"\x26"+b+";":e[b];g[d]=f?e[b]:"\x26"+b+";";c.push(d);return""});a=a.replace(/,$/,"");if(!f&&a){a=a.split(",");var b=document.createElement("div"),d;b.innerHTML="\x26"+a.join(";\x26")+";";d=b.innerHTML;b=null;for(b=0;b<d.length;b++){var h=d.charAt(b);g[h]="\x26"+a[b]+";";c.push(h)}}g.regex=c.join(f?"|":"");return g}
CKEDITOR.plugins.add("entities",{afterInit:function(a){function f(b){return h[b]}function g(a){return"force"!=c.entities_processNumerical&&b[a]?b[a]:"\x26#"+(CKEDITOR.env.ie?a.charCodeAt(0):a.codePointAt(0))+";"}var c=a.config;if(a=(a=a.dataProcessor)&&a.htmlFilter){var e=[];!1!==c.basicEntities&&e.push("nbsp,gt,lt,amp");c.entities&&(e.length&&e.push("quot,iexcl,cent,pound,curren,yen,brvbar,sect,uml,copy,ordf,laquo,not,shy,reg,macr,deg,plusmn,sup2,sup3,acute,micro,para,middot,cedil,sup1,ordm,raquo,frac14,frac12,frac34,iquest,times,divide,fnof,bull,hellip,prime,Prime,oline,frasl,weierp,image,real,trade,alefsym,larr,uarr,rarr,darr,harr,crarr,lArr,uArr,rArr,dArr,hArr,forall,part,exist,empty,nabla,isin,notin,ni,prod,sum,minus,lowast,radic,prop,infin,ang,and,or,cap,cup,int,there4,sim,cong,asymp,ne,equiv,le,ge,sub,sup,nsub,sube,supe,oplus,otimes,perp,sdot,lceil,rceil,lfloor,rfloor,lang,rang,loz,spades,clubs,hearts,diams,circ,tilde,ensp,emsp,thinsp,zwnj,zwj,lrm,rlm,ndash,mdash,lsquo,rsquo,sbquo,ldquo,rdquo,bdquo,dagger,Dagger,permil,lsaquo,rsaquo,euro"),
c.entities_latin&&e.push("Agrave,Aacute,Acirc,Atilde,Auml,Aring,AElig,Ccedil,Egrave,Eacute,Ecirc,Euml,Igrave,Iacute,Icirc,Iuml,ETH,Ntilde,Ograve,Oacute,Ocirc,Otilde,Ouml,Oslash,Ugrave,Uacute,Ucirc,Uuml,Yacute,THORN,szlig,agrave,aacute,acirc,atilde,auml,aring,aelig,ccedil,egrave,eacute,ecirc,euml,igrave,iacute,icirc,iuml,eth,ntilde,ograve,oacute,ocirc,otilde,ouml,oslash,ugrave,uacute,ucirc,uuml,yacute,thorn,yuml,OElig,oelig,Scaron,scaron,Yuml"),c.entities_greek&&e.push("Alpha,Beta,Gamma,Delta,Epsilon,Zeta,Eta,Theta,Iota,Kappa,Lambda,Mu,Nu,Xi,Omicron,Pi,Rho,Sigma,Tau,Upsilon,Phi,Chi,Psi,Omega,alpha,beta,gamma,delta,epsilon,zeta,eta,theta,iota,kappa,lambda,mu,nu,xi,omicron,pi,rho,sigmaf,sigma,tau,upsilon,phi,chi,psi,omega,thetasym,upsih,piv"),
c.entities_additional&&e.push(c.entities_additional));var b=k(e.join(",")),d=b.regex?"["+b.regex+"]":"a^";delete b.regex;c.entities&&c.entities_processNumerical&&(d="[^ -~]|"+d);var d=new RegExp(d,CKEDITOR.env.ie?"g":"gu"),h=k("nbsp,gt,lt,amp,shy",!0),l=new RegExp(h.regex,"g");a.addRules({text:function(a){return a.replace(l,f).replace(d,g)}},{applyToAll:!0,excludeNestedEditable:!0})}}})})();CKEDITOR.config.basicEntities=!0;CKEDITOR.config.entities=!0;CKEDITOR.config.entities_latin=!0;
CKEDITOR.config.entities_greek=!0;CKEDITOR.config.entities_additional="#39";(function(){function k(a){var l=a.config,p=a.fire("uiSpace",{space:"top",html:""}).html,t=function(){function f(a,c,e){b.setStyle(c,w(e));b.setStyle("position",a)}function e(a){var b=k.getDocumentPosition();switch(a){case "top":f("absolute","top",b.y-q-r);break;case "pin":f("fixed","top",x);break;case "bottom":f("absolute","top",b.y+(c.height||c.bottom-c.top)+r)}m=a}var m,k,n,c,h,q,v,p=l.floatSpaceDockedOffsetX||0,r=l.floatSpaceDockedOffsetY||0,u=l.floatSpacePinnedOffsetX||0,x=l.floatSpacePinnedOffsetY||
0;return function(d){if(k=a.editable()){var f=d&&"focus"==d.name;f&&b.show();a.fire("floatingSpaceLayout",{show:f});b.removeStyle("left");b.removeStyle("right");n=b.getClientRect();c=k.getClientRect();h=g.getViewPaneSize();q=n.height;v="pageXOffset"in g.$?g.$.pageXOffset:CKEDITOR.document.$.documentElement.scrollLeft;m?(q+r<=c.top?e("top"):q+r>h.height-c.bottom?e("pin"):e("bottom"),d=h.width/2,d=l.floatSpacePreferRight?"right":0<c.left&&c.right<h.width&&c.width>n.width?"rtl"==l.contentsLangDirection?
"right":"left":d-c.left>c.right-d?"left":"right",n.width>h.width?(d="left",f=0):(f="left"==d?0<c.left?c.left:0:c.right<h.width?h.width-c.right:0,f+n.width>h.width&&(d="left"==d?"right":"left",f=0)),b.setStyle(d,w(("pin"==m?u:p)+f+("pin"==m?0:"left"==d?v:-v)))):(m="pin",e("pin"),t(d))}}}();if(p){var k=new CKEDITOR.template('\x3cdiv id\x3d"cke_{name}" class\x3d"cke {id} cke_reset_all cke_chrome cke_editor_{name} cke_float cke_{langDir} '+CKEDITOR.env.cssClass+'" dir\x3d"{langDir}" title\x3d"'+(CKEDITOR.env.gecko?
" ":"")+'" lang\x3d"{langCode}" role\x3d"application" style\x3d"{style}"'+(a.applicationTitle?' aria-labelledby\x3d"cke_{name}_arialbl"':" ")+"\x3e"+(a.applicationTitle?'\x3cspan id\x3d"cke_{name}_arialbl" class\x3d"cke_voice_label"\x3e{voiceLabel}\x3c/span\x3e':" ")+'\x3cdiv class\x3d"cke_inner"\x3e\x3cdiv id\x3d"{topId}" class\x3d"cke_top" role\x3d"presentation"\x3e{content}\x3c/div\x3e\x3c/div\x3e\x3c/div\x3e'),b=CKEDITOR.document.getBody().append(CKEDITOR.dom.element.createFromHtml(k.output({content:p,
id:a.id,langDir:a.lang.dir,langCode:a.langCode,name:a.name,style:"display:none;z-index:"+(l.baseFloatZIndex-1),topId:a.ui.spaceId("top"),voiceLabel:a.applicationTitle}))),u=CKEDITOR.tools.eventsBuffer(500,t),e=CKEDITOR.tools.eventsBuffer(100,t);b.unselectable();b.on("mousedown",function(a){a=a.data;a.getTarget().hasAscendant("a",1)||a.preventDefault()});a.on("focus",function(b){t(b);a.on("change",u.input);g.on("scroll",e.input);g.on("resize",e.input)});a.on("blur",function(){b.hide();a.removeListener("change",
u.input);g.removeListener("scroll",e.input);g.removeListener("resize",e.input)});a.on("destroy",function(){g.removeListener("scroll",e.input);g.removeListener("resize",e.input);b.clearCustomData();b.remove()});a.focusManager.hasFocus&&b.show();a.focusManager.add(b,1)}}var g=CKEDITOR.document.getWindow(),w=CKEDITOR.tools.cssLength;CKEDITOR.plugins.add("floatingspace",{init:function(a){a.on("loaded",function(){k(this)},null,null,20)}})})();(function(){function v(a){function e(a){var b=!1;h.attachListener(h,"keydown",function(){var d=c.getBody().getElementsByTag(a);if(!b){for(var e=0;e<d.count();e++)d.getItem(e).setCustomData("retain",!0);b=!0}},null,null,1);h.attachListener(h,"keyup",function(){var d=c.getElementsByTag(a);b&&(1==d.count()&&!d.getItem(0).getCustomData("retain")&&CKEDITOR.tools.isEmpty(d.getItem(0).getAttributes())&&d.getItem(0).remove(1),b=!1)})}var b=this.editor;if(b&&!b.isDetached()){var c=a.document,f=c.body,d=c.getElementById("cke_actscrpt");
d&&d.parentNode.removeChild(d);(d=c.getElementById("cke_shimscrpt"))&&d.parentNode.removeChild(d);(d=c.getElementById("cke_basetagscrpt"))&&d.parentNode.removeChild(d);f.contentEditable=!0;CKEDITOR.env.ie&&(f.hideFocus=!0,f.disabled=!0,f.removeAttribute("disabled"));delete this._.isLoadingData;this.$=f;c=new CKEDITOR.dom.document(c);this.setup();this.fixInitialSelection();var h=this;CKEDITOR.env.ie&&!CKEDITOR.env.edge&&c.getDocumentElement().addClass(c.$.compatMode);CKEDITOR.env.ie&&!CKEDITOR.env.edge&&
b.enterMode!=CKEDITOR.ENTER_P?e("p"):CKEDITOR.env.edge&&15>CKEDITOR.env.version&&b.enterMode!=CKEDITOR.ENTER_DIV&&e("div");if(CKEDITOR.env.webkit||CKEDITOR.env.ie&&10<CKEDITOR.env.version)c.getDocumentElement().on("mousedown",function(a){a.data.getTarget().is("html")&&setTimeout(function(){b.editable().focus()})});w(b);try{b.document.$.execCommand("2D-position",!1,!0)}catch(g){}(CKEDITOR.env.gecko||CKEDITOR.env.ie&&"CSS1Compat"==b.document.$.compatMode)&&this.attachListener(this,"keydown",function(a){var d=
a.data.getKeystroke();if(33==d||34==d)if(CKEDITOR.env.ie)setTimeout(function(){b.getSelection().scrollIntoView()},0);else if(b.window.$.innerHeight>this.$.offsetHeight){var c=b.createRange();c[33==d?"moveToElementEditStart":"moveToElementEditEnd"](this);c.select();a.data.preventDefault()}});CKEDITOR.env.ie&&this.attachListener(c,"blur",function(){try{c.$.selection.empty()}catch(a){}});CKEDITOR.env.iOS&&this.attachListener(c,"touchend",function(){a.focus()});f=b.document.getElementsByTag("title").getItem(0);
f.data("cke-title",f.getText());CKEDITOR.env.ie&&(b.document.$.title=this._.docTitle);CKEDITOR.tools.setTimeout(function(){"unloaded"==this.status&&(this.status="ready");b.fire("contentDom");this._.isPendingFocus&&(b.focus(),this._.isPendingFocus=!1);setTimeout(function(){b.fire("dataReady")},0)},0,this)}}function w(a){function e(){var d;a.editable().attachListener(a,"selectionChange",function(){var c=a.getSelection().getSelectedElement();c&&(d&&(d.detachEvent("onresizestart",b),d=null),c.$.attachEvent("onresizestart",
b),d=c.$)})}function b(a){a.returnValue=!1}if(CKEDITOR.env.gecko)try{var c=a.document.$;c.execCommand("enableObjectResizing",!1,!a.config.disableObjectResizing);c.execCommand("enableInlineTableEditing",!1,!a.config.disableNativeTableHandles)}catch(f){}else CKEDITOR.env.ie&&11>CKEDITOR.env.version&&a.config.disableObjectResizing&&e()}function x(){var a=[];if(8<=CKEDITOR.document.$.documentMode){a.push("html.CSS1Compat [contenteditable\x3dfalse]{min-height:0 !important}");var e=[],b;for(b in CKEDITOR.dtd.$removeEmpty)e.push("html.CSS1Compat "+
b+"[contenteditable\x3dfalse]");a.push(e.join(",")+"{display:inline-block}")}else CKEDITOR.env.gecko&&(a.push("html{height:100% !important}"),a.push("img:-moz-broken{-moz-force-broken-image-icon:1;min-width:24px;min-height:24px}"));a.push("html{cursor:text;*cursor:auto}");a.push("img,input,textarea{cursor:default}");return a.join("\n")}var l;CKEDITOR.plugins.add("wysiwygarea",{init:function(a){a.config.fullPage&&a.addFeature({allowedContent:"html head title; style [media,type]; body (*)[id]; meta link [*]",
requiredContent:"body"});a.addMode("wysiwyg",function(e){function b(b){b&&b.removeListener();if(!a.isDestroyed()&&!a.isDetached()&&(a.editable(new l(a,g.getFrameDocument().getBody())),a.setData(a.getData(1),e),t)){if(u)a.on("mode",c,{iframe:g,editor:a,callback:e});a.on("mode",function(){a.status="ready"});f()}}function c(a){a&&a.removeListener();g.on("load",function(){p&&(p=!1,d())})}function f(){m=new MutationObserver(function(b){for(var c=0;c<b.length;c++){var e=b[c];if("childList"===e.type&&0!==
e.addedNodes.length)for(var f=0;f<e.addedNodes.length;f++){var g=e.addedNodes[f];g.contains&&g.contains(a.container.$)&&(u?p=!0:d())}}});m.observe(a.config.observableParent,{childList:!0,subtree:!0})}function d(){var b=a.getData(!1),c;a.editable().preserveIframe=!0;a.editable(null);c=new l(a,g.getFrameDocument().getBody());a.editable(c);a.status="recreating";a.setData(b,{callback:e,internal:!1,noSnapshot:!1})}var h="document.open();"+(CKEDITOR.env.ie?"("+CKEDITOR.tools.fixDomain+")();":"")+"document.close();",
h=CKEDITOR.env.air?"javascript:void(0)":CKEDITOR.env.ie&&!CKEDITOR.env.edge?"javascript:void(function(){"+encodeURIComponent(h)+"}())":"",g=CKEDITOR.dom.element.createFromHtml('\x3ciframe src\x3d"'+h+'" frameBorder\x3d"0"\x3e\x3c/iframe\x3e');g.setStyles({width:"100%",height:"100%"});g.addClass("cke_wysiwyg_frame").addClass("cke_reset");h=a.ui.space("contents");h.append(g);var r=CKEDITOR.env.ie&&!CKEDITOR.env.edge||CKEDITOR.env.gecko;if(r)g.on("load",b);var k=a.title,n=a.fire("ariaEditorHelpLabel",
{}).label,p=!1,u=CKEDITOR.env.ie&&11===CKEDITOR.env.version,t=!!window.MutationObserver,m;k&&(CKEDITOR.env.ie&&n&&(k+=", "+n),g.setAttribute("title",k));if(n){var k=CKEDITOR.tools.getNextId(),q=CKEDITOR.dom.element.createFromHtml('\x3cspan id\x3d"'+k+'" class\x3d"cke_voice_label"\x3e'+n+"\x3c/span\x3e");h.append(q,1);g.setAttribute("aria-describedby",k)}a.on("beforeModeUnload",function(a){a.removeListener();q&&q.remove();t&&m.disconnect()});a.on("destroy",function(){m&&m.disconnect()});g.setAttributes({tabIndex:a.tabIndex,
allowTransparency:"true"});!r&&b();a.fire("ariaWidget",g)})}});CKEDITOR.editor.prototype.addContentsCss=function(a){var e=this.config,b=e.contentsCss;CKEDITOR.tools.isArray(b)||(e.contentsCss=b?[b]:[]);e.contentsCss.push(a)};l=CKEDITOR.tools.createClass({$:function(){this.base.apply(this,arguments);this._.frameLoadedHandler=CKEDITOR.tools.addFunction(function(a){CKEDITOR.tools.setTimeout(v,0,this,a)},this);this._.docTitle=this.getWindow().getFrame().getAttribute("title")||" "},base:CKEDITOR.editable,
proto:{preserveIframe:!1,setData:function(a,e){var b=this.editor;if(e)this.setHtml(a),this.fixInitialSelection(),b.fire("dataReady");else{this._.isLoadingData=!0;b._.dataStore={id:1};var c=b.config,f=c.fullPage,d=c.docType,h=CKEDITOR.tools.buildStyleHtml(x()).replace(/<style>/,'\x3cstyle data-cke-temp\x3d"1"\x3e');f||(h+=CKEDITOR.tools.buildStyleHtml(b.config.contentsCss));var g=c.baseHref?'\x3cbase href\x3d"'+c.baseHref+'" data-cke-temp\x3d"1" /\x3e':"";f&&(a=a.replace(/<!DOCTYPE[^>]*>/i,function(a){b.docType=
d=a;return""}).replace(/<\?xml\s[^\?]*\?>/i,function(a){b.xmlDeclaration=a;return""}));a=b.dataProcessor.toHtml(a);f?(/<body[\s|>]/.test(a)||(a="\x3cbody\x3e"+a),/<html[\s|>]/.test(a)||(a="\x3chtml\x3e"+a+"\x3c/html\x3e"),/<head[\s|>]/.test(a)?/<title[\s|>]/.test(a)||(a=a.replace(/<head[^>]*>/,"$\x26\x3ctitle\x3e\x3c/title\x3e")):a=a.replace(/<html[^>]*>/,"$\x26\x3chead\x3e\x3ctitle\x3e\x3c/title\x3e\x3c/head\x3e"),g&&(a=a.replace(/<head[^>]*?>/,"$\x26"+g)),a=a.replace(/<\/head\s*>/,h+"$\x26"),a=
d+a):a=c.docType+'\x3chtml dir\x3d"'+c.contentsLangDirection+'" lang\x3d"'+(c.contentsLanguage||b.langCode)+'"\x3e\x3chead\x3e\x3ctitle\x3e'+this._.docTitle+"\x3c/title\x3e"+g+h+"\x3c/head\x3e\x3cbody"+(c.bodyId?' id\x3d"'+c.bodyId+'"':"")+(c.bodyClass?' class\x3d"'+c.bodyClass+'"':"")+"\x3e"+a+"\x3c/body\x3e\x3c/html\x3e";CKEDITOR.env.gecko&&(a=a.replace(/<body/,'\x3cbody contenteditable\x3d"true" '),2E4>CKEDITOR.env.version&&(a=a.replace(/<body[^>]*>/,"$\x26\x3c!-- cke-content-start --\x3e")));
a=a.replace(/<body/,'\x3cbody role\x3d"textbox" aria-multiline\x3d"true"');b.title&&(a=a.replace(/<body/,'\x3cbody aria-label\x3d"'+CKEDITOR.tools.htmlEncodeAttr(b.title)+'"'));CKEDITOR.env.gecko||(a=a.replace("\x3cbody",'\x3cbody tabindex\x3d"0" '));c='\x3cscript id\x3d"cke_actscrpt" type\x3d"text/javascript"'+(CKEDITOR.env.ie?' defer\x3d"defer" ':"")+"\x3evar wasLoaded\x3d0;function onload(){if(!wasLoaded)window.parent.CKEDITOR \x26\x26 window.parent.CKEDITOR.tools.callFunction("+this._.frameLoadedHandler+
",window);wasLoaded\x3d1;}"+(CKEDITOR.env.ie?"onload();":'document.addEventListener("DOMContentLoaded", onload, false );')+"\x3c/script\x3e";CKEDITOR.env.ie&&9>CKEDITOR.env.version&&(c+='\x3cscript id\x3d"cke_shimscrpt"\x3ewindow.parent.CKEDITOR.tools.enableHtml5Elements(document)\x3c/script\x3e');g&&CKEDITOR.env.ie&&10>CKEDITOR.env.version&&(c+='\x3cscript id\x3d"cke_basetagscrpt"\x3evar baseTag \x3d document.querySelector( "base" );baseTag.href \x3d baseTag.href;\x3c/script\x3e');a=a.replace(/(?=\s*<\/(:?head)>)/,
c);this.clearCustomData();this.clearListeners();b.fire("contentDomUnload");var l=this.getDocument();try{l.write(a)}catch(k){setTimeout(function(){l.write(a)},0)}}},getData:function(a){if(a)return this.getHtml();a=this.editor;var e=a.config,b=e.fullPage,c=b&&a.docType,f=b&&a.xmlDeclaration,d=this.getDocument(),d=b?d.getDocumentElement().getOuterHtml():d.getBody().getHtml();CKEDITOR.env.gecko&&e.enterMode!=CKEDITOR.ENTER_BR&&(d=d.replace(/<br>(?=\s*(:?$|<\/body>))/,""));b&&(d=d.replace(/<body(.*?)role="?textbox"?/i,
"\x3cbody$1").replace(/<body(.*?)aria-multiline="?true"?/i,"\x3cbody$1").replace(/<body(.*?)tabindex="?0"?/i,"\x3cbody$1").replace(/<body(.*?)aria-label="(.+?)"/i,"\x3cbody$1").replace(/<body(.*?)aria-readonly="?(?:true|false)"?/i,"\x3cbody$1"));d=a.dataProcessor.toDataFormat(d);f&&(d=f+"\n"+d);c&&(d=c+"\n"+d);return d},focus:function(){this._.isLoadingData?this._.isPendingFocus=!0:l.baseProto.focus.call(this)},detach:function(){if(!this.preserveIframe){var a=this.editor,e=a.document,a=a.container.findOne("iframe.cke_wysiwyg_frame");
l.baseProto.detach.call(this);this.clearCustomData(this._.expandoNumber);e.getDocumentElement().clearCustomData();CKEDITOR.tools.removeFunction(this._.frameLoadedHandler);a&&(a.clearCustomData(),(e=a.removeCustomData("onResize"))&&e.removeListener(),a.isDetached()||a.remove())}}}})})();CKEDITOR.config.disableObjectResizing=!1;CKEDITOR.config.disableNativeTableHandles=!0;CKEDITOR.config.disableNativeSpellChecker=!0;CKEDITOR.config.observableParent=CKEDITOR.document.$;(function(){function m(a,b){var e,f;b.on("refresh",function(a){var b=[k],c;for(c in a.data.states)b.push(a.data.states[c]);this.setState(CKEDITOR.tools.search(b,p)?p:k)},b,null,100);b.on("exec",function(b){e=a.getSelection();f=e.createBookmarks(1);b.data||(b.data={});b.data.done=!1},b,null,0);b.on("exec",function(){a.forceNextSelectionCheck();e.selectBookmarks(f)},b,null,100)}var k=CKEDITOR.TRISTATE_DISABLED,p=CKEDITOR.TRISTATE_OFF;CKEDITOR.plugins.add("indent",{init:function(a){var b=CKEDITOR.plugins.indent.genericDefinition;
m(a,a.addCommand("indent",new b(!0)));m(a,a.addCommand("outdent",new b));a.ui.addButton&&(a.ui.addButton("Indent",{label:a.lang.indent.indent,command:"indent",directional:!0,toolbar:"indent,20"}),a.ui.addButton("Outdent",{label:a.lang.indent.outdent,command:"outdent",directional:!0,toolbar:"indent,10"}));a.on("dirChanged",function(b){var f=a.createRange(),l=b.data.node;f.setStartBefore(l);f.setEndAfter(l);for(var n=new CKEDITOR.dom.walker(f),c;c=n.next();)if(c.type==CKEDITOR.NODE_ELEMENT)if(!c.equals(l)&&
c.getDirection())f.setStartAfter(c),n=new CKEDITOR.dom.walker(f);else{var d=a.config.indentClasses;if(d)for(var g="ltr"==b.data.dir?["_rtl",""]:["","_rtl"],h=0;h<d.length;h++)c.hasClass(d[h]+g[0])&&(c.removeClass(d[h]+g[0]),c.addClass(d[h]+g[1]));d=c.getStyle("margin-right");g=c.getStyle("margin-left");d?c.setStyle("margin-left",d):c.removeStyle("margin-left");g?c.setStyle("margin-right",g):c.removeStyle("margin-right")}})}});CKEDITOR.plugins.indent={genericDefinition:function(a){this.isIndent=!!a;
this.startDisabled=!this.isIndent},specificDefinition:function(a,b,e){this.name=b;this.editor=a;this.jobs={};this.enterBr=a.config.enterMode==CKEDITOR.ENTER_BR;this.isIndent=!!e;this.relatedGlobal=e?"indent":"outdent";this.indentKey=e?9:CKEDITOR.SHIFT+9;this.database={}},registerCommands:function(a,b){a.on("pluginsLoaded",function(){for(var a in b)(function(a,b){var e=a.getCommand(b.relatedGlobal),c;for(c in b.jobs)e.on("exec",function(d){d.data.done||(a.fire("lockSnapshot"),b.execJob(a,c)&&(d.data.done=
!0),a.fire("unlockSnapshot"),CKEDITOR.dom.element.clearAllMarkers(b.database))},this,null,c),e.on("refresh",function(d){d.data.states||(d.data.states={});d.data.states[b.name+"@"+c]=b.refreshJob(a,c,d.data.path)},this,null,c);a.addFeature(b)})(this,b[a])})}};CKEDITOR.plugins.indent.genericDefinition.prototype={context:"p",exec:function(){}};CKEDITOR.plugins.indent.specificDefinition.prototype={execJob:function(a,b){var e=this.jobs[b];if(e.state!=k)return e.exec.call(this,a)},refreshJob:function(a,
b,e){b=this.jobs[b];a.activeFilter.checkFeature(this)?b.state=b.refresh.call(this,a,e):b.state=k;return b.state},getContext:function(a){return a.contains(this.context)}}})();(function(){function w(f){function g(b){for(var e=c.startContainer,a=c.endContainer;e&&!e.getParent().equals(b);)e=e.getParent();for(;a&&!a.getParent().equals(b);)a=a.getParent();if(!e||!a)return!1;for(var d=[],h=!1;!h;)e.equals(a)&&(h=!0),d.push(e),e=e.getNext();if(1>d.length)return!1;e=b.getParents(!0);for(a=0;a<e.length;a++)if(e[a].getName&&p[e[a].getName()]){b=e[a];break}for(var e=k.isIndent?1:-1,a=d[0],d=d[d.length-1],h=CKEDITOR.plugins.list.listToArray(b,q),m=h[d.getCustomData("listarray_index")].indent,
a=a.getCustomData("listarray_index");a<=d.getCustomData("listarray_index");a++)if(h[a].indent+=e,0<e){for(var g=h[a].parent,n=a-1;0<=n;n--)if(h[n].indent===e){g=h[n].parent;break}h[a].parent=new CKEDITOR.dom.element(g.getName(),g.getDocument())}for(a=d.getCustomData("listarray_index")+1;a<h.length&&h[a].indent>m;a++)h[a].indent+=e;e=CKEDITOR.plugins.list.arrayToList(h,q,null,f.config.enterMode,b.getDirection());if(!k.isIndent){var t;if((t=b.getParent())&&t.is("li"))for(var d=e.listNode.getChildren(),
r=[],l,a=d.count()-1;0<=a;a--)(l=d.getItem(a))&&l.is&&l.is("li")&&r.push(l)}e&&e.listNode.replace(b);if(r&&r.length)for(a=0;a<r.length;a++){for(l=b=r[a];(l=l.getNext())&&l.is&&l.getName()in p;)CKEDITOR.env.needsNbspFiller&&!b.getFirst(x)&&b.append(c.document.createText(" ")),b.append(l);b.insertAfter(t)}e&&f.fire("contentDomInvalidated");return!0}for(var k=this,q=this.database,p=this.context,c,m=f.getSelection(),m=(m&&m.getRanges()).createIterator();c=m.getNextRange();){for(var b=c.getCommonAncestor();b&&
(b.type!=CKEDITOR.NODE_ELEMENT||!p[b.getName()]);){if(f.editable().equals(b)){b=!1;break}b=b.getParent()}b||(b=c.startPath().contains(p))&&c.setEndAt(b,CKEDITOR.POSITION_BEFORE_END);if(!b){var d=c.getEnclosedNode();d&&d.type==CKEDITOR.NODE_ELEMENT&&d.getName()in p&&(c.setStartAt(d,CKEDITOR.POSITION_AFTER_START),c.setEndAt(d,CKEDITOR.POSITION_BEFORE_END),b=d)}b&&c.startContainer.type==CKEDITOR.NODE_ELEMENT&&c.startContainer.getName()in p&&(d=new CKEDITOR.dom.walker(c),d.evaluator=n,c.startContainer=
d.next());b&&c.endContainer.type==CKEDITOR.NODE_ELEMENT&&c.endContainer.getName()in p&&(d=new CKEDITOR.dom.walker(c),d.evaluator=n,c.endContainer=d.previous());if(b)return g(b)}return 0}function n(f){return f.type==CKEDITOR.NODE_ELEMENT&&f.is("li")}function x(f){return y(f)&&z(f)}var y=CKEDITOR.dom.walker.whitespaces(!0),z=CKEDITOR.dom.walker.bookmark(!1,!0),u=CKEDITOR.TRISTATE_DISABLED,v=CKEDITOR.TRISTATE_OFF;CKEDITOR.plugins.add("indentlist",{requires:"indent",init:function(f){function g(f){k.specificDefinition.apply(this,
arguments);this.requiredContent=["ul","ol"];f.on("key",function(g){var c=f.elementPath();if("wysiwyg"==f.mode&&g.data.keyCode==this.indentKey&&c){var m=this.getContext(c);!m||this.isIndent&&CKEDITOR.plugins.indentList.firstItemInPath(this.context,c,m)||(f.execCommand(this.relatedGlobal),g.cancel())}},this);this.jobs[this.isIndent?10:30]={refresh:this.isIndent?function(f,c){var g=this.getContext(c),b=CKEDITOR.plugins.indentList.firstItemInPath(this.context,c,g);return g&&this.isIndent&&!b?v:u}:function(f,
c){return!this.getContext(c)||this.isIndent?u:v},exec:CKEDITOR.tools.bind(w,this)}}var k=CKEDITOR.plugins.indent;k.registerCommands(f,{indentlist:new g(f,"indentlist",!0),outdentlist:new g(f,"outdentlist")});CKEDITOR.tools.extend(g.prototype,k.specificDefinition.prototype,{context:{ol:1,ul:1}})}});CKEDITOR.plugins.indentList={};CKEDITOR.plugins.indentList.firstItemInPath=function(f,g,k){var q=g.contains(n);k||(k=g.contains(f));return k&&q&&q.equals(k.getFirst(n))}})();(function(){function K(a,l,d,f){for(var e=CKEDITOR.plugins.list.listToArray(l.root,d),g=[],b=0;b<l.contents.length;b++){var h=l.contents[b];(h=h.getAscendant("li",!0))&&!h.getCustomData("list_item_processed")&&(g.push(h),CKEDITOR.dom.element.setMarker(d,h,"list_item_processed",!0))}for(var h=l.root.getDocument(),k,c,b=0;b<g.length;b++){var p=g[b].getCustomData("listarray_index");k=e[p].parent;k.is(this.type)||(c=h.createElement(this.type),k.copyAttributes(c,{start:1,type:1}),c.removeStyle("list-style-type"),
e[p].parent=c)}d=CKEDITOR.plugins.list.arrayToList(e,d,null,a.config.enterMode);for(var m,e=d.listNode.getChildCount(),b=0;b<e&&(m=d.listNode.getChild(b));b++)m.getName()==this.type&&f.push(m);d.listNode.replace(l.root);a.fire("contentDomInvalidated")}function L(a,l,d){var f=l.contents,e=l.root.getDocument(),g=[];if(1==f.length&&f[0].equals(l.root)){var b=e.createElement("div");f[0].moveChildren&&f[0].moveChildren(b);f[0].append(b);f[0]=b}l=l.contents[0].getParent();for(b=0;b<f.length;b++)l=l.getCommonAncestor(f[b].getParent());
a=a.config.useComputedState;for(var h,k,b=0;b<f.length;b++)for(var c=f[b],p;p=c.getParent();){if(p.equals(l)){g.push(c);!k&&c.getDirection()&&(k=1);c=c.getDirection(a);null!==h&&(h=h&&h!=c?null:c);break}c=p}if(!(1>g.length)){f=g[g.length-1].getNext();b=e.createElement(this.type);for(d.push(b);g.length;)d=g.shift(),a=e.createElement("li"),c=d,c.is("pre")||M.test(c.getName())||"false"==c.getAttribute("contenteditable")?d.appendTo(a):(d.copyAttributes(a),h&&d.getDirection()&&(a.removeStyle("direction"),
a.removeAttribute("dir")),d.moveChildren(a),d.remove()),a.appendTo(b);h&&k&&b.setAttribute("dir",h);f?b.insertBefore(f):b.appendTo(l)}}function N(a,l,d){function f(b){if(!(!(c=k[b?"getFirst":"getLast"]())||c.is&&c.isBlockBoundary()||!(p=l.root[b?"getPrevious":"getNext"](CKEDITOR.dom.walker.invisible(!0)))||p.is&&p.isBlockBoundary({br:1})))a.document.createElement("br")[b?"insertBefore":"insertAfter"](c)}for(var e=CKEDITOR.plugins.list.listToArray(l.root,d),g=[],b=0;b<l.contents.length;b++){var h=
l.contents[b];(h=h.getAscendant("li",!0))&&!h.getCustomData("list_item_processed")&&(g.push(h),CKEDITOR.dom.element.setMarker(d,h,"list_item_processed",!0))}h=null;for(b=0;b<g.length;b++)h=g[b].getCustomData("listarray_index"),e[h].indent=-1;for(b=h+1;b<e.length;b++)if(e[b].indent>e[b-1].indent+1){g=e[b-1].indent+1-e[b].indent;for(h=e[b].indent;e[b]&&e[b].indent>=h;)e[b].indent+=g,b++;b--}var k=CKEDITOR.plugins.list.arrayToList(e,d,null,a.config.enterMode,l.root.getAttribute("dir")).listNode,c,p;
f(!0);f();k.replace(l.root);a.fire("contentDomInvalidated")}function C(a,l){this.name=a;this.context=this.type=l;this.allowedContent=l+" li";this.requiredContent=l}function F(a,l,d,f){for(var e,g;e=a[f?"getLast":"getFirst"](O);)(g=e.getDirection(1))!==l.getDirection(1)&&e.setAttribute("dir",g),e.remove(),d?e[f?"insertBefore":"insertAfter"](d):l.append(e,f),d=e}function G(a){function l(d){var f=a[d?"getPrevious":"getNext"](t);f&&f.type==CKEDITOR.NODE_ELEMENT&&f.is(a.getName())&&(F(a,f,null,!d),a.remove(),
a=f)}l();l(1)}function H(a){return a.type==CKEDITOR.NODE_ELEMENT&&(a.getName()in CKEDITOR.dtd.$block||a.getName()in CKEDITOR.dtd.$listItem)&&CKEDITOR.dtd[a.getName()]["#"]}function D(a,l,d){a.fire("saveSnapshot");d.enlarge(CKEDITOR.ENLARGE_LIST_ITEM_CONTENTS);var f=d.extractContents();l.trim(!1,!0);var e=l.createBookmark(),g=new CKEDITOR.dom.elementPath(l.startContainer),b=g.block,g=g.lastElement.getAscendant("li",1)||b,h=new CKEDITOR.dom.elementPath(d.startContainer),k=h.contains(CKEDITOR.dtd.$listItem),
h=h.contains(CKEDITOR.dtd.$list);b?(b=b.getBogus())&&b.remove():h&&(b=h.getPrevious(t))&&z(b)&&b.remove();(b=f.getLast())&&b.type==CKEDITOR.NODE_ELEMENT&&b.is("br")&&b.remove();(b=l.startContainer.getChild(l.startOffset))?f.insertBefore(b):l.startContainer.append(f);k&&(f=A(k))&&(g.contains(k)?(F(f,k.getParent(),k),f.remove()):g.append(f));for(;d.checkStartOfBlock()&&d.checkEndOfBlock();){h=d.startPath();f=h.block;if(!f)break;f.is("li")&&(g=f.getParent(),f.equals(g.getLast(t))&&f.equals(g.getFirst(t))&&
(f=g));d.moveToPosition(f,CKEDITOR.POSITION_BEFORE_START);f.remove()}d=d.clone();f=a.editable();d.setEndAt(f,CKEDITOR.POSITION_BEFORE_END);d=new CKEDITOR.dom.walker(d);d.evaluator=function(a){return t(a)&&!z(a)};(d=d.next())&&d.type==CKEDITOR.NODE_ELEMENT&&d.getName()in CKEDITOR.dtd.$list&&G(d);l.moveToBookmark(e);l.select();a.fire("saveSnapshot")}function A(a){return(a=a.getLast(t))&&a.type==CKEDITOR.NODE_ELEMENT&&a.getName()in u?a:null}var u={ol:1,ul:1},P=CKEDITOR.dom.walker.whitespaces(),I=CKEDITOR.dom.walker.bookmark(),
t=function(a){return!(P(a)||I(a))},z=CKEDITOR.dom.walker.bogus();CKEDITOR.plugins.list={listToArray:function(a,l,d,f,e){if(!u[a.getName()])return[];f||(f=0);d||(d=[]);for(var g=0,b=a.getChildCount();g<b;g++){var h=a.getChild(g);h.type==CKEDITOR.NODE_ELEMENT&&h.getName()in CKEDITOR.dtd.$list&&CKEDITOR.plugins.list.listToArray(h,l,d,f+1);if("li"==h.$.nodeName.toLowerCase()){var k={parent:a,indent:f,element:h,contents:[]};e?k.grandparent=e:(k.grandparent=a.getParent(),k.grandparent&&"li"==k.grandparent.$.nodeName.toLowerCase()&&
(k.grandparent=k.grandparent.getParent()));l&&CKEDITOR.dom.element.setMarker(l,h,"listarray_index",d.length);d.push(k);for(var c=0,p=h.getChildCount(),m;c<p;c++)m=h.getChild(c),m.type==CKEDITOR.NODE_ELEMENT&&u[m.getName()]?CKEDITOR.plugins.list.listToArray(m,l,d,f+1,k.grandparent):k.contents.push(m)}}return d},arrayToList:function(a,l,d,f,e){d||(d=0);if(!a||a.length<d+1)return null;for(var g,b=a[d].parent.getDocument(),h=new CKEDITOR.dom.documentFragment(b),k=null,c=d,p=Math.max(a[d].indent,0),m=
null,n,r,y=f==CKEDITOR.ENTER_P?"p":"div";;){var q=a[c];g=q.grandparent;n=q.element.getDirection(1);if(q.indent==p){k&&a[c].parent.getName()==k.getName()||(k=a[c].parent.clone(!1,1),e&&k.setAttribute("dir",e),h.append(k));m=k.append(q.element.clone(0,1));n!=k.getDirection(1)&&m.setAttribute("dir",n);for(g=0;g<q.contents.length;g++)m.append(q.contents[g].clone(1,1));c++}else if(q.indent==Math.max(p,0)+1)q=a[c-1].element.getDirection(1),c=CKEDITOR.plugins.list.arrayToList(a,null,c,f,q!=n?n:null),!m.getChildCount()&&
CKEDITOR.env.needsNbspFiller&&7>=b.$.documentMode&&m.append(b.createText(" ")),m.append(c.listNode),c=c.nextIndex;else if(-1==q.indent&&!d&&g){u[g.getName()]?(m=q.element.clone(!1,!0),n!=g.getDirection(1)&&m.setAttribute("dir",n)):m=new CKEDITOR.dom.documentFragment(b);var k=g.getDirection(1)!=n,w=q.element,B=w.getAttribute("class"),E=w.getAttribute("style"),J=m.type==CKEDITOR.NODE_DOCUMENT_FRAGMENT&&(f!=CKEDITOR.ENTER_BR||k||E||B),v,z=q.contents.length,x;for(g=0;g<z;g++)if(v=q.contents[g],I(v)&&
1<z)J?x=v.clone(1,1):m.append(v.clone(1,1));else if(v.type==CKEDITOR.NODE_ELEMENT&&v.isBlockBoundary()){k&&!v.getDirection()&&v.setAttribute("dir",n);r=v;var A=w.getAttribute("style");A&&r.setAttribute("style",A.replace(/([^;])$/,"$1;")+(r.getAttribute("style")||""));B&&v.addClass(B);r=null;x&&(m.append(x),x=null);m.append(v.clone(1,1))}else J?(r||(r=b.createElement(y),m.append(r),k&&r.setAttribute("dir",n)),E&&r.setAttribute("style",E),B&&r.setAttribute("class",B),x&&(r.append(x),x=null),r.append(v.clone(1,
1))):m.append(v.clone(1,1));x&&((r||m).append(x),x=null);m.type==CKEDITOR.NODE_DOCUMENT_FRAGMENT&&c!=a.length-1&&(CKEDITOR.env.needsBrFiller&&(n=m.getLast())&&n.type==CKEDITOR.NODE_ELEMENT&&n.is("br")&&n.remove(),(n=m.getLast(t))&&n.type==CKEDITOR.NODE_ELEMENT&&n.is(CKEDITOR.dtd.$block)||m.append(b.createElement("br")));n=m.$.nodeName.toLowerCase();"div"!=n&&"p"!=n||m.appendBogus();h.append(m);k=null;c++}else return null;r=null;if(a.length<=c||Math.max(a[c].indent,0)<p)break}if(l)for(a=h.getFirst();a;){if(a.type==
CKEDITOR.NODE_ELEMENT&&(CKEDITOR.dom.element.clearMarkers(l,a),a.getName()in CKEDITOR.dtd.$listItem&&(d=a,b=e=f=void 0,f=d.getDirection()))){for(e=d.getParent();e&&!(b=e.getDirection());)e=e.getParent();f==b&&d.removeAttribute("dir")}a=a.getNextSourceNode()}return{listNode:h,nextIndex:c}}};var M=/^h[1-6]$/,O=CKEDITOR.dom.walker.nodeType(CKEDITOR.NODE_ELEMENT);C.prototype={exec:function(a){function l(a){return u[a.root.getName()]&&!d(a.root,[CKEDITOR.NODE_COMMENT])}function d(a,b){return CKEDITOR.tools.array.filter(a.getChildren().toArray(),
function(a){return-1===CKEDITOR.tools.array.indexOf(b,a.type)}).length}function f(a){var b=!0;if(0===a.getChildCount())return!1;a.forEach(function(a){if(a.type!==CKEDITOR.NODE_COMMENT)return b=!1},null,!0);return b}this.refresh(a,a.elementPath());var e=a.config,g=a.getSelection(),b=g&&g.getRanges();if(this.state==CKEDITOR.TRISTATE_OFF){var h=a.editable();if(h.getFirst(t)){var k=1==b.length&&b[0];(e=k&&k.getEnclosedNode())&&e.is&&this.type==e.getName()&&this.setState(CKEDITOR.TRISTATE_ON)}else e.enterMode==
CKEDITOR.ENTER_BR?h.appendBogus():b[0].fixBlock(1,e.enterMode==CKEDITOR.ENTER_P?"p":"div"),g.selectRanges(b)}for(var e=g.createBookmarks(!0),h=[],c={},b=b.createIterator(),p=0;(k=b.getNextRange())&&++p;){var m=k.getBoundaryNodes(),n=m.startNode,r=m.endNode;n.type==CKEDITOR.NODE_ELEMENT&&"td"==n.getName()&&k.setStartAt(m.startNode,CKEDITOR.POSITION_AFTER_START);r.type==CKEDITOR.NODE_ELEMENT&&"td"==r.getName()&&k.setEndAt(m.endNode,CKEDITOR.POSITION_BEFORE_END);k=k.createIterator();for(k.forceBrBreak=
this.state==CKEDITOR.TRISTATE_OFF;m=k.getNextParagraph();)if(!m.getCustomData("list_block")&&!f(m)){CKEDITOR.dom.element.setMarker(c,m,"list_block",1);for(var y=a.elementPath(m),n=y.elements,r=0,y=y.blockLimit,q,w=n.length-1;0<=w&&(q=n[w]);w--)if(u[q.getName()]&&y.contains(q)){y.removeCustomData("list_group_object_"+p);(n=q.getCustomData("list_group_object"))?n.contents.push(m):(n={root:q,contents:[m]},h.push(n),CKEDITOR.dom.element.setMarker(c,q,"list_group_object",n));r=1;break}r||(r=y,r.getCustomData("list_group_object_"+
p)?r.getCustomData("list_group_object_"+p).contents.push(m):(n={root:r,contents:[m]},CKEDITOR.dom.element.setMarker(c,r,"list_group_object_"+p,n),h.push(n)))}}for(q=[];0<h.length;)n=h.shift(),this.state==CKEDITOR.TRISTATE_OFF?l(n)||(u[n.root.getName()]?K.call(this,a,n,c,q):L.call(this,a,n,q)):this.state==CKEDITOR.TRISTATE_ON&&u[n.root.getName()]&&!l(n)&&N.call(this,a,n,c);for(w=0;w<q.length;w++)G(q[w]);CKEDITOR.dom.element.clearAllMarkers(c);g.selectBookmarks(e);a.focus()},refresh:function(a,l){var d=
l.contains(u,1),f=l.blockLimit||l.root;d&&f.contains(d)?this.setState(d.is(this.type)?CKEDITOR.TRISTATE_ON:CKEDITOR.TRISTATE_OFF):this.setState(CKEDITOR.TRISTATE_OFF)}};CKEDITOR.plugins.add("list",{requires:"indentlist",init:function(a){a.blockless||(a.addCommand("numberedlist",new C("numberedlist","ol")),a.addCommand("bulletedlist",new C("bulletedlist","ul")),a.ui.addButton&&(a.ui.addButton("NumberedList",{isToggle:!0,label:a.lang.list.numberedlist,command:"numberedlist",directional:!0,toolbar:"list,10"}),
a.ui.addButton("BulletedList",{isToggle:!0,label:a.lang.list.bulletedlist,command:"bulletedlist",directional:!0,toolbar:"list,20"})),a.on("key",function(l){var d=l.data.domEvent.getKey(),f;if("wysiwyg"==a.mode&&d in{8:1,46:1}){var e=a.getSelection().getRanges()[0],g=e&&e.startPath();if(e&&e.collapsed){var b=8==d,h=a.editable(),k=new CKEDITOR.dom.walker(e.clone());k.evaluator=function(a){return t(a)&&!z(a)};k.guard=function(a,b){return!(b&&a.type==CKEDITOR.NODE_ELEMENT&&a.is("table"))};d=e.clone();
if(b){var c;(c=g.contains(u))&&e.checkBoundaryOfElement(c,CKEDITOR.START)&&(c=c.getParent())&&c.is("li")&&(c=A(c))?(f=c,c=c.getPrevious(t),d.moveToPosition(c&&z(c)?c:f,CKEDITOR.POSITION_BEFORE_START)):(k.range.setStartAt(h,CKEDITOR.POSITION_AFTER_START),k.range.setEnd(e.startContainer,e.startOffset),(c=k.previous())&&c.type==CKEDITOR.NODE_ELEMENT&&(c.getName()in u||c.is("li"))&&(c.is("li")||(k.range.selectNodeContents(c),k.reset(),k.evaluator=H,c=k.previous()),f=c,d.moveToElementEditEnd(f),d.moveToPosition(d.endPath().block,
CKEDITOR.POSITION_BEFORE_END)));if(f)D(a,d,e),l.cancel();else{var p=g.contains(u);p&&e.checkBoundaryOfElement(p,CKEDITOR.START)&&(f=p.getFirst(t),e.checkBoundaryOfElement(f,CKEDITOR.START)&&(c=p.getPrevious(t),A(f)?c&&(e.moveToElementEditEnd(c),e.select()):a.execCommand("outdent"),l.cancel()))}}else if(f=g.contains("li")){if(k.range.setEndAt(h,CKEDITOR.POSITION_BEFORE_END),b=(h=f.getLast(t))&&H(h)?h:f,g=0,(c=k.next())&&c.type==CKEDITOR.NODE_ELEMENT&&c.getName()in u&&c.equals(h)?(g=1,c=k.next()):e.checkBoundaryOfElement(b,
CKEDITOR.END)&&(g=2),g&&c){e=e.clone();e.moveToElementEditStart(c);if(1==g&&(d.optimize(),!d.startContainer.equals(f))){for(f=d.startContainer;f.is(CKEDITOR.dtd.$inline);)p=f,f=f.getParent();p&&d.moveToPosition(p,CKEDITOR.POSITION_AFTER_END)}2==g&&(d.moveToPosition(d.endPath().block,CKEDITOR.POSITION_BEFORE_END),e.endPath().block&&e.moveToPosition(e.endPath().block,CKEDITOR.POSITION_AFTER_START));D(a,d,e);l.cancel()}}else k.range.setEndAt(h,CKEDITOR.POSITION_BEFORE_END),(c=k.next())&&c.type==CKEDITOR.NODE_ELEMENT&&
c.is(u)&&(c=c.getFirst(t),g.block&&e.checkStartOfBlock()&&e.checkEndOfBlock()?(g.block.remove(),e.moveToElementEditStart(c),e.select()):A(c)?(e.moveToElementEditStart(c),e.select()):(e=e.clone(),e.moveToElementEditStart(c),D(a,d,e)),l.cancel());setTimeout(function(){a.selectionChange(1)})}}}))}})})();(function(){function n(a,b){return CKEDITOR.tools.array.reduce(b,function(a,b){return b(a)},a)}var g=[CKEDITOR.CTRL+90,CKEDITOR.CTRL+89,CKEDITOR.CTRL+CKEDITOR.SHIFT+90],p={8:1,46:1};CKEDITOR.plugins.add("undo",{init:function(a){function b(a){d.enabled&&!1!==a.data.command.canUndo&&d.save()}function c(){d.enabled=a.readOnly?!1:"wysiwyg"==a.mode;d.onChange()}var d=a.undoManager=new e(a),l=d.editingHandler=new k(d),f=a.addCommand("undo",{exec:function(){d.undo()&&(a.selectionChange(),this.fire("afterUndo"))},
startDisabled:!0,canUndo:!1}),h=a.addCommand("redo",{exec:function(){d.redo()&&(a.selectionChange(),this.fire("afterRedo"))},startDisabled:!0,canUndo:!1});a.setKeystroke([[g[0],"undo"],[g[1],"redo"],[g[2],"redo"]]);d.onChange=function(){f.setState(d.undoable()?CKEDITOR.TRISTATE_OFF:CKEDITOR.TRISTATE_DISABLED);h.setState(d.redoable()?CKEDITOR.TRISTATE_OFF:CKEDITOR.TRISTATE_DISABLED)};a.on("beforeCommandExec",b);a.on("afterCommandExec",b);a.on("saveSnapshot",function(a){d.save(a.data&&a.data.contentOnly)});
a.on("contentDom",l.attachListeners,l);a.on("instanceReady",function(){a.fire("saveSnapshot")});a.on("beforeModeUnload",function(){"wysiwyg"==a.mode&&d.save(!0)});a.on("mode",c);a.on("readOnly",c);a.ui.addButton&&(a.ui.addButton("Undo",{label:a.lang.undo.undo,command:"undo",toolbar:"undo,10"}),a.ui.addButton("Redo",{label:a.lang.undo.redo,command:"redo",toolbar:"undo,20"}));a.resetUndo=function(){d.reset();a.fire("saveSnapshot")};a.on("updateSnapshot",function(){d.currentImage&&d.update()});a.on("lockSnapshot",
function(a){a=a.data;d.lock(a&&a.dontUpdate,a&&a.forceUpdate)});a.on("unlockSnapshot",d.unlock,d)}});CKEDITOR.plugins.undo={};var e=CKEDITOR.plugins.undo.UndoManager=function(a){this.strokesRecorded=[0,0];this.locked=null;this.previousKeyGroup=-1;this.limit=a.config.undoStackSize||20;this.strokesLimit=25;this._filterRules=[];this.editor=a;this.reset();CKEDITOR.env.ie&&this.addFilterRule(function(a){return a.replace(/\s+data-cke-expando=".*?"/g,"")})};e.prototype={type:function(a,b){var c=e.getKeyGroup(a),
d=this.strokesRecorded[c]+1;b=b||d>=this.strokesLimit;this.typing||(this.hasUndo=this.typing=!0,this.hasRedo=!1,this.onChange());b?(d=0,this.editor.fire("saveSnapshot")):this.editor.fire("change");this.strokesRecorded[c]=d;this.previousKeyGroup=c},keyGroupChanged:function(a){return e.getKeyGroup(a)!=this.previousKeyGroup},reset:function(){this.snapshots=[];this.index=-1;this.currentImage=null;this.hasRedo=this.hasUndo=!1;this.locked=null;this.resetType()},resetType:function(){this.strokesRecorded=
[0,0];this.typing=!1;this.previousKeyGroup=-1},refreshState:function(){this.hasUndo=!!this.getNextImage(!0);this.hasRedo=!!this.getNextImage(!1);this.resetType();this.onChange()},save:function(a,b,c){var d=this.editor;if(this.locked||"ready"!=d.status||"wysiwyg"!=d.mode)return!1;var e=d.editable();if(!e||"ready"!=e.status)return!1;e=this.snapshots;b||(b=new f(d));if(!1===b.contents)return!1;if(this.currentImage)if(b.equalsContent(this.currentImage)){if(a||b.equalsSelection(this.currentImage))return!1}else!1!==
c&&d.fire("change");e.splice(this.index+1,e.length-this.index-1);e.length==this.limit&&e.shift();this.index=e.push(b)-1;this.currentImage=b;!1!==c&&this.refreshState();return!0},restoreImage:function(a){var b=this.editor,c;a.bookmarks&&(b.focus(),c=b.getSelection());this.locked={level:999};this.editor.loadSnapshot(a.contents);a.bookmarks?c.selectBookmarks(a.bookmarks):CKEDITOR.env.ie&&(c=this.editor.document.getBody().$.createTextRange(),c.collapse(!0),c.select());this.locked=null;this.index=a.index;
this.currentImage=this.snapshots[this.index];this.update();this.refreshState();b.fire("change")},getNextImage:function(a){var b=this.snapshots,c=this.currentImage,d;if(c)if(a)for(d=this.index-1;0<=d;d--){if(a=b[d],!c.equalsContent(a))return a.index=d,a}else for(d=this.index+1;d<b.length;d++)if(a=b[d],!c.equalsContent(a))return a.index=d,a;return null},redoable:function(){return this.enabled&&this.hasRedo},undoable:function(){return this.enabled&&this.hasUndo},undo:function(){if(this.undoable()){this.save(!0);
var a=this.getNextImage(!0);if(a)return this.restoreImage(a),!0}return!1},redo:function(){if(this.redoable()&&(this.save(!0),this.redoable())){var a=this.getNextImage(!1);if(a)return this.restoreImage(a),!0}return!1},update:function(a){if(!this.locked){a||(a=new f(this.editor));for(var b=this.index,c=this.snapshots;0<b&&this.currentImage.equalsContent(c[b-1]);)--b;c.splice(b,this.index-b+1,a);this.index=b;this.currentImage=a}},updateSelection:function(a){if(!this.snapshots.length)return!1;var b=this.snapshots,
c=b[b.length-1];return c.equalsContent(a)&&!c.equalsSelection(a)?(this.currentImage=b[b.length-1]=a,!0):!1},lock:function(a,b){if(this.locked)this.locked.level++;else if(a)this.locked={level:1};else{var c=null;if(b)c=!0;else{var d=new f(this.editor,!0);this.currentImage&&this.currentImage.equalsContent(d)&&(c=d)}this.locked={update:c,level:1}}},unlock:function(){if(this.locked&&!--this.locked.level){var a=this.locked.update;this.locked=null;if(!0===a)this.update();else if(a){var b=new f(this.editor,
!0);a.equalsContent(b)||this.update()}}},addFilterRule:function(a){this._filterRules.push(a)}};e.navigationKeyCodes={37:1,38:1,39:1,40:1,36:1,35:1,33:1,34:1};e.keyGroups={PRINTABLE:0,FUNCTIONAL:1};e.isNavigationKey=function(a){return!!e.navigationKeyCodes[a]};e.getKeyGroup=function(a){var b=e.keyGroups;return p[a]?b.FUNCTIONAL:b.PRINTABLE};e.getOppositeKeyGroup=function(a){var b=e.keyGroups;return a==b.FUNCTIONAL?b.PRINTABLE:b.FUNCTIONAL};e.ieFunctionalKeysBug=function(a){return CKEDITOR.env.ie&&
e.getKeyGroup(a)==e.keyGroups.FUNCTIONAL};var f=CKEDITOR.plugins.undo.Image=function(a,b){this.editor=a;a.fire("beforeUndoImage");var c=a.getSnapshot();c&&(this.contents=n(c,a.undoManager._filterRules));b||(this.bookmarks=(c=c&&a.getSelection())&&c.createBookmarks2(!0));a.fire("afterUndoImage")},h=/\b(?:href|src|name)="[^"]*?"/gi;f.prototype={equalsContent:function(a){var b=this.contents;a=a.contents;CKEDITOR.env.ie&&(CKEDITOR.env.ie7Compat||CKEDITOR.env.quirks)&&(b=b.replace(h,""),a=a.replace(h,
""));return b!=a?!1:!0},equalsSelection:function(a){var b=this.bookmarks;a=a.bookmarks;if(b||a){if(!b||!a||b.length!=a.length)return!1;for(var c=0;c<b.length;c++){var d=b[c],e=a[c];if(d.startOffset!=e.startOffset||d.endOffset!=e.endOffset||!CKEDITOR.tools.arrayCompare(d.start,e.start)||!CKEDITOR.tools.arrayCompare(d.end,e.end))return!1}}return!0}};var k=CKEDITOR.plugins.undo.NativeEditingHandler=function(a){this.undoManager=a;this.ignoreInputEvent=!1;this.keyEventsStack=new m;this.lastKeydownImage=
null};k.prototype={onKeydown:function(a){var b=a.data.getKey();if(229!==b)if(-1<CKEDITOR.tools.indexOf(g,a.data.getKeystroke()))a.data.preventDefault();else if(this.keyEventsStack.cleanUp(a),a=this.undoManager,this.keyEventsStack.getLast(b)||this.keyEventsStack.push(b),this.lastKeydownImage=new f(a.editor),e.isNavigationKey(b)||this.undoManager.keyGroupChanged(b))if(a.strokesRecorded[0]||a.strokesRecorded[1])a.save(!1,this.lastKeydownImage,!1),a.resetType()},onInput:function(){if(this.ignoreInputEvent)this.ignoreInputEvent=
!1;else{var a=this.keyEventsStack.getLast();a||(a=this.keyEventsStack.push(0));this.keyEventsStack.increment(a.keyCode);this.keyEventsStack.getTotalInputs()>=this.undoManager.strokesLimit&&(this.undoManager.type(a.keyCode,!0),this.keyEventsStack.resetInputs())}},onKeyup:function(a){var b=this.undoManager;a=a.data.getKey();var c=this.keyEventsStack.getTotalInputs();this.keyEventsStack.remove(a);if(!(e.ieFunctionalKeysBug(a)&&this.lastKeydownImage&&this.lastKeydownImage.equalsContent(new f(b.editor,
!0))))if(0<c)b.type(a);else if(e.isNavigationKey(a))this.onNavigationKey(!0)},onNavigationKey:function(a){var b=this.undoManager;!a&&b.save(!0,null,!1)||b.updateSelection(new f(b.editor));b.resetType()},ignoreInputEventListener:function(){this.ignoreInputEvent=!0},activateInputEventListener:function(){this.ignoreInputEvent=!1},attachListeners:function(){var a=this.undoManager.editor,b=a.editable(),c=this;b.attachListener(b,"keydown",function(a){c.onKeydown(a);if(e.ieFunctionalKeysBug(a.data.getKey()))c.onInput()},
null,null,999);b.attachListener(b,CKEDITOR.env.ie?"keypress":"input",c.onInput,c,null,999);b.attachListener(b,"keyup",c.onKeyup,c,null,999);b.attachListener(b,"paste",c.ignoreInputEventListener,c,null,999);b.attachListener(b,"drop",c.ignoreInputEventListener,c,null,999);a.on("afterPaste",c.activateInputEventListener,c,null,999);b.attachListener(b.isInline()?b:a.document.getDocumentElement(),"click",function(){c.onNavigationKey()},null,null,999);b.attachListener(this.undoManager.editor,"blur",function(){c.keyEventsStack.remove(9)},
null,null,999)}};var m=CKEDITOR.plugins.undo.KeyEventsStack=function(){this.stack=[]};m.prototype={push:function(a){a=this.stack.push({keyCode:a,inputs:0});return this.stack[a-1]},getLastIndex:function(a){if("number"!=typeof a)return this.stack.length-1;for(var b=this.stack.length;b--;)if(this.stack[b].keyCode==a)return b;return-1},getLast:function(a){a=this.getLastIndex(a);return-1!=a?this.stack[a]:null},increment:function(a){this.getLast(a).inputs++},remove:function(a){a=this.getLastIndex(a);-1!=
a&&this.stack.splice(a,1)},resetInputs:function(a){if("number"==typeof a)this.getLast(a).inputs=0;else for(a=this.stack.length;a--;)this.stack[a].inputs=0},getTotalInputs:function(){for(var a=this.stack.length,b=0;a--;)b+=this.stack[a].inputs;return b},cleanUp:function(a){a=a.data.$;a.ctrlKey||a.metaKey||this.remove(17);a.shiftKey||this.remove(16);a.altKey||this.remove(18)}}})();CKEDITOR.plugins.add("specialchar",{availableLangs:{af:1,ar:1,az:1,bg:1,ca:1,cs:1,cy:1,da:1,de:1,"de-ch":1,el:1,en:1,"en-au":1,"en-ca":1,"en-gb":1,eo:1,es:1,"es-mx":1,et:1,eu:1,fa:1,fi:1,fr:1,"fr-ca":1,gl:1,he:1,hr:1,hu:1,id:1,it:1,ja:1,km:1,ko:1,ku:1,lt:1,lv:1,nb:1,nl:1,no:1,oc:1,pl:1,pt:1,"pt-br":1,ro:1,ru:1,si:1,sk:1,sl:1,sq:1,sr:1,"sr-latn":1,sv:1,th:1,tr:1,tt:1,ug:1,uk:1,vi:1,zh:1,"zh-cn":1},requires:"dialog",init:function(a){var c=this;CKEDITOR.dialog.add("specialchar",this.path+"dialogs/specialchar.js");
a.addCommand("specialchar",{exec:function(){var b=a.langCode,b=c.availableLangs[b]?b:c.availableLangs[b.replace(/-.*/,"")]?b.replace(/-.*/,""):"en";CKEDITOR.scriptLoader.load(CKEDITOR.getUrl(c.path+"dialogs/lang/"+b+".js"),function(){CKEDITOR.tools.extend(a.lang.specialchar,c.langEntries[b]);a.openDialog("specialchar")})},modes:{wysiwyg:1},canUndo:!1});a.ui.addButton&&a.ui.addButton("SpecialChar",{label:a.lang.specialchar.toolbar,command:"specialchar",toolbar:"insert,50"})}});
CKEDITOR.config.specialChars="! \x26quot; # $ % \x26amp; ' ( ) * + - . / 0 1 2 3 4 5 6 7 8 9 : ; \x26lt; \x3d \x26gt; ? @ A B C D E F G H I J K L M N O P Q R S T U V W X Y Z [ ] ^ _ ` a b c d e f g h i j k l m n o p q r s t u v w x y z { | } ~ \x26euro; \x26lsquo; \x26rsquo; \x26ldquo; \x26rdquo; \x26ndash; \x26mdash; \x26iexcl; \x26cent; \x26pound; \x26curren; \x26yen; \x26brvbar; \x26sect; \x26uml; \x26copy; \x26ordf; \x26laquo; \x26not; \x26reg; \x26macr; \x26deg; \x26sup2; \x26sup3; \x26acute; \x26micro; \x26para; \x26middot; \x26cedil; \x26sup1; \x26ordm; \x26raquo; \x26frac14; \x26frac12; \x26frac34; \x26iquest; \x26Agrave; \x26Aacute; \x26Acirc; \x26Atilde; \x26Auml; \x26Aring; \x26AElig; \x26Ccedil; \x26Egrave; \x26Eacute; \x26Ecirc; \x26Euml; \x26Igrave; \x26Iacute; \x26Icirc; \x26Iuml; \x26ETH; \x26Ntilde; \x26Ograve; \x26Oacute; \x26Ocirc; \x26Otilde; \x26Ouml; \x26times; \x26Oslash; \x26Ugrave; \x26Uacute; \x26Ucirc; \x26Uuml; \x26Yacute; \x26THORN; \x26szlig; \x26agrave; \x26aacute; \x26acirc; \x26atilde; \x26auml; \x26aring; \x26aelig; \x26ccedil; \x26egrave; \x26eacute; \x26ecirc; \x26euml; \x26igrave; \x26iacute; \x26icirc; \x26iuml; \x26eth; \x26ntilde; \x26ograve; \x26oacute; \x26ocirc; \x26otilde; \x26ouml; \x26divide; \x26oslash; \x26ugrave; \x26uacute; \x26ucirc; \x26uuml; \x26yacute; \x26thorn; \x26yuml; \x26OElig; \x26oelig; \x26#372; \x26#374 \x26#373 \x26#375; \x26sbquo; \x26#8219; \x26bdquo; \x26hellip; \x26trade; \x26#9658; \x26bull; \x26rarr; \x26rArr; \x26hArr; \x26diams; \x26asymp;".split(" ");(function(){function m(a,d){CKEDITOR.tools.extend(this,{editor:a,editable:a.editable(),doc:a.document,win:a.window},d,!0);this.inline=this.editable.isInline();this.inline||(this.frame=this.win.getFrame());this.target=this[this.inline?"editable":"doc"]}function n(a,d){CKEDITOR.tools.extend(this,d,{editor:a},!0)}function p(a,d){var b=a.editable();CKEDITOR.tools.extend(this,{editor:a,editable:b,inline:b.isInline(),doc:a.document,win:a.window,container:CKEDITOR.document.getBody(),winTop:CKEDITOR.document.getWindow()},
d,!0);this.hidden={};this.visible={};this.inline||(this.frame=this.win.getFrame());this.queryViewport();var c=CKEDITOR.tools.bind(this.queryViewport,this),e=CKEDITOR.tools.bind(this.hideVisible,this),g=CKEDITOR.tools.bind(this.removeAll,this);b.attachListener(this.winTop,"resize",c);b.attachListener(this.winTop,"scroll",c);b.attachListener(this.winTop,"resize",e);b.attachListener(this.win,"scroll",e);b.attachListener(this.inline?b:this.frame,"mouseout",function(a){var b=a.data.$.clientX;a=a.data.$.clientY;
this.queryViewport();(b<=this.rect.left||b>=this.rect.right||a<=this.rect.top||a>=this.rect.bottom)&&this.hideVisible();(0>=b||b>=this.winTopPane.width||0>=a||a>=this.winTopPane.height)&&this.hideVisible()},this);b.attachListener(a,"resize",c);b.attachListener(a,"mode",g);a.on("destroy",g);this.lineTpl=(new CKEDITOR.template('\x3cdiv data-cke-lineutils-line\x3d"1" class\x3d"cke_reset_all" style\x3d"{lineStyle}"\x3e\x3cspan style\x3d"{tipLeftStyle}"\x3e\x26nbsp;\x3c/span\x3e\x3cspan style\x3d"{tipRightStyle}"\x3e\x26nbsp;\x3c/span\x3e\x3c/div\x3e')).output({lineStyle:CKEDITOR.tools.writeCssText(CKEDITOR.tools.extend({},
t,this.lineStyle,!0)),tipLeftStyle:CKEDITOR.tools.writeCssText(CKEDITOR.tools.extend({},q,{left:"0px","border-left-color":"red","border-width":"6px 0 6px 6px"},this.tipCss,this.tipLeftStyle,!0)),tipRightStyle:CKEDITOR.tools.writeCssText(CKEDITOR.tools.extend({},q,{right:"0px","border-right-color":"red","border-width":"6px 6px 6px 0"},this.tipCss,this.tipRightStyle,!0))})}function l(a){var d;if(d=a&&a.type==CKEDITOR.NODE_ELEMENT)d=!(r[a.getComputedStyle("float")]||r[a.getAttribute("align")]);return d&&
!u[a.getComputedStyle("position")]}CKEDITOR.plugins.add("lineutils");CKEDITOR.LINEUTILS_BEFORE=1;CKEDITOR.LINEUTILS_AFTER=2;CKEDITOR.LINEUTILS_INSIDE=4;m.prototype={start:function(a){var d=this,b=this.editor,c=this.doc,e,g,f,h,k=CKEDITOR.tools.eventsBuffer(50,function(){b.readOnly||"wysiwyg"!=b.mode||(d.relations={},(g=c.$.elementFromPoint(f,h))&&g.nodeType&&(e=new CKEDITOR.dom.element(g),d.traverseSearch(e),isNaN(f+h)||d.pixelSearch(e,f,h),a&&a(d.relations,f,h)))});this.listener=this.editable.attachListener(this.target,
"mousemove",function(a){f=a.data.$.clientX;h=a.data.$.clientY;k.input()});this.editable.attachListener(this.inline?this.editable:this.frame,"mouseout",function(){k.reset()})},stop:function(){this.listener&&this.listener.removeListener()},getRange:function(){var a={};a[CKEDITOR.LINEUTILS_BEFORE]=CKEDITOR.POSITION_BEFORE_START;a[CKEDITOR.LINEUTILS_AFTER]=CKEDITOR.POSITION_AFTER_END;a[CKEDITOR.LINEUTILS_INSIDE]=CKEDITOR.POSITION_AFTER_START;return function(d){var b=this.editor.createRange();b.moveToPosition(this.relations[d.uid].element,
a[d.type]);return b}}(),store:function(){function a(a,b,c){var e=a.getUniqueId();e in c?c[e].type|=b:c[e]={element:a,type:b}}return function(d,b){var c;b&CKEDITOR.LINEUTILS_AFTER&&l(c=d.getNext())&&c.isVisible()&&(a(c,CKEDITOR.LINEUTILS_BEFORE,this.relations),b^=CKEDITOR.LINEUTILS_AFTER);b&CKEDITOR.LINEUTILS_INSIDE&&l(c=d.getFirst())&&c.isVisible()&&(a(c,CKEDITOR.LINEUTILS_BEFORE,this.relations),b^=CKEDITOR.LINEUTILS_INSIDE);a(d,b,this.relations)}}(),traverseSearch:function(a){var d,b,c;do if(c=a.$["data-cke-expando"],
!(c&&c in this.relations)){if(a.equals(this.editable))break;if(l(a))for(d in this.lookups)(b=this.lookups[d](a))&&this.store(a,b)}while((!a||a.type!=CKEDITOR.NODE_ELEMENT||"true"!=a.getAttribute("contenteditable"))&&(a=a.getParent()))},pixelSearch:function(){function a(a,c,e,g,f){for(var h=0,k;f(e);){e+=g;if(25==++h)break;if(k=this.doc.$.elementFromPoint(c,e))if(k==a)h=0;else if(d(a,k)&&(h=0,l(k=new CKEDITOR.dom.element(k))))return k}}var d=CKEDITOR.env.ie||CKEDITOR.env.webkit?function(a,c){return a.contains(c)}:
function(a,c){return!!(a.compareDocumentPosition(c)&16)};return function(b,c,d){var g=this.win.getViewPaneSize().height,f=a.call(this,b.$,c,d,-1,function(a){return 0<a});c=a.call(this,b.$,c,d,1,function(a){return a<g});if(f)for(this.traverseSearch(f);!f.getParent().equals(b);)f=f.getParent();if(c)for(this.traverseSearch(c);!c.getParent().equals(b);)c=c.getParent();for(;f||c;){f&&(f=f.getNext(l));if(!f||f.equals(c))break;this.traverseSearch(f);c&&(c=c.getPrevious(l));if(!c||c.equals(f))break;this.traverseSearch(c)}}}(),
greedySearch:function(){this.relations={};for(var a=this.editable.getElementsByTag("*"),d=0,b,c,e;b=a.getItem(d++);)if(!b.equals(this.editable)&&b.type==CKEDITOR.NODE_ELEMENT&&(b.hasAttribute("contenteditable")||!b.isReadOnly())&&l(b)&&b.isVisible())for(e in this.lookups)(c=this.lookups[e](b))&&this.store(b,c);return this.relations}};n.prototype={locate:function(){function a(a,b){var c=a.element[b===CKEDITOR.LINEUTILS_BEFORE?"getPrevious":"getNext"]();return c&&l(c)?(a.siblingRect=c.getClientRect(),
b==CKEDITOR.LINEUTILS_BEFORE?(a.siblingRect.bottom+a.elementRect.top)/2:(a.elementRect.bottom+a.siblingRect.top)/2):b==CKEDITOR.LINEUTILS_BEFORE?a.elementRect.top:a.elementRect.bottom}return function(d){var b;this.locations={};for(var c in d)b=d[c],b.elementRect=b.element.getClientRect(),b.type&CKEDITOR.LINEUTILS_BEFORE&&this.store(c,CKEDITOR.LINEUTILS_BEFORE,a(b,CKEDITOR.LINEUTILS_BEFORE)),b.type&CKEDITOR.LINEUTILS_AFTER&&this.store(c,CKEDITOR.LINEUTILS_AFTER,a(b,CKEDITOR.LINEUTILS_AFTER)),b.type&
CKEDITOR.LINEUTILS_INSIDE&&this.store(c,CKEDITOR.LINEUTILS_INSIDE,(b.elementRect.top+b.elementRect.bottom)/2);return this.locations}}(),sort:function(){var a,d,b,c;return function(e,g){a=this.locations;d=[];for(var f in a)for(var h in a[f])if(b=Math.abs(e-a[f][h]),d.length){for(c=0;c<d.length;c++)if(b<d[c].dist){d.splice(c,0,{uid:+f,type:h,dist:b});break}c==d.length&&d.push({uid:+f,type:h,dist:b})}else d.push({uid:+f,type:h,dist:b});return"undefined"!=typeof g?d.slice(0,g):d}}(),store:function(a,
d,b){this.locations[a]||(this.locations[a]={});this.locations[a][d]=b}};var q={display:"block",width:"0px",height:"0px","border-color":"transparent","border-style":"solid",position:"absolute",top:"-6px"},t={height:"0px","border-top":"1px dashed red",position:"absolute","z-index":9999};p.prototype={removeAll:function(){for(var a in this.hidden)this.hidden[a].remove(),delete this.hidden[a];for(a in this.visible)this.visible[a].remove(),delete this.visible[a]},hideLine:function(a){var d=a.getUniqueId();
a.hide();this.hidden[d]=a;delete this.visible[d]},showLine:function(a){var d=a.getUniqueId();a.show();this.visible[d]=a;delete this.hidden[d]},hideVisible:function(){for(var a in this.visible)this.hideLine(this.visible[a])},placeLine:function(a,d){var b,c,e;if(b=this.getStyle(a.uid,a.type)){for(e in this.visible)if(this.visible[e].getCustomData("hash")!==this.hash){c=this.visible[e];break}if(!c)for(e in this.hidden)if(this.hidden[e].getCustomData("hash")!==this.hash){this.showLine(c=this.hidden[e]);
break}c||this.showLine(c=this.addLine());c.setCustomData("hash",this.hash);this.visible[c.getUniqueId()]=c;c.setStyles(b);d&&d(c)}},getStyle:function(a,d){var b=this.relations[a],c=this.locations[a][d],e={};e.width=b.siblingRect?Math.max(b.siblingRect.width,b.elementRect.width):b.elementRect.width;e.top=this.inline?c+this.winTopScroll.y-this.rect.relativeY:this.rect.top+this.winTopScroll.y+c;if(e.top-this.winTopScroll.y<this.rect.top||e.top-this.winTopScroll.y>this.rect.bottom)return!1;this.inline?
e.left=b.elementRect.left-this.rect.relativeX:(0<b.elementRect.left?e.left=this.rect.left+b.elementRect.left:(e.width+=b.elementRect.left,e.left=this.rect.left),0<(b=e.left+e.width-(this.rect.left+this.winPane.width))&&(e.width-=b));e.left+=this.winTopScroll.x;for(var g in e)e[g]=CKEDITOR.tools.cssLength(e[g]);return e},addLine:function(){var a=CKEDITOR.dom.element.createFromHtml(this.lineTpl);a.appendTo(this.container);return a},prepare:function(a,d){this.relations=a;this.locations=d;this.hash=Math.random()},
cleanup:function(){var a,d;for(d in this.visible)a=this.visible[d],a.getCustomData("hash")!==this.hash&&this.hideLine(a)},queryViewport:function(){this.winPane=this.win.getViewPaneSize();this.winTopScroll=this.winTop.getScrollPosition();this.winTopPane=this.winTop.getViewPaneSize();this.rect=this.getClientRect(this.inline?this.editable:this.frame)},getClientRect:function(a){a=a.getClientRect();var d=this.container.getDocumentPosition(),b=this.container.getComputedStyle("position");a.relativeX=a.relativeY=
0;"static"!=b&&(a.relativeY=d.y,a.relativeX=d.x,a.top-=a.relativeY,a.bottom-=a.relativeY,a.left-=a.relativeX,a.right-=a.relativeX);return a}};var r={left:1,right:1,center:1},u={absolute:1,fixed:1};CKEDITOR.plugins.lineutils={finder:m,locator:n,liner:p}})();(function(){function e(a){return a.getName&&!a.hasAttribute("data-cke-temp")}CKEDITOR.plugins.add("widgetselection",{init:function(a){if(CKEDITOR.env.webkit){var b=CKEDITOR.plugins.widgetselection;a.on("contentDom",function(a){a=a.editor;var c=a.editable();c.attachListener(c,"keydown",function(a){a.data.getKeystroke()==CKEDITOR.CTRL+65&&CKEDITOR.tools.setTimeout(function(){b.addFillers(c)||b.removeFillers(c)},0)},null,null,-1);a.on("selectionCheck",function(a){b.removeFillers(a.editor.editable())});
a.on("paste",function(a){a.data.dataValue=b.cleanPasteData(a.data.dataValue)});"selectall"in a.plugins&&b.addSelectAllIntegration(a)})}}});CKEDITOR.plugins.widgetselection={startFiller:null,endFiller:null,fillerAttribute:"data-cke-filler-webkit",fillerContent:"\x26nbsp;",fillerTagName:"div",addFillers:function(a){var b=a.editor;if(!this.isWholeContentSelected(a)&&0<a.getChildCount()){var d=a.getFirst(e),c=a.getLast(e);d&&d.type==CKEDITOR.NODE_ELEMENT&&!d.isEditable()&&(this.startFiller=this.createFiller(),
a.append(this.startFiller,1));c&&c.type==CKEDITOR.NODE_ELEMENT&&!c.isEditable()&&(this.endFiller=this.createFiller(!0),a.append(this.endFiller,0));if(this.hasFiller(a))return b=b.createRange(),b.selectNodeContents(a),b.select(),!0}return!1},removeFillers:function(a){if(this.hasFiller(a)&&!this.isWholeContentSelected(a)){var b=a.findOne(this.fillerTagName+"["+this.fillerAttribute+"\x3dstart]"),d=a.findOne(this.fillerTagName+"["+this.fillerAttribute+"\x3dend]");this.startFiller&&b&&this.startFiller.equals(b)?
this.removeFiller(this.startFiller,a):this.startFiller=b;this.endFiller&&d&&this.endFiller.equals(d)?this.removeFiller(this.endFiller,a):this.endFiller=d}},cleanPasteData:function(a){a&&a.length&&(a=a.replace(this.createFillerRegex(),"").replace(this.createFillerRegex(!0),""));return a},isWholeContentSelected:function(a){var b=a.editor.getSelection().getRanges()[0];return!b||b&&b.collapsed?!1:(b=b.clone(),b.enlarge(CKEDITOR.ENLARGE_ELEMENT),!!(b&&a&&b.startContainer&&b.endContainer&&0===b.startOffset&&
b.endOffset===a.getChildCount()&&b.startContainer.equals(a)&&b.endContainer.equals(a)))},hasFiller:function(a){return 0<a.find(this.fillerTagName+"["+this.fillerAttribute+"]").count()},createFiller:function(a){var b=new CKEDITOR.dom.element(this.fillerTagName);b.setHtml(this.fillerContent);b.setAttribute(this.fillerAttribute,a?"end":"start");b.setAttribute("data-cke-temp",1);b.setStyles({display:"block",width:0,height:0,padding:0,border:0,margin:0,position:"absolute",top:0,left:"-9999px",opacity:0,
overflow:"hidden"});return b},removeFiller:function(a,b){if(a){var d=b.editor,c=b.editor.getSelection().getRanges()[0].startPath(),f=d.createRange(),g,e;c.contains(a)&&(g=a.getHtml(),e=!0);c="start"==a.getAttribute(this.fillerAttribute);a.remove();g&&0<g.length&&g!=this.fillerContent?(b.insertHtmlIntoRange(g,d.getSelection().getRanges()[0]),f.setStartAt(b.getChild(b.getChildCount()-1),CKEDITOR.POSITION_BEFORE_END),d.getSelection().selectRanges([f])):e&&(c?f.setStartAt(b.getFirst().getNext(),CKEDITOR.POSITION_AFTER_START):
f.setEndAt(b.getLast().getPrevious(),CKEDITOR.POSITION_BEFORE_END),b.editor.getSelection().selectRanges([f]))}},createFillerRegex:function(a){var b=this.createFiller(a).getOuterHtml().replace(/style="[^"]*"/gi,'style\x3d"[^"]*"').replace(/>[^<]*</gi,"\x3e[^\x3c]*\x3c");return new RegExp((a?"":"^")+b+(a?"$":""))},addSelectAllIntegration:function(a){var b=this;a.editable().attachListener(a,"beforeCommandExec",function(d){var c=a.editable();"selectAll"==d.data.name&&c&&b.addFillers(c)},null,null,9999)}}})();(function(){function q(a){this.editor=a;this.registered={};this.instances={};this.selected=[];this.widgetHoldingFocusedEditable=this.focused=null;this._={nextId:0,upcasts:[],upcastCallbacks:[],filters:{}};R(this);S(this);this.on("checkWidgets",T);this.editor.on("contentDomInvalidated",this.checkWidgets,this);U(this);V(this);W(this);X(this);Y(this)}function h(a,b,c,d,e){var f=a.editor;CKEDITOR.tools.extend(this,d,{editor:f,id:b,inline:"span"==c.getParent().getName(),element:c,data:CKEDITOR.tools.extend({},
"function"==typeof d.defaults?d.defaults():d.defaults),dataReady:!1,inited:!1,ready:!1,edit:h.prototype.edit,focusedEditable:null,definition:d,repository:a,draggable:!1!==d.draggable,_:{downcastFn:d.downcast&&"string"==typeof d.downcast?d.downcasts[d.downcast]:d.downcast}},!0);a.fire("instanceCreated",this);Z(this,d);this.init&&this.init();this.inited=!0;(a=this.element.data("cke-widget-data"))&&this.setData(JSON.parse(decodeURIComponent(a)));e&&this.setData(e);this.data.classes||this.setData("classes",
this.getClasses());this.dataReady=!0;v(this);this.fire("data",this.data);this.isInited()&&f.editable().contains(this.wrapper)&&(this.ready=!0,this.fire("ready"))}function t(a,b,c){CKEDITOR.dom.element.call(this,b.$);this.editor=a;this._={};b=this.filter=c.filter;CKEDITOR.dtd[this.getName()].p?(this.enterMode=b?b.getAllowedEnterMode(a.enterMode):a.enterMode,this.shiftEnterMode=b?b.getAllowedEnterMode(a.shiftEnterMode,!0):a.shiftEnterMode):this.enterMode=this.shiftEnterMode=CKEDITOR.ENTER_BR}function aa(a,
b){a.addCommand(b.name,{exec:function(a,d){function e(){a.widgets.finalizeCreation(k)}var f=a.widgets.focused;if(f&&f.name==b.name)f.edit();else if(b.insert)b.insert({editor:a,commandData:d});else if(b.template){var f="function"==typeof b.defaults?b.defaults():b.defaults,f=CKEDITOR.dom.element.createFromHtml(b.template.output(f),a.document),g,l=a.widgets.wrapElement(f,b.name),k=new CKEDITOR.dom.documentFragment(l.getDocument());k.append(l);(g=a.widgets.initOn(f,b,d&&d.startupData))?(f=g.once("edit",
function(b){if(b.data.dialog)g.once("dialog",function(b){b=b.data;var d,f;d=b.once("ok",e,null,null,20);f=b.once("cancel",function(b){b.data&&!1===b.data.hide||a.widgets.destroy(g,!0)});b.once("hide",function(){d.removeListener();f.removeListener()})});else e()},null,null,999),g.edit(),f.removeListener()):e()}},allowedContent:b.allowedContent,requiredContent:b.requiredContent,contentForms:b.contentForms,contentTransformations:b.contentTransformations})}function ba(a,b){function c(a,e){var c=b.upcast.split(","),
d,f;for(f=0;f<c.length;f++)if(d=c[f],d===a.name)return b.upcasts[d].call(this,a,e);return!1}function d(b,e,c){var d=CKEDITOR.tools.getIndex(a._.upcasts,function(a){return a[2]>c});0>d&&(d=a._.upcasts.length);a._.upcasts.splice(d,0,[CKEDITOR.tools.bind(b,e),e.name,c])}var e=b.upcast,f=b.upcastPriority||10;e&&("string"==typeof e?d(c,b,f):d(e,b,f))}function x(a,b){a.focused=null;if(b.isInited()){var c=b.editor.checkDirty();a.fire("widgetBlurred",{widget:b});b.setFocused(!1);!c&&b.editor.resetDirty()}}
function T(a){a=a.data;if("wysiwyg"==this.editor.mode){var b=this.editor.editable(),c=this.instances,d,e,f,g;if(b){for(d in c)c[d].isReady()&&!b.contains(c[d].wrapper)&&this.destroy(c[d],!0);if(a&&a.initOnlyNew)c=this.initOnAll();else{var l=b.find(".cke_widget_wrapper"),c=[];d=0;for(e=l.count();d<e;d++){f=l.getItem(d);if(g=!this.getByElement(f,!0)){a:{g=ca;for(var k=f;k=k.getParent();)if(g(k)){g=!0;break a}g=!1}g=!g}g&&b.contains(f)&&(f.addClass("cke_widget_new"),c.push(this.initOn(f.getFirst(h.isDomWidgetElement))))}}a&&
a.focusInited&&1==c.length&&c[0].focus()}}}function y(a){if("undefined"!=typeof a.attributes&&a.attributes["data-widget"]){var b=z(a),c=A(a),d=!1;b&&b.value&&b.value.match(/^\s/g)&&(b.parent.attributes["data-cke-white-space-first"]=1,b.value=b.value.replace(/^\s/g,"\x26nbsp;"),d=!0);c&&c.value&&c.value.match(/\s$/g)&&(c.parent.attributes["data-cke-white-space-last"]=1,c.value=c.value.replace(/\s$/g,"\x26nbsp;"),d=!0);d&&(a.attributes["data-cke-widget-white-space"]=1)}}function z(a){return a.find(function(a){return 3===
a.type},!0).shift()}function A(a){return a.find(function(a){return 3===a.type},!0).pop()}function B(a,b,c){if(!c.allowedContent&&!c.disallowedContent)return null;var d=this._.filters[a];d||(this._.filters[a]=d={});a=d[b];a||(a=c.allowedContent?new CKEDITOR.filter(c.allowedContent):this.editor.filter.clone(),d[b]=a,c.disallowedContent&&a.disallow(c.disallowedContent));return a}function da(a){var b=[],c=a._.upcasts,d=a._.upcastCallbacks;return{toBeWrapped:b,iterator:function(a){var f,g,l,k,n;if("data-cke-widget-wrapper"in
a.attributes)return(a=a.getFirst(h.isParserWidgetElement))&&b.push([a]),!1;if("data-widget"in a.attributes)return b.push([a]),!1;if(n=c.length){if(a.attributes["data-cke-widget-upcasted"])return!1;k=0;for(f=d.length;k<f;++k)if(!1===d[k](a))return;for(k=0;k<n;++k)if(f=c[k],l={},g=f[0](a,l))return g instanceof CKEDITOR.htmlParser.element&&(a=g),a.attributes["data-cke-widget-data"]=encodeURIComponent(JSON.stringify(l)),a.attributes["data-cke-widget-upcasted"]=1,b.push([a,f[1]]),!1}}}}function C(a,b){return{tabindex:-1,
contenteditable:"false","data-cke-widget-wrapper":1,"data-cke-filter":"off","class":"cke_widget_wrapper cke_widget_new cke_widget_"+(a?"inline":"block")+(b?" cke_widget_"+b:"")}}function D(a,b,c){if(a.type==CKEDITOR.NODE_ELEMENT){var d=CKEDITOR.dtd[a.name];if(d&&!d[c.name]){var d=a.split(b),e=a.parent;b=d.getIndex();a.children.length||(--b,a.remove());d.children.length||d.remove();return D(e,b,c)}}a.add(c,b)}function E(a,b){return"boolean"==typeof a.inline?a.inline:!!CKEDITOR.dtd.$inline[b]}function ca(a){return a.hasAttribute("data-cke-temp")}
function p(a,b,c,d){var e=a.editor;e.fire("lockSnapshot");c?(d=c.data("cke-widget-editable"),d=b.editables[d],a.widgetHoldingFocusedEditable=b,b.focusedEditable=d,c.addClass("cke_widget_editable_focused"),d.filter&&e.setActiveFilter(d.filter),e.setActiveEnterMode(d.enterMode,d.shiftEnterMode)):(d||b.focusedEditable.removeClass("cke_widget_editable_focused"),b.focusedEditable=null,a.widgetHoldingFocusedEditable=null,e.setActiveFilter(null),e.setActiveEnterMode(null,null));e.fire("unlockSnapshot")}
function ea(a){a.contextMenu&&a.contextMenu.addListener(function(b){if(b=a.widgets.getByElement(b,!0))return b.fire("contextMenu",{})})}function fa(a,b){return CKEDITOR.tools.trim(b)}function X(a){var b=a.editor,c=CKEDITOR.plugins.lineutils;b.on("dragstart",function(c){var e=c.data.target;h.isDomDragHandler(e)&&(e=a.getByElement(e),c.data.dataTransfer.setData("cke/widget-id",e.id),b.focus(),e.focus())});b.on("drop",function(c){function e(a,b){return a&&b?a.wrapper.equals(b.wrapper)||a.wrapper.contains(b.wrapper):
!1}var f=c.data.dataTransfer,g=f.getData("cke/widget-id"),l=f.getTransferType(b),f=b.createRange(),k=function(a){a=a.getBoundaryNodes().startNode;a.type!==CKEDITOR.NODE_ELEMENT&&(a=a.getParent());return b.widgets.getByElement(a)}(c.data.dropRange);if(""!==g&&l===CKEDITOR.DATA_TRANSFER_CROSS_EDITORS)c.cancel();else if(l==CKEDITOR.DATA_TRANSFER_INTERNAL)if(""===g&&0<b.widgets.selected.length)c.data.dataTransfer.setData("text/html",F(b));else if(g=a.instances[g])e(g,k)?c.cancel():(f.setStartBefore(g.wrapper),
f.setEndAfter(g.wrapper),c.data.dragRange=f,delete CKEDITOR.plugins.clipboard.dragStartContainerChildCount,delete CKEDITOR.plugins.clipboard.dragEndContainerChildCount,c.data.dataTransfer.setData("text/html",g.getClipboardHtml()),b.widgets.destroy(g,!0))});b.on("contentDom",function(){var d=b.editable();CKEDITOR.tools.extend(a,{finder:new c.finder(b,{lookups:{"default":function(b){if(!b.is(CKEDITOR.dtd.$listItem)&&b.is(CKEDITOR.dtd.$block)&&!h.isDomNestedEditable(b)&&!a._.draggedWidget.wrapper.contains(b)){var c=
h.getNestedEditable(d,b);if(c){b=a._.draggedWidget;if(a.getByElement(c)==b)return;c=CKEDITOR.filter.instances[c.data("cke-filter")];b=b.requiredContent;if(c&&b&&!c.check(b))return}return CKEDITOR.LINEUTILS_BEFORE|CKEDITOR.LINEUTILS_AFTER}}}}),locator:new c.locator(b),liner:new c.liner(b,{lineStyle:{cursor:"move !important","border-top-color":"#666"},tipLeftStyle:{"border-left-color":"#666"},tipRightStyle:{"border-right-color":"#666"}})},!0)})}function V(a){var b=a.editor;b.on("contentDom",function(){var c=
b.editable(),d=c.isInline()?c:b.document,e,f;c.attachListener(d,"mousedown",function(c){var d=c.data.getTarget();e=d instanceof CKEDITOR.dom.element?a.getByElement(d):null;f=0;e&&(e.inline&&d.type==CKEDITOR.NODE_ELEMENT&&d.hasAttribute("data-cke-widget-drag-handler")?(f=1,a.focused!=e&&b.getSelection().removeAllRanges()):h.getNestedEditable(e.wrapper,d)?e=null:(c.data.preventDefault(),CKEDITOR.env.ie||e.focus()))});c.attachListener(d,"mouseup",function(){f&&e&&e.wrapper&&(f=0,e.focus())});CKEDITOR.env.ie&&
c.attachListener(d,"mouseup",function(){setTimeout(function(){e&&e.wrapper&&c.contains(e.wrapper)&&(e.focus(),e=null)})})});b.on("doubleclick",function(b){var d=a.getByElement(b.data.element);if(d&&!h.getNestedEditable(d.wrapper,b.data.element))return d.fire("doubleclick",{element:b.data.element})},null,null,1)}function W(a){a.editor.on("key",function(b){var c=a.focused,d=a.widgetHoldingFocusedEditable,e;c?e=c.fire("key",{keyCode:b.data.keyCode}):d&&(c=b.data.keyCode,b=d.focusedEditable,c==CKEDITOR.CTRL+
65?(c=b.getBogus(),d=d.editor.createRange(),d.selectNodeContents(b),c&&d.setEndAt(c,CKEDITOR.POSITION_BEFORE_START),d.select(),e=!1):8==c||46==c?(e=d.editor.getSelection().getRanges(),d=e[0],e=!(1==e.length&&d.collapsed&&d.checkBoundaryOfElement(b,CKEDITOR[8==c?"START":"END"]))):e=void 0);return e},null,null,1)}function Y(a){function b(b){1>a.selected.length||G(c,"cut"===b.name)}var c=a.editor;c.on("contentDom",function(){var a=c.editable();a.attachListener(a,"copy",b);a.attachListener(a,"cut",b)})}
function U(a){function b(){var a=e.getSelection();if(a&&(a=a.getRanges()[0])&&!a.collapsed){var b=c(a.startContainer),d=c(a.endContainer);!b&&d?(a.setEndBefore(d.wrapper),a.select()):b&&!d&&(a.setStartAfter(b.wrapper),a.select())}}function c(a){return a?a.type==CKEDITOR.NODE_TEXT?c(a.getParent()):e.widgets.getByElement(a):null}function d(){a.fire("checkSelection")}var e=a.editor;e.on("selectionCheck",d);e.on("contentDom",function(){e.editable().attachListener(e,"key",function(){setTimeout(d,10)})});
if(!CKEDITOR.env.ie)a.on("checkSelection",b);a.on("checkSelection",a.checkSelection,a);e.on("selectionChange",function(b){var c=(b=h.getNestedEditable(e.editable(),b.data.selection.getStartElement()))&&a.getByElement(b),d=a.widgetHoldingFocusedEditable;d?d===c&&d.focusedEditable.equals(b)||(p(a,d,null),c&&b&&p(a,c,b)):c&&b&&p(a,c,b)});e.on("dataReady",function(){H(a).commit()});e.on("blur",function(){var b;(b=a.focused)&&x(a,b);(b=a.widgetHoldingFocusedEditable)&&p(a,b,null)})}function S(a){var b=
a.editor,c={};b.on("toDataFormat",function(b){var e=CKEDITOR.tools.getNextNumber(),f=[];b.data.downcastingSessionId=e;c[e]=f;b.data.dataValue.forEach(function(b){var c=b.attributes,e;if("data-cke-widget-white-space"in c){e=z(b);var d=A(b);e.parent.attributes["data-cke-white-space-first"]&&(e.value=e.value.replace(/^&nbsp;/g," "));d.parent.attributes["data-cke-white-space-last"]&&(d.value=d.value.replace(/&nbsp;$/g," "))}if("data-cke-widget-id"in c){if(c=a.instances[c["data-cke-widget-id"]])e=b.getFirst(h.isParserWidgetElement),
f.push({wrapper:b,element:e,widget:c,editables:{}}),"1"!=e.attributes["data-cke-widget-keep-attr"]&&delete e.attributes["data-widget"]}else if("data-cke-widget-editable"in c)return 0<f.length&&(f[f.length-1].editables[c["data-cke-widget-editable"]]=b),!1},CKEDITOR.NODE_ELEMENT,!0)},null,null,8);b.on("toDataFormat",function(a){if(a.data.downcastingSessionId)for(var b=c[a.data.downcastingSessionId],f,g,l,k,h,m;f=b.shift();){g=f.widget;l=f.element;k=g._.downcastFn&&g._.downcastFn.call(g,l);a.data.widgetsCopy&&
g.getClipboardHtml&&(k=CKEDITOR.htmlParser.fragment.fromHtml(g.getClipboardHtml()),k=k.children[0]);for(m in f.editables)h=f.editables[m],delete h.attributes.contenteditable,h.setHtml(g.editables[m].getData());k||(k=l);f.wrapper.replaceWith(k)}},null,null,13);b.on("contentDomUnload",function(){a.destroyAll(!0)})}function R(a){var b=a.editor,c,d;b.on("toHtml",function(b){var d=da(a),g;for(b.data.dataValue.forEach(d.iterator,CKEDITOR.NODE_ELEMENT,!0);g=d.toBeWrapped.pop();){var l=g[0],k=l.parent;k.type==
CKEDITOR.NODE_ELEMENT&&k.attributes["data-cke-widget-wrapper"]&&k.replaceWith(l);a.wrapElement(g[0],g[1])}c=b.data.protectedWhitespaces?3==b.data.dataValue.children.length&&h.isParserWidgetWrapper(b.data.dataValue.children[1]):1==b.data.dataValue.children.length&&h.isParserWidgetWrapper(b.data.dataValue.children[0])},null,null,8);b.on("dataReady",function(){if(d)for(var c=b.editable().find(".cke_widget_wrapper"),f,g,l=0,k=c.count();l<k;++l)f=c.getItem(l),g=f.getFirst(h.isDomWidgetElement),g.type==
CKEDITOR.NODE_ELEMENT&&g.data("widget")?(g.replace(f),a.wrapElement(g)):f.remove();d=0;a.destroyAll(!0);a.initOnAll()});b.on("loadSnapshot",function(b){/data-cke-widget/.test(b.data)&&(d=1);a.destroyAll(!0)},null,null,9);b.on("paste",function(a){a=a.data;a.dataValue=a.dataValue.replace(ga,fa);a.range&&(a=h.getNestedEditable(b.editable(),a.range.startContainer))&&(a=CKEDITOR.filter.instances[a.data("cke-filter")])&&b.setActiveFilter(a)});b.on("afterInsertHtml",function(d){d.data.intoRange?a.checkWidgets({initOnlyNew:!0}):
(b.fire("lockSnapshot"),a.checkWidgets({initOnlyNew:!0,focusInited:c}),b.fire("unlockSnapshot"))})}function H(a){var b=a.selected,c=[],d=b.slice(0),e=null;return{select:function(a){0>CKEDITOR.tools.indexOf(b,a)&&c.push(a);a=CKEDITOR.tools.indexOf(d,a);0<=a&&d.splice(a,1);return this},focus:function(a){e=a;return this},commit:function(){var f=a.focused!==e,g,l;a.editor.fire("lockSnapshot");for(f&&(g=a.focused)&&x(a,g);g=d.pop();)b.splice(CKEDITOR.tools.indexOf(b,g),1),g.isInited()&&(l=g.editor.checkDirty(),
g.setSelected(!1),!l&&g.editor.resetDirty());f&&e&&(l=a.editor.checkDirty(),a.focused=e,a.fire("widgetFocused",{widget:e}),e.setFocused(!0),!l&&a.editor.resetDirty());for(;g=c.pop();)b.push(g),g.setSelected(!0);a.editor.fire("unlockSnapshot")}}}function ha(a){a&&a.addFilterRule(function(a){return a.replace(/\s*cke_widget_selected/g,"").replace(/\s*cke_widget_focused/g,"")})}function I(a,b,c){var d=0;b=J(b);var e=a.data.classes||{},f;if(b){for(e=CKEDITOR.tools.clone(e);f=b.pop();)c?e[f]||(d=e[f]=1):
e[f]&&(delete e[f],d=1);d&&a.setData("classes",e)}}function K(a){a.cancel()}function L(a,b){var c=function(a){return a==CKEDITOR.ENTER_BR?"br":a==CKEDITOR.ENTER_DIV?"div":"p"}(a.editor.config.enterMode),d=new CKEDITOR.dom.element(c);"br"!==c&&d.appendBogus();"after"===b?d.insertAfter(a.wrapper):d.insertBefore(a.wrapper);(function(b){var c=a.editor.createRange();c.setStart(b,0);a.editor.getSelection().selectRanges([c])})(d)}function G(a,b){var c=a.widgets.focused,d,e,f;u.hasCopyBin(a)||(e=new u(a,
{beforeDestroy:function(){!b&&c&&c.focus();f&&a.getSelection().selectBookmarks(f);d&&CKEDITOR.plugins.widgetselection.addFillers(a.editable())},afterDestroy:function(){b&&!a.readOnly&&(c?a.widgets.del(c):a.extractSelectedHtml(),a.fire("saveSnapshot"))}}),c||(d=CKEDITOR.env.webkit&&CKEDITOR.plugins.widgetselection.isWholeContentSelected(a.editable()),f=a.getSelection().createBookmarks(!0)),e.handle(F(a)))}function J(a){return(a=(a=a.getDefinition().attributes)&&a["class"])?a.split(/\s+/):null}function M(){var a=
CKEDITOR.document.getActive(),b=this.editor,c=b.editable();(c.isInline()?c:b.document.getWindow().getFrame()).equals(a)&&b.focusManager.focus(c)}function N(){CKEDITOR.env.gecko&&this.editor.unlockSelection();CKEDITOR.env.webkit||(this.editor.forceNextSelectionCheck(),this.editor.selectionChange(1))}function F(a){var b=a.getSelectedHtml(!0);if(a.widgets.focused)return a.widgets.focused.getClipboardHtml();a.once("toDataFormat",function(a){a.data.widgetsCopy=!0},null,null,-1);return a.dataProcessor.toDataFormat(b)}
function Z(a,b){var c=a.editor.config.widget_keystrokeInsertLineBefore,d=a.editor.config.widget_keystrokeInsertLineAfter;ia(a);O(a);ja(a);P(a);ka(a);la(a);ma(a);if(CKEDITOR.env.ie&&9>CKEDITOR.env.version)a.wrapper.on("dragstart",function(b){var c=b.data.getTarget();h.getNestedEditable(a,c)||a.inline&&h.isDomDragHandler(c)||b.data.preventDefault()});a.wrapper.removeClass("cke_widget_new");a.element.addClass("cke_widget_element");a.on("key",function(b){b=b.data.keyCode;if(b==c)L(a,"before"),a.editor.fire("saveSnapshot");
else if(b==d)L(a,"after"),a.editor.fire("saveSnapshot");else if(13==b)a.edit();else{if(b==CKEDITOR.CTRL+67||b==CKEDITOR.CTRL+88){G(a.editor,b==CKEDITOR.CTRL+88);return}if(b in Q||CKEDITOR.CTRL&b||CKEDITOR.ALT&b)return}return!1},null,null,999);a.on("doubleclick",function(b){a.edit()&&b.cancel()});if(b.data)a.on("data",b.data);if(b.edit)a.on("edit",b.edit)}function ia(a){(a.wrapper=a.element.getParent()).setAttribute("data-cke-widget-id",a.id)}function O(a,b){a.partSelectors||(a.partSelectors=a.parts);
if(a.parts){var c={},d,e;for(e in a.partSelectors)b||!a.parts[e]||"string"==typeof a.parts[e]?(d=a.wrapper.findOne(a.partSelectors[e]),c[e]=d):c[e]=a.parts[e];a.parts=c}}function ja(a){var b=a.editables,c,d;a.editables={};if(a.editables)for(c in b)d=b[c],a.initEditable(c,"string"==typeof d?{selector:d}:d)}function P(a){if(!0===a.mask)na(a);else if(a.mask){var b=new CKEDITOR.tools.buffers.throttle(250,oa,a),c=CKEDITOR.env.gecko?300:0,d,e;a.on("focus",function(){b.input();d=a.editor.on("change",b.input);
e=a.on("blur",function(){d.removeListener();e.removeListener()})});a.editor.on("instanceReady",function(){setTimeout(function(){b.input()},c)});a.editor.on("mode",function(){setTimeout(function(){b.input()},c)});if(CKEDITOR.env.gecko){var f=a.element.find("img");CKEDITOR.tools.array.forEach(f.toArray(),function(a){a.on("load",function(){b.input()})})}for(var g in a.editables)a.editables[g].on("focus",function(){a.editor.on("change",b.input);e&&e.removeListener()}),a.editables[g].on("blur",function(){a.editor.removeListener("change",
b.input)});b.input()}}function na(a){var b=a.wrapper.findOne(".cke_widget_mask");b||(b=new CKEDITOR.dom.element("img",a.editor.document),b.setAttributes({src:CKEDITOR.tools.transparentImageData,"class":"cke_reset cke_widget_mask"}),a.wrapper.append(b));a.mask=b}function oa(){if(this.wrapper){this.maskPart=this.maskPart||this.mask;var a=this.parts[this.maskPart],b;if(a&&"string"!=typeof a){b=this.wrapper.findOne(".cke_widget_partial_mask");b||(b=new CKEDITOR.dom.element("img",this.editor.document),
b.setAttributes({src:CKEDITOR.tools.transparentImageData,"class":"cke_reset cke_widget_partial_mask"}),this.wrapper.append(b));this.mask=b;var c=b.$,d=a.$,e=!(c.offsetTop==d.offsetTop&&c.offsetLeft==d.offsetLeft);if(c.offsetWidth!=d.offsetWidth||c.offsetHeight!=d.offsetHeight||e)c=a.getParent(),d=CKEDITOR.plugins.widget.isDomWidget(c),b.setStyles({top:a.$.offsetTop+(d?0:c.$.offsetTop)+"px",left:a.$.offsetLeft+(d?0:c.$.offsetLeft)+"px",width:a.$.offsetWidth+"px",height:a.$.offsetHeight+"px"})}}}function ka(a){if(a.draggable){var b=
a.editor,c=a.wrapper.getLast(h.isDomDragHandlerContainer),d;c?d=c.findOne("img"):(c=new CKEDITOR.dom.element("span",b.document),c.setAttributes({"class":"cke_reset cke_widget_drag_handler_container",style:"background:rgba(220,220,220,0.5);background-image:url("+b.plugins.widget.path+"images/handle.png);display:none;"}),d=new CKEDITOR.dom.element("img",b.document),d.setAttributes({"class":"cke_reset cke_widget_drag_handler","data-cke-widget-drag-handler":"1",src:CKEDITOR.tools.transparentImageData,
width:15,title:b.lang.widget.move,height:15,role:"presentation"}),a.inline&&d.setAttribute("draggable","true"),c.append(d),a.wrapper.append(c));a.wrapper.on("dragover",function(a){a.data.preventDefault()});a.wrapper.on("mouseenter",a.updateDragHandlerPosition,a);setTimeout(function(){a.on("data",a.updateDragHandlerPosition,a)},50);if(!a.inline&&(d.on("mousedown",pa,a),CKEDITOR.env.ie&&9>CKEDITOR.env.version))d.on("dragstart",function(a){a.data.preventDefault(!0)});a.dragHandlerContainer=c}}function pa(a){function b(){var b;
for(r.reset();b=l.pop();)b.removeListener();var c=k;b=a.sender;var d=this.repository.finder,e=this.repository.liner,f=this.editor,g=this.editor.editable();CKEDITOR.tools.isEmpty(e.visible)||(c=d.getRange(c[0]),this.focus(),f.fire("drop",{dropRange:c,target:c.startContainer}));g.removeClass("cke_widget_dragging");e.hideVisible();f.fire("dragend",{target:b})}if(CKEDITOR.tools.getMouseButton(a)===CKEDITOR.MOUSE_BUTTON_LEFT){var c=this.repository.finder,d=this.repository.locator,e=this.repository.liner,
f=this.editor,g=f.editable(),l=[],k=[],h,m;this.repository._.draggedWidget=this;var w=c.greedySearch(),r=CKEDITOR.tools.eventsBuffer(50,function(){h=d.locate(w);k=d.sort(m,1);k.length&&(e.prepare(w,h),e.placeLine(k[0]),e.cleanup())});g.addClass("cke_widget_dragging");l.push(g.on("mousemove",function(a){m=a.data.$.clientY;r.input()}));f.fire("dragstart",{target:a.sender});l.push(f.document.once("mouseup",b,this));g.isInline()||l.push(CKEDITOR.document.once("mouseup",b,this))}}function la(a){var b=
null;a.on("data",function(){var a=this.data.classes,d;if(b!=a){for(d in b)a&&a[d]||this.removeClass(d);for(d in a)this.addClass(d);b=a}})}function ma(a){a.on("data",function(){if(a.wrapper){var b=this.getLabel?this.getLabel():this.editor.lang.widget.label.replace(/%1/,this.pathName||this.element.getName());a.wrapper.setAttribute("role","region");a.wrapper.setAttribute("aria-label",b)}},null,null,9999)}function v(a){a.element.data("cke-widget-data",encodeURIComponent(JSON.stringify(a.data)))}function qa(){function a(){}
function b(a,b,c){return c&&this.checkElement(a)?(a=c.widgets.getByElement(a,!0))&&a.checkStyleActive(this):!1}function c(a){function b(a,c,d){for(var e=a.length,f=0;f<e;){if(c.call(d,a[f],f,a))return a[f];f++}}function c(a){function b(a,c){var d=CKEDITOR.tools.object.keys(a),e=CKEDITOR.tools.object.keys(c);if(d.length!==e.length)return!1;for(var f in a)if(("object"!==typeof a[f]||"object"!==typeof c[f]||!b(a[f],c[f]))&&a[f]!==c[f])return!1;return!0}return function(c){return b(a.getDefinition(),c.getDefinition())}}
var h=a.widget,k;d[h]||(d[h]={});for(var n=0,m=a.group.length;n<m;n++)k=a.group[n],d[h][k]||(d[h][k]=[]),k=d[h][k],b(k,c(a))||k.push(a)}var d={};CKEDITOR.style.addCustomHandler({type:"widget",setup:function(a){this.widget=a.widget;(this.group="string"==typeof a.group?[a.group]:a.group)&&c(this)},apply:function(a){var b;a instanceof CKEDITOR.editor&&this.checkApplicable(a.elementPath(),a)&&(b=a.widgets.focused,this.group&&this.removeStylesFromSameGroup(a),b.applyStyle(this))},remove:function(a){a instanceof
CKEDITOR.editor&&this.checkApplicable(a.elementPath(),a)&&a.widgets.focused.removeStyle(this)},removeStylesFromSameGroup:function(a){var b=!1,c,h;if(!(a instanceof CKEDITOR.editor))return!1;h=a.elementPath();if(this.checkApplicable(h,a))for(var k=0,n=this.group.length;k<n;k++){c=d[this.widget][this.group[k]];for(var m=0;m<c.length;m++)c[m]!==this&&c[m].checkActive(h,a)&&(a.widgets.focused.removeStyle(c[m]),b=!0)}return b},checkActive:function(a,b){return this.checkElementMatch(a.lastElement,0,b)},
checkApplicable:function(a,b){return b instanceof CKEDITOR.editor?this.checkElement(a.lastElement):!1},checkElementMatch:b,checkElementRemovable:b,checkElement:function(a){return h.isDomWidgetWrapper(a)?(a=a.getFirst(h.isDomWidgetElement))&&a.data("widget")==this.widget:!1},buildPreview:function(a){return a||this._.definition.name},toAllowedContentRules:function(a){if(!a)return null;a=a.widgets.registered[this.widget];var b,c={};if(!a)return null;if(a.styleableElements){b=this.getClassesArray();if(!b)return null;
c[a.styleableElements]={classes:b,propertiesOnly:!0};return c}return a.styleToAllowedContentRules?a.styleToAllowedContentRules(this):null},getClassesArray:function(){var a=this._.definition.attributes&&this._.definition.attributes["class"];return a?CKEDITOR.tools.trim(a).split(/\s+/):null},applyToRange:a,removeFromRange:a,applyToObject:a})}CKEDITOR.plugins.add("widget",{requires:"lineutils,clipboard,widgetselection",onLoad:function(){void 0!==CKEDITOR.document.$.querySelectorAll&&(CKEDITOR.addCss('.cke_widget_wrapper{position:relative;outline:none}.cke_widget_inline{display:inline-block}.cke_widget_wrapper:hover\x3e.cke_widget_element{outline:2px solid #ffd25c;cursor:default}.cke_widget_wrapper:hover .cke_widget_editable{outline:2px solid #ffd25c}.cke_widget_wrapper.cke_widget_focused\x3e.cke_widget_element,.cke_widget_wrapper .cke_widget_editable.cke_widget_editable_focused{outline:2px solid #47a4f5}.cke_widget_editable{cursor:text}.cke_widget_drag_handler_container{position:absolute;width:15px;height:0;display:block;opacity:0.75;transition:height 0s 0.2s;line-height:0}.cke_widget_wrapper:hover\x3e.cke_widget_drag_handler_container{height:15px;transition:none}.cke_widget_drag_handler_container:hover{opacity:1}.cke_editable[contenteditable\x3d"false"] .cke_widget_drag_handler_container{display:none;}img.cke_widget_drag_handler{cursor:move;width:15px;height:15px;display:inline-block}.cke_widget_mask{position:absolute;top:0;left:0;width:100%;height:100%;display:block}.cke_widget_partial_mask{position:absolute;display:block}.cke_editable.cke_widget_dragging, .cke_editable.cke_widget_dragging *{cursor:move !important}'),
qa())},beforeInit:function(a){void 0!==CKEDITOR.document.$.querySelectorAll&&(a.widgets=new q(a))},afterInit:function(a){if(void 0!==CKEDITOR.document.$.querySelectorAll){var b=a.widgets.registered,c,d,e;for(d in b)c=b[d],(e=c.button)&&a.ui.addButton&&a.ui.addButton(CKEDITOR.tools.capitalize(c.name,!0),{label:e,command:c.name,toolbar:"insert,10"});ea(a);ha(a.undoManager)}}});q.prototype={MIN_SELECTION_CHECK_INTERVAL:500,add:function(a,b){var c=this.editor;b=CKEDITOR.tools.prototypedCopy(b);b.name=
a;b._=b._||{};c.fire("widgetDefinition",b);b.template&&(b.template=new CKEDITOR.template(b.template));aa(c,b);ba(this,b);this.registered[a]=b;if(b.dialog&&c.plugins.dialog)var d=CKEDITOR.on("dialogDefinition",function(a){a=a.data.definition;var f=a.dialog;a.getMode||f.getName()!==b.dialog||(a.getMode=function(){var a=f.getModel(c);return a&&a instanceof CKEDITOR.plugins.widget&&a.ready?CKEDITOR.dialog.EDITING_MODE:CKEDITOR.dialog.CREATION_MODE});d.removeListener()});return b},addUpcastCallback:function(a){this._.upcastCallbacks.push(a)},
checkSelection:function(){if(this.editor.getSelection()){var a=this.editor.getSelection(),b=a.getSelectedElement(),c=H(this),d;if(b&&(d=this.getByElement(b,!0)))return c.focus(d).select(d).commit();a=a.getRanges()[0];if(!a||a.collapsed)return c.commit();a=new CKEDITOR.dom.walker(a);for(a.evaluator=h.isDomWidgetWrapper;b=a.next();)c.select(this.getByElement(b));c.commit()}},checkWidgets:function(a){this.fire("checkWidgets",CKEDITOR.tools.copy(a||{}))},del:function(a){if(this.focused===a){var b=a.editor,
c=b.createRange(),d;(d=c.moveToClosestEditablePosition(a.wrapper,!0))||(d=c.moveToClosestEditablePosition(a.wrapper,!1));d&&b.getSelection().selectRanges([c])}a.wrapper.remove();this.destroy(a,!0)},destroy:function(a,b){this.widgetHoldingFocusedEditable===a&&p(this,a,null,b);a.destroy(b);delete this.instances[a.id];this.fire("instanceDestroyed",a)},destroyAll:function(a,b){var c,d,e=this.instances;if(b&&!a){d=b.find(".cke_widget_wrapper");for(var e=d.count(),f=0;f<e;++f)(c=this.getByElement(d.getItem(f),
!0))&&this.destroy(c)}else for(d in e)c=e[d],this.destroy(c,a)},finalizeCreation:function(a){(a=a.getFirst())&&h.isDomWidgetWrapper(a)&&(this.editor.insertElement(a),a=this.getByElement(a),a.ready=!0,a.fire("ready"),a.focus())},getByElement:function(){function a(a){return a.is(b)&&a.data("cke-widget-id")}var b={div:1,span:1};return function(b,d){if(!b)return null;var e=a(b);if(!d&&!e){var f=this.editor.editable();do b=b.getParent();while(b&&!b.equals(f)&&!(e=a(b)))}return this.instances[e]||null}}(),
initOn:function(a,b,c){b?"string"==typeof b&&(b=this.registered[b]):b=this.registered[a.data("widget")];if(!b)return null;var d=this.wrapElement(a,b.name);return d?d.hasClass("cke_widget_new")?(a=new h(this,this._.nextId++,a,b,c),a.isInited()?this.instances[a.id]=a:null):this.getByElement(a):null},initOnAll:function(a){a=(a||this.editor.editable()).find(".cke_widget_new");for(var b=[],c,d=a.count();d--;)(c=this.initOn(a.getItem(d).getFirst(h.isDomWidgetElement)))&&b.push(c);return b},onWidget:function(a){var b=
Array.prototype.slice.call(arguments);b.shift();for(var c in this.instances){var d=this.instances[c];d.name==a&&d.on.apply(d,b)}this.on("instanceCreated",function(c){c=c.data;c.name==a&&c.on.apply(c,b)})},parseElementClasses:function(a){if(!a)return null;a=CKEDITOR.tools.trim(a).split(/\s+/);for(var b,c={},d=0;b=a.pop();)-1==b.indexOf("cke_")&&(c[b]=d=1);return d?c:null},wrapElement:function(a,b){var c=null,d,e;if(a instanceof CKEDITOR.dom.element){b=b||a.data("widget");d=this.registered[b];if(!d)return null;
if((c=a.getParent())&&c.type==CKEDITOR.NODE_ELEMENT&&c.data("cke-widget-wrapper"))return c;a.hasAttribute("data-cke-widget-keep-attr")||a.data("cke-widget-keep-attr",a.data("widget")?1:0);a.data("widget",b);(e=E(d,a.getName()))&&y(a);c=new CKEDITOR.dom.element(e?"span":"div",a.getDocument());c.setAttributes(C(e,b));c.data("cke-display-name",d.pathName?d.pathName:a.getName());a.getParent(!0)&&c.replace(a);a.appendTo(c)}else if(a instanceof CKEDITOR.htmlParser.element){b=b||a.attributes["data-widget"];
d=this.registered[b];if(!d)return null;if((c=a.parent)&&c.type==CKEDITOR.NODE_ELEMENT&&c.attributes["data-cke-widget-wrapper"])return c;"data-cke-widget-keep-attr"in a.attributes||(a.attributes["data-cke-widget-keep-attr"]=a.attributes["data-widget"]?1:0);b&&(a.attributes["data-widget"]=b);(e=E(d,a.name))&&y(a);c=new CKEDITOR.htmlParser.element(e?"span":"div",C(e,b));c.attributes["data-cke-display-name"]=d.pathName?d.pathName:a.name;d=a.parent;var f;d&&(f=a.getIndex(),a.remove());c.add(a);d&&D(d,
f,c)}return c},_tests_createEditableFilter:B};CKEDITOR.event.implementOn(q.prototype);h.prototype={addClass:function(a){this.element.addClass(a);this.wrapper.addClass(h.WRAPPER_CLASS_PREFIX+a)},applyStyle:function(a){I(this,a,1)},checkStyleActive:function(a){a=J(a);var b;if(!a)return!1;for(;b=a.pop();)if(!this.hasClass(b))return!1;return!0},destroy:function(a){this.fire("destroy");if(this.editables)for(var b in this.editables)this.destroyEditable(b,a);a||("0"==this.element.data("cke-widget-keep-attr")&&
this.element.removeAttribute("data-widget"),this.element.removeAttributes(["data-cke-widget-data","data-cke-widget-keep-attr"]),this.element.removeClass("cke_widget_element"),this.element.replace(this.wrapper));this.wrapper=null},destroyEditable:function(a,b){var c=this.editables[a],d=!0;c.removeListener("focus",N);c.removeListener("blur",M);this.editor.focusManager.remove(c);if(c.filter){for(var e in this.repository.instances){var f=this.repository.instances[e];f.editables&&(f=f.editables[a])&&f!==
c&&c.filter===f.filter&&(d=!1)}d&&(c.filter.destroy(),(d=this.repository._.filters[this.name])&&delete d[a])}b||(this.repository.destroyAll(!1,c),c.removeClass("cke_widget_editable"),c.removeClass("cke_widget_editable_focused"),c.removeAttributes(["contenteditable","data-cke-widget-editable","data-cke-enter-mode"]));delete this.editables[a]},edit:function(){var a={dialog:this.dialog},b=this;if(!1===this.fire("edit",a)||!a.dialog)return!1;this.editor.openDialog(a.dialog,function(a){var d,e;!1!==b.fire("dialog",
a)&&(d=a.on("show",function(){a.setupContent(b)}),e=a.on("ok",function(){var d,e=b.on("data",function(a){d=1;a.cancel()},null,null,0);b.editor.fire("saveSnapshot");a.commitContent(b);e.removeListener();d&&(b.fire("data",b.data),b.editor.fire("saveSnapshot"))}),a.once("hide",function(){d.removeListener();e.removeListener()}))},b);return!0},getClasses:function(){return this.repository.parseElementClasses(this.element.getAttribute("class"))},getClipboardHtml:function(){var a=this.editor.createRange();
a.setStartBefore(this.wrapper);a.setEndAfter(this.wrapper);return this.editor.editable().getHtmlFromRange(a).getHtml()},hasClass:function(a){return this.element.hasClass(a)},initEditable:function(a,b){var c=this._findOneNotNested(b.selector);return c&&c.is(CKEDITOR.dtd.$editable)?(c=new t(this.editor,c,{filter:B.call(this.repository,this.name,a,b)}),this.editables[a]=c,c.setAttributes({contenteditable:"true","data-cke-widget-editable":a,"data-cke-enter-mode":c.enterMode}),c.filter&&c.data("cke-filter",
c.filter.id),c.addClass("cke_widget_editable"),c.removeClass("cke_widget_editable_focused"),b.pathName&&c.data("cke-display-name",b.pathName),this.editor.focusManager.add(c),c.on("focus",N,this),CKEDITOR.env.ie&&c.on("blur",M,this),c._.initialSetData=!0,c.setData(c.getHtml()),!0):!1},_findOneNotNested:function(a){a=this.wrapper.find(a);for(var b,c,d=0;d<a.count();d++)if(b=a.getItem(d),c=b.getAscendant(h.isDomWidgetWrapper),this.wrapper.equals(c))return b;return null},isInited:function(){return!(!this.wrapper||
!this.inited)},isReady:function(){return this.isInited()&&this.ready},focus:function(){var a=this.editor.getSelection();if(a){var b=this.editor.checkDirty();a.fake(this.wrapper);!b&&this.editor.resetDirty()}this.editor.focus()},refreshMask:function(){P(this)},refreshParts:function(a){O(this,"undefined"!==typeof a?a:!0)},removeClass:function(a){this.element.removeClass(a);this.wrapper.removeClass(h.WRAPPER_CLASS_PREFIX+a)},removeStyle:function(a){I(this,a,0)},setData:function(a,b){var c=this.data,
d=0;if("string"==typeof a)c[a]!==b&&(c[a]=b,d=1);else{var e=a;for(a in e)c[a]!==e[a]&&(d=1,c[a]=e[a])}d&&this.dataReady&&(v(this),this.fire("data",c));return this},setFocused:function(a){this.wrapper[a?"addClass":"removeClass"]("cke_widget_focused");this.fire(a?"focus":"blur");return this},setSelected:function(a){this.wrapper[a?"addClass":"removeClass"]("cke_widget_selected");this.fire(a?"select":"deselect");return this},updateDragHandlerPosition:function(){var a=this.editor,b=this.element.$,c=this._.dragHandlerOffset,
b={x:b.offsetLeft,y:b.offsetTop-15};c&&b.x==c.x&&b.y==c.y||(c=a.checkDirty(),a.fire("lockSnapshot"),this.dragHandlerContainer.setStyles({top:b.y+"px",left:b.x+"px"}),this.dragHandlerContainer.removeStyle("display"),a.fire("unlockSnapshot"),!c&&a.resetDirty(),this._.dragHandlerOffset=b)}};CKEDITOR.event.implementOn(h.prototype);h.getNestedEditable=function(a,b){return!b||b.equals(a)?null:h.isDomNestedEditable(b)?b:h.getNestedEditable(a,b.getParent())};h.isDomDragHandler=function(a){return a.type==
CKEDITOR.NODE_ELEMENT&&a.hasAttribute("data-cke-widget-drag-handler")};h.isDomDragHandlerContainer=function(a){return a.type==CKEDITOR.NODE_ELEMENT&&a.hasClass("cke_widget_drag_handler_container")};h.isDomNestedEditable=function(a){return a.type==CKEDITOR.NODE_ELEMENT&&a.hasAttribute("data-cke-widget-editable")};h.isDomWidgetElement=function(a){return a.type==CKEDITOR.NODE_ELEMENT&&a.hasAttribute("data-widget")};h.isDomWidgetWrapper=function(a){return a.type==CKEDITOR.NODE_ELEMENT&&a.hasAttribute("data-cke-widget-wrapper")};
h.isDomWidget=function(a){return a?this.isDomWidgetWrapper(a)||this.isDomWidgetElement(a):!1};h.isParserWidgetElement=function(a){return a.type==CKEDITOR.NODE_ELEMENT&&!!a.attributes["data-widget"]};h.isParserWidgetWrapper=function(a){return a.type==CKEDITOR.NODE_ELEMENT&&!!a.attributes["data-cke-widget-wrapper"]};h.WRAPPER_CLASS_PREFIX="cke_widget_wrapper_";t.prototype=CKEDITOR.tools.extend(CKEDITOR.tools.prototypedCopy(CKEDITOR.dom.element.prototype),{setData:function(a){this._.initialSetData||
this.editor.widgets.destroyAll(!1,this);this._.initialSetData=!1;a=this.editor.dataProcessor.unprotectRealComments(a);a=this.editor.dataProcessor.unprotectSource(a);a=this.editor.dataProcessor.toHtml(a,{context:this.getName(),filter:this.filter,enterMode:this.enterMode});this.setHtml(a);this.editor.widgets.initOnAll(this)},getData:function(){return this.editor.dataProcessor.toDataFormat(this.getHtml(),{context:this.getName(),filter:this.filter,enterMode:this.enterMode})}});var ga=/^(?:<(?:div|span)(?: data-cke-temp="1")?(?: id="cke_copybin")?(?: data-cke-temp="1")?>)?(?:<(?:div|span)(?: style="[^"]+")?>)?<span [^>]*data-cke-copybin-start="1"[^>]*>.?<\/span>([\s\S]+)<span [^>]*data-cke-copybin-end="1"[^>]*>.?<\/span>(?:<\/(?:div|span)>)?(?:<\/(?:div|span)>)?$/i,
Q={37:1,38:1,39:1,40:1,8:1,46:1};Q[CKEDITOR.SHIFT+121]=1;var u=CKEDITOR.tools.createClass({$:function(a,b){this._.createCopyBin(a,b);this._.createListeners(b)},_:{createCopyBin:function(a){var b=a.document,c=CKEDITOR.env.edge&&16<=CKEDITOR.env.version,d=!a.blockless&&!CKEDITOR.env.ie||c?"div":"span",c=b.createElement(d),b=b.createElement(d);b.setAttributes({id:"cke_copybin","data-cke-temp":"1"});c.setStyles({position:"absolute",width:"1px",height:"1px",overflow:"hidden"});c.setStyle("ltr"==a.config.contentsLangDirection?
"left":"right","-5000px");this.editor=a;this.copyBin=c;this.container=b},createListeners:function(a){a&&(a.beforeDestroy&&(this.beforeDestroy=a.beforeDestroy),a.afterDestroy&&(this.afterDestroy=a.afterDestroy))}},proto:{handle:function(a){var b=this.copyBin,c=this.editor,d=this.container,e=CKEDITOR.env.ie&&9>CKEDITOR.env.version,f=c.document.getDocumentElement().$,g=c.createRange(),h=this,k=CKEDITOR.env.mac&&CKEDITOR.env.webkit,n=k?100:0,m=window.requestAnimationFrame&&!k?requestAnimationFrame:setTimeout,
p,r,q;b.setHtml('\x3cspan data-cke-copybin-start\x3d"1"\x3e​\x3c/span\x3e'+a+'\x3cspan data-cke-copybin-end\x3d"1"\x3e​\x3c/span\x3e');c.fire("lockSnapshot");d.append(b);c.editable().append(d);p=c.on("selectionChange",K,null,null,0);r=c.widgets.on("checkSelection",K,null,null,0);e&&(q=f.scrollTop);g.selectNodeContents(b);g.select();e&&(f.scrollTop=q);return new CKEDITOR.tools.promise(function(a){m(function(){h.beforeDestroy&&h.beforeDestroy();d.remove();p.removeListener();r.removeListener();c.fire("unlockSnapshot");
h.afterDestroy&&h.afterDestroy();a()},n)})}},statics:{hasCopyBin:function(a){return!!u.getCopyBin(a)},getCopyBin:function(a){return a.document.getById("cke_copybin")}}});CKEDITOR.plugins.widget=h;h.repository=q;h.nestedEditable=t})();CKEDITOR.config.widget_keystrokeInsertLineBefore=CKEDITOR.SHIFT+CKEDITOR.ALT+13;CKEDITOR.config.widget_keystrokeInsertLineAfter=CKEDITOR.SHIFT+13;(function(){CKEDITOR.plugins.add("mathjax",{requires:"widget,dialog",icons:"mathjax",isSupportedEnvironment:function(){return!CKEDITOR.env.ie||8<CKEDITOR.env.version},init:function(b){var c=b.config.mathJaxClass||"math-tex";b.config.mathJaxLib||CKEDITOR.error("mathjax-no-config");b.widgets.add("mathjax",{inline:!0,dialog:"mathjax",button:b.lang.mathjax.button,mask:!0,allowedContent:"span(!"+c+")",styleToAllowedContentRules:function(a){a=a.getClassesArray();if(!a)return null;a.push("!"+c);return"span("+
a.join(",")+")"},pathName:b.lang.mathjax.pathName,template:'\x3cspan class\x3d"'+c+'" style\x3d"display:inline-block" data-cke-survive\x3d1\x3e\x3c/span\x3e',parts:{span:"span"},defaults:{math:"\\(x \x3d {-b \\pm \\sqrt{b^2-4ac} \\over 2a}\\)"},init:function(){var a=this.parts.span.getChild(0);a&&a.type==CKEDITOR.NODE_ELEMENT&&a.is("iframe")||(a=new CKEDITOR.dom.element("iframe"),a.setAttributes({style:"border:0;width:0;height:0",scrolling:"no",frameborder:0,allowTransparency:!0,src:CKEDITOR.plugins.mathjax.fixSrc}),
this.parts.span.append(a));this.once("ready",function(){CKEDITOR.env.ie&&a.setAttribute("src",CKEDITOR.plugins.mathjax.fixSrc);this.frameWrapper=new CKEDITOR.plugins.mathjax.frameWrapper(a,b);this.frameWrapper.setValue(this.data.math)})},data:function(){this.frameWrapper&&this.frameWrapper.setValue(this.data.math)},upcast:function(a,b){if("span"==a.name&&a.hasClass(c)&&!(1<a.children.length||a.children[0].type!=CKEDITOR.NODE_TEXT)){b.math=CKEDITOR.tools.htmlDecode(a.children[0].value);var d=a.attributes;
d.style=d.style?d.style+";display:inline-block":"display:inline-block";d["data-cke-survive"]=1;a.children[0].remove();return a}},downcast:function(a){a.children[0].replaceWith(new CKEDITOR.htmlParser.text(CKEDITOR.tools.htmlEncode(this.data.math)));var b=a.attributes;b.style=b.style.replace(/display:\s?inline-block;?\s?/,"");""===b.style&&delete b.style;return a}});CKEDITOR.dialog.add("mathjax",this.path+"dialogs/mathjax.js");b.on("contentPreview",function(a){a.data.dataValue=a.data.dataValue.replace(/<\/head>/,
'\x3cscript src\x3d"'+CKEDITOR.getUrl(b.config.mathJaxLib)+'"\x3e\x3c/script\x3e\x3c/head\x3e')});b.on("paste",function(a){a.data.dataValue=a.data.dataValue.replace(new RegExp("\x3cspan[^\x3e]*?"+c+".*?\x3c/span\x3e","ig"),function(a){return a.replace(/(<iframe.*?\/iframe>)/i,"")})})}});CKEDITOR.plugins.mathjax={};CKEDITOR.plugins.mathjax.fixSrc=CKEDITOR.env.gecko?"javascript:true":CKEDITOR.env.ie?"javascript:void((function(){"+encodeURIComponent("document.open();("+CKEDITOR.tools.fixDomain+")();document.close();")+
"})())":"javascript:void(0)";CKEDITOR.plugins.mathjax.loadingIcon=CKEDITOR.plugins.get("mathjax").path+"images/loader.gif";CKEDITOR.plugins.mathjax.copyStyles=function(b,c){for(var a="color font-family font-style font-weight font-variant font-size".split(" "),e=0;e<a.length;e++){var d=a[e],g=b.getComputedStyle(d);g&&c.setStyle(d,g)}};CKEDITOR.plugins.mathjax.trim=function(b){var c=b.indexOf("\\(")+2,a=b.lastIndexOf("\\)");return b.substring(c,a)};CKEDITOR.plugins.mathjax.frameWrapper=CKEDITOR.env.ie&&
8==CKEDITOR.env.version?function(b,c){b.getFrameDocument().write('\x3c!DOCTYPE html\x3e\x3chtml\x3e\x3chead\x3e\x3cmeta charset\x3d"utf-8"\x3e\x3c/head\x3e\x3cbody style\x3d"padding:0;margin:0;background:transparent;overflow:hidden"\x3e\x3cspan style\x3d"white-space:nowrap;" id\x3d"tex"\x3e\x3c/span\x3e\x3c/body\x3e\x3c/html\x3e');return{setValue:function(a){var e=b.getFrameDocument(),d=e.getById("tex");d.setHtml(CKEDITOR.plugins.mathjax.trim(CKEDITOR.tools.htmlEncode(a)));CKEDITOR.plugins.mathjax.copyStyles(b,
d);c.fire("lockSnapshot");b.setStyles({width:Math.min(250,d.$.offsetWidth)+"px",height:e.$.body.offsetHeight+"px",display:"inline","vertical-align":"middle"});c.fire("unlockSnapshot")}}}:function(b,c){function a(){f=b.getFrameDocument();f.getById("preview")||(CKEDITOR.env.ie&&b.removeAttribute("src"),f.write('\x3c!DOCTYPE html\x3e\x3chtml\x3e\x3chead\x3e\x3cmeta charset\x3d"utf-8"\x3e\x3cscript type\x3d"text/x-mathjax-config"\x3eMathJax.Hub.Config( {showMathMenu: false,messageStyle: "none"} );function getCKE() {if ( typeof window.parent.CKEDITOR \x3d\x3d \'object\' ) {return window.parent.CKEDITOR;} else {return window.parent.parent.CKEDITOR;}}function update() {MathJax.Hub.Queue([ \'Typeset\', MathJax.Hub, this.buffer ],function() {getCKE().tools.callFunction( '+
n+" );});}MathJax.Hub.Queue( function() {getCKE().tools.callFunction("+p+');} );\x3c/script\x3e\x3cscript src\x3d"'+c.config.mathJaxLib+'"\x3e\x3c/script\x3e\x3c/head\x3e\x3cbody style\x3d"padding:0;margin:0;background:transparent;overflow:hidden"\x3e\x3cspan id\x3d"preview"\x3e\x3c/span\x3e\x3cspan id\x3d"buffer" style\x3d"display:none"\x3e\x3c/span\x3e\x3c/body\x3e\x3c/html\x3e'))}function e(){m=!0;h=k;c.fire("lockSnapshot");d.setHtml(h);g.setHtml("\x3cimg src\x3d"+CKEDITOR.plugins.mathjax.loadingIcon+
" alt\x3d"+c.lang.mathjax.loading+"\x3e");b.setStyles({height:"16px",width:"16px",display:"inline","vertical-align":"middle"});c.fire("unlockSnapshot");f.getWindow().$.update(h)}var d,g,h,k,f=b.getFrameDocument(),l=!1,m=!1,p=CKEDITOR.tools.addFunction(function(){g=f.getById("preview");d=f.getById("buffer");l=!0;k&&e();CKEDITOR.fire("mathJaxLoaded",b)}),n=CKEDITOR.tools.addFunction(function(){CKEDITOR.plugins.mathjax.copyStyles(b,g);g.setHtml(d.getHtml());c.fire("lockSnapshot");b.setStyles({height:0,
width:0});var a=Math.max(f.$.body.offsetHeight,f.$.documentElement.offsetHeight),l=Math.max(g.$.offsetWidth,f.$.body.scrollWidth);b.setStyles({height:a+"px",width:l+"px"});c.fire("unlockSnapshot");CKEDITOR.fire("mathJaxUpdateDone",b);h!=k?e():m=!1});b.on("load",a);a();return{setValue:function(a){k=CKEDITOR.tools.htmlEncode(a);l&&!m&&e()}}}})();(function(){CKEDITOR.plugins.add("sourcearea",{init:function(a){function d(){var a=e&&this.equals(CKEDITOR.document.getActive());this.hide();this.setStyle("height",this.getParent().$.clientHeight+"px");this.setStyle("width",this.getParent().$.clientWidth+"px");this.show();a&&this.focus()}if(a.elementMode!=CKEDITOR.ELEMENT_MODE_INLINE){var f=CKEDITOR.plugins.sourcearea;a.addMode("source",function(e){var b=a.ui.space("contents").getDocument().createElement("textarea");b.setStyles(CKEDITOR.tools.extend({width:CKEDITOR.env.ie7Compat?
"99%":"100%",height:"100%",resize:"none",outline:"none","text-align":"left"},CKEDITOR.tools.cssVendorPrefix("tab-size",a.config.sourceAreaTabSize||4)));b.setAttribute("dir","ltr");b.addClass("cke_source").addClass("cke_reset").addClass("cke_enable_context_menu");a.ui.space("contents").append(b);b=a.editable(new c(a,b));b.setData(a.getData(1));CKEDITOR.env.ie&&(b.attachListener(a,"resize",d,b),b.attachListener(CKEDITOR.document.getWindow(),"resize",d,b),CKEDITOR.tools.setTimeout(d,0,b));a.fire("ariaWidget",
this);e()});a.addCommand("source",f.commands.source);a.ui.addButton&&a.ui.addButton("Source",{isToggle:!0,label:a.lang.sourcearea.toolbar,command:"source",toolbar:"mode,10"});a.on("mode",function(){a.getCommand("source").setState("source"==a.mode?CKEDITOR.TRISTATE_ON:CKEDITOR.TRISTATE_OFF)});var e=CKEDITOR.env.ie&&9==CKEDITOR.env.version}}});var c=CKEDITOR.tools.createClass({base:CKEDITOR.editable,proto:{setData:function(a){this.setValue(a);this.status="ready";this.editor.fire("dataReady")},getData:function(){return this.getValue()},
insertHtml:function(){},insertElement:function(){},insertText:function(){},setReadOnly:function(a){this[(a?"set":"remove")+"Attribute"]("readOnly","readonly")},detach:function(){c.baseProto.detach.call(this);this.clearCustomData();this.remove()}}})})();
CKEDITOR.plugins.sourcearea={commands:{source:{modes:{wysiwyg:1,source:1},editorFocus:!1,readOnly:1,exec:function(c){"wysiwyg"==c.mode&&c.fire("saveSnapshot");c.getCommand("source").setState(CKEDITOR.TRISTATE_DISABLED);c.setMode("source"==c.mode?"wysiwyg":"source")},canUndo:!1}}};CKEDITOR.config.plugins='dialogui,dialog,about,basicstyles,notification,button,toolbar,clipboard,enterkey,entities,floatingspace,wysiwygarea,indent,indentlist,list,undo,specialchar,lineutils,widgetselection,widget,mathjax,sourcearea';CKEDITOR.config.skin='n1theme';(function() {var setIcons = function(icons, strip) {var path = CKEDITOR.getUrl( 'plugins/' + strip );icons = icons.split( ',' );for ( var i = 0; i < icons.length; i++ )CKEDITOR.skin.icons[ icons[ i ] ] = { path: path, offset: -icons[ ++i ], bgsize : icons[ ++i ] };};if (CKEDITOR.env.hidpi) setIcons('about,0,,bold,24,,italic,48,,strike,72,,subscript,96,,superscript,120,,underline,144,,copy-rtl,168,,copy,192,,cut-rtl,216,,cut,240,,paste-rtl,264,,paste,288,,indent-rtl,312,,indent,336,,outdent-rtl,360,,outdent,384,,bulletedlist-rtl,408,,bulletedlist,432,,numberedlist-rtl,456,,numberedlist,480,,redo-rtl,504,,redo,528,,undo-rtl,552,,undo,576,,specialchar,600,,mathjax,624,,source-rtl,648,,source,672,','icons_hidpi.png');else setIcons('about,0,auto,bold,24,auto,italic,48,auto,strike,72,auto,subscript,96,auto,superscript,120,auto,underline,144,auto,copy-rtl,168,auto,copy,192,auto,cut-rtl,216,auto,cut,240,auto,paste-rtl,264,auto,paste,288,auto,indent-rtl,312,auto,indent,336,auto,outdent-rtl,360,auto,outdent,384,auto,bulletedlist-rtl,408,auto,bulletedlist,432,auto,numberedlist-rtl,456,auto,numberedlist,480,auto,redo-rtl,504,auto,redo,528,auto,undo-rtl,552,auto,undo,576,auto,specialchar,600,auto,mathjax,624,auto,source-rtl,648,auto,source,672,auto','icons.png');})();CKEDITOR.lang.languages={"en":1};}());