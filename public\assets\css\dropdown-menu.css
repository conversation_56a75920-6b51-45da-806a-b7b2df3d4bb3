/**
 * Dropdown Menu Styling for Madrasa Admin Panel
 * Ensuring consistent styling with theme colors
 */

/* Main dropdown styling */
.navbar .navbar-menu-wrapper .navbar-nav .nav-item.nav-profile .dropdown-menu {
  border: none;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  padding: 0;
  min-width: 300px; /* Further increased width for better text accommodation */
  margin-top: 12px;
  background-color: #fff;
  overflow: hidden; /* Prevent content overflow */
  box-sizing: border-box; /* Ensure proper box sizing */
}

/* Dropdown header section */
.navbar .navbar-menu-wrapper .navbar-nav .nav-item.nav-profile .dropdown-menu .dropdown-header {
  padding: 25px 25px; /* Increased padding for better spacing */
  display: flex;
  align-items: center;
  background: linear-gradient(45deg, rgba(23, 119, 117, 0.1), rgba(40, 210, 191, 0.1));
  border-radius: 8px 8px 0 0;
  min-height: 90px; /* Ensure minimum height */
}

/* Profile image in dropdown header */
.navbar .navbar-menu-wrapper .navbar-nav .nav-item.nav-profile .dropdown-menu .dropdown-header .dropdown-profile-img {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 15px;
  border: 2px solid rgba(23, 119, 117, 0.3);
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0; /* Prevent shrinking */
}

.navbar .navbar-menu-wrapper .navbar-nav .nav-item.nav-profile .dropdown-menu .dropdown-header .dropdown-profile-img img {
  width: 70%;
  height: auto;
  object-fit: contain;
  display: block;
}

/* Profile info in dropdown header */
.navbar .navbar-menu-wrapper .navbar-nav .nav-item.nav-profile .dropdown-menu .dropdown-header .dropdown-profile-info {
  flex: 1;
  min-width: 0; /* Allow text truncation */
}

.navbar .navbar-menu-wrapper .navbar-nav .nav-item.nav-profile .dropdown-menu .dropdown-header .dropdown-profile-name {
  margin: 0;
  font-size: 18px; /* Increased font size */
  font-weight: 600;
  color: #09322F;
  line-height: 1.4;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.navbar .navbar-menu-wrapper .navbar-nav .nav-item.nav-profile .dropdown-menu .dropdown-header .dropdown-profile-role {
  margin: 4px 0 0 0;
  font-size: 15px; /* Increased font size */
  color: #177775;
  opacity: 0.9;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Dropdown item styling */
.navbar .navbar-menu-wrapper .navbar-nav .nav-item.nav-profile .dropdown-menu .dropdown-item {
  padding: 16px 25px; /* Increased horizontal padding */
  color: #09322F;
  font-weight: 500;
  font-size: 16px; /* Increased font size */
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  min-height: 50px; /* Ensure minimum height for items */
  white-space: nowrap; /* Prevent text wrapping issues */
  overflow: hidden; /* Prevent content overflow */
  text-overflow: ellipsis; /* Add ellipsis for long text */
  width: 100%; /* Ensure full width */
}

/* Dropdown item hover effect */
.navbar .navbar-menu-wrapper .navbar-nav .nav-item.nav-profile .dropdown-menu .dropdown-item:hover {
  background: linear-gradient(45deg, rgba(23, 119, 117, 0.05), rgba(40, 210, 191, 0.05));
  color: #177775;
}

/* Dropdown item active state */
.navbar .navbar-menu-wrapper .navbar-nav .nav-item.nav-profile .dropdown-menu .dropdown-item:active {
  background: linear-gradient(45deg, rgba(23, 119, 117, 0.1), rgba(40, 210, 191, 0.1));
}

/* Icon styling in dropdown */
.navbar .navbar-menu-wrapper .navbar-nav .nav-item.nav-profile .dropdown-menu .dropdown-item i {
  margin-right: 15px;
  font-size: 20px; /* Increased icon size */
  width: 24px; /* Adjusted width */
  text-align: center;
  color: #177775;
  flex-shrink: 0; /* Prevent icon from shrinking */
}

/* Update profile icon */
.navbar .navbar-menu-wrapper .navbar-nav .nav-item.nav-profile .dropdown-menu .dropdown-item i.fa-user {
  color: #177775;
}

/* Change password icon */
.navbar .navbar-menu-wrapper .navbar-nav .nav-item.nav-profile .dropdown-menu .dropdown-item i.fa-refresh {
  color: #28D2BF;
}

/* Signout icon */
.navbar .navbar-menu-wrapper .navbar-nav .nav-item.nav-profile .dropdown-menu .dropdown-item i.fa-sign-out {
  color: #177775;
}

/* Dropdown divider */
.navbar .navbar-menu-wrapper .navbar-nav .nav-item.nav-profile .dropdown-menu .dropdown-divider {
  margin: 0;
  border-top: 1px solid rgba(23, 119, 117, 0.1);
  margin: 0 15px; /* Increased horizontal margin */
}

/* Dropdown menu arrow/pointer */
.navbar .navbar-menu-wrapper .navbar-nav .nav-item.nav-profile .dropdown-menu:before {
  content: "";
  position: absolute;
  top: -8px;
  right: 25px;
  border-width: 0 8px 8px;
  border-style: solid;
  border-color: transparent transparent white;
  filter: drop-shadow(0 -2px 2px rgba(0, 0, 0, 0.03));
}

/* RTL support */
[dir="rtl"] .navbar .navbar-menu-wrapper .navbar-nav .nav-item.nav-profile .dropdown-menu:before {
  right: auto;
  left: 25px;
}

[dir="rtl"] .navbar .navbar-menu-wrapper .navbar-nav .nav-item.nav-profile .dropdown-menu .dropdown-item i {
  margin-right: 0;
  margin-left: 15px;
}

[dir="rtl"] .navbar .navbar-menu-wrapper .navbar-nav .nav-item.nav-profile .dropdown-menu .dropdown-header .dropdown-profile-img {
  margin-right: 0;
  margin-left: 15px;
}

[dir="rtl"] .dropdown-item {
  text-align: right;
}

/* Animation for dropdown */
.navbar .navbar-menu-wrapper .navbar-nav .nav-item.nav-profile .dropdown-menu {
  transform-origin: top right;
  animation: dropdown-fade-in 0.2s ease forwards;
}

[dir="rtl"] .navbar .navbar-menu-wrapper .navbar-nav .nav-item.nav-profile .dropdown-menu {
  transform-origin: top left;
}

@keyframes dropdown-fade-in {
  0% {
    opacity: 0;
    transform: translateY(-10px) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}