﻿/**
 * @license Copyright (c) 2003-2023, CKSource Holding sp. z o.o. All rights reserved.
 * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-oss-license/
 */

/**
 * This file was added automatically by CKEditor builder.
 * You may re-use it at any time to build CKEditor again.
 *
 * If you would like to build CKEditor online again
 * (for example to upgrade), visit one the following links:
 *
 * (1) https://ckeditor.com/cke4/builder
 *     Visit online builder to build CKEditor from scratch.
 *
 * (2) https://ckeditor.com/cke4/builder/01eb8d0b0592cf43ffedae27976d6318
 *     Visit online builder to build CKEditor, starting with the same setup as before.
 *
 * (3) https://ckeditor.com/cke4/builder/download/01eb8d0b0592cf43ffedae27976d6318
 *     Straight download link to the latest version of CKEditor (Optimized) with the same setup as before.
 *
 * NOTE:
 *    This file is not used by CKEditor, you may remove it.
 *    Changing this file will not change your CKEditor configuration.
 */

var CKBUILDER_CONFIG = {
	skin: 'n1theme',
	preset: 'basic',
	ignore: [
		'.DS_Store',
		'.bender',
		'.editorconfig',
		'.gitattributes',
		'.gitignore',
		'.idea',
		'.jscsrc',
		'.jshintignore',
		'.jshintrc',
		'.mailmap',
		'.npm',
		'.nvmrc',
		'.travis.yml',
		'bender-err.log',
		'bender-out.log',
		'bender.ci.js',
		'bender.js',
		'dev',
		'gruntfile.js',
		'less',
		'node_modules',
		'package-lock.json',
		'package.json',
		'tests'
	],
	plugins : {
		'about' : 1,
		'basicstyles' : 1,
		'clipboard' : 1,
		'enterkey' : 1,
		'entities' : 1,
		'floatingspace' : 1,
		'indentlist' : 1,
		'list' : 1,
		'mathjax' : 1,
		'sourcearea' : 1,
		'specialchar' : 1,
		'toolbar' : 1,
		'undo' : 1,
		'wysiwygarea' : 1
	},
	languages : {
		'en' : 1
	}
};