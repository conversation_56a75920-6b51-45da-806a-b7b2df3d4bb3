@php
    $lang = Session::get('language');
    $isRtl = $lang && $lang->is_rtl ? true : false;
@endphp
<link rel="stylesheet" href="{{ asset('/assets/css/vendor.bundle.base.css') }}" async>

<link rel="stylesheet" href="{{ asset('/assets/fonts/font-awesome.min.css') }}" async/>
<link rel="stylesheet" href="{{ asset('/assets/css/font-awesome-compat.css') }}" async/>
<link rel="stylesheet" href="{{ asset('/assets/select2/select2.min.css') }}" async>
<link rel="stylesheet" href="{{ asset('/assets/jquery-toast-plugin/jquery.toast.min.css') }}">
<link rel="stylesheet" href="{{ asset('/assets/color-picker/color.min.css') }}" async>

<!-- Load the base styles first -->
<link rel="stylesheet" href="{{ asset('/assets/css/style.css') }}">

<!-- Then conditionally load RTL styles if needed -->
@if ($isRtl)
<link rel="stylesheet" href="{{ asset('/assets/css/rtl.css') }}">
<link rel="stylesheet" href="{{ asset('/assets/css/arabic-enhancements.css') }}">
@endif

<!-- Common styling components -->
<link rel="stylesheet" href="{{ asset('/assets/css/theme-colors.css') }}">
<link rel="stylesheet" href="{{ asset('/assets/css/icon-enhancements.css') }}">
<link rel="stylesheet" href="{{ asset('/assets/css/alert-theme.css') }}">
<link rel="stylesheet" href="{{ asset('/assets/css/navbar-profile-fix.css') }}">
<link rel="stylesheet" href="{{ asset('/assets/css/dropdown-menu.css') }}">
<link rel="stylesheet" href="{{ asset('/assets/css/language-dropdown.css') }}">
<link rel="stylesheet" href="{{ asset('/assets/css/datepicker.min.css') }}" async>
<link rel="stylesheet" href="{{ asset('/assets/css/daterangepicker.css') }}">
<link rel="stylesheet" href="{{ asset('/assets/css/ekko-lightbox.css') }}">

<link rel="stylesheet" href="{{ asset('/assets/bootstrap-table/bootstrap-table.min.css') }}">
<link rel="stylesheet" href="{{ asset('/assets/bootstrap-table/fixed-columns.min.css') }}">
<link rel="stylesheet" href="{{ asset('/assets/bootstrap-table/reorder-rows.css') }}">


{{-- <link rel="shortcut icon" href="{{asset(config('global.LOGO_SM')) }}" /> --}}
<link rel="shortcut icon" href="{{ url(Storage::url(env('FAVICON'))) }}"/>

@php
    try {
        $theme_color = getSettings('theme_color');
        $theme_color = isset($theme_color['theme_color']) ? $theme_color['theme_color'] : '#4e73df';
    } catch (Exception $e) {
        $theme_color = '#4e73df'; // Default theme color
    }
    
    // Ensure we have a valid theme color
    if (empty($theme_color)) {
        $theme_color = '#4e73df';
    }
@endphp
@php
    try {
        $login_image = getSettings('login_image');
        if($login_image!= null && isset($login_image['login_image'])){
            $path = $login_image['login_image'];
            $login_image = url(Storage::url($path));
        }
        else {
            $login_image = url(Storage::url('madrasa.jpg'));
        }
    } catch (Exception $e) {
        $login_image = url(Storage::url('madrasa.jpg')); // Default login image
    }
    
    // Ensure we have a valid login image
    if (empty($login_image)) {
        $login_image = url(Storage::url('madrasa.jpg'));
    }
@endphp
<style>
    :root {
        --theme-color: <?=$theme_color ?>;
        --image-url: url(<?=$login_image ?>);
    }
</style>
<script>
    var baseUrl = "{{ URL::to('/') }}";
    const onErrorImage = (e) => {
        // Set fallback image immediately to prevent flickering
        e.target.src = "{{ asset('/logo/madrasa-favicon.png') }}";
        e.target.classList.add('error-profile-fallback');
    };
    
    // Ensure profile images load correctly
    document.addEventListener('DOMContentLoaded', function() {
        // Pre-load the fallback image to ensure it's in cache
        const img = new Image();
        img.src = "{{ asset('/logo/madrasa-favicon.png') }}";
        
        // Add event listeners for language switcher links
        document.querySelectorAll('.language-dropdown .dropdown-item').forEach(function(link) {
            link.addEventListener('click', function(e) {
                // Add a small delay to allow the server to process the language change
                setTimeout(function() {
                    window.location.reload();
                }, 100);
            });
        });
    });
</script>