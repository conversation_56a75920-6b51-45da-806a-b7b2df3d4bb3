# Frontend Structure Documentation

## Overview
This document provides a comprehensive overview of the frontend structure of the Madrasa Admin Panel application. The frontend is built using Laravel Blade templates with a combination of HTML, CSS, JavaScript, and Vue.js components.

## File Structure

### Views (Blade Templates)
- **Main Layouts**: `resources/views/layouts/`
  - `app.blade.php` - Basic HTML structure (unused)
  - `master.blade.php` - Main layout template
  - `header.blade.php` - Top navigation bar
  - `sidebar.blade.php` - Side navigation menu
  - `footer.blade.php` - Footer section
  - `include.blade.php` - CSS includes and theme settings
  - `footer_js.blade.php` - JavaScript includes

- **Pages**: `resources/views/`
  - `dashboard.blade.php` - Main dashboard page
  - `home.blade.php` - Home page
  - Module-specific directories (announcement, assignment, attendance, etc.)

### CSS/SCSS
- **Source Files**: `resources/sass/`
  - `app.scss` - Main SCSS file
  - `_variables.scss` - SCSS variables

- **Compiled Assets**: `public/assets/css/`
  - `style.css` - Main stylesheet
  - `rtl.css` - RTL-specific styles
  - Vendor stylesheets in various plugin directories

### JavaScript
- **Source Files**: `resources/js/`
  - `app.js` - Main JavaScript entry point
  - `bootstrap.js` - Bootstrap and axios configuration
  - `components/` - Vue.js components directory

- **Compiled Assets**: `public/assets/js/`
  - Various JavaScript libraries and plugins

### Language Files
- **JSON Translations**: `resources/lang/`
  - `EN.json`, `HN.json`, `UR.json`, `guj.json` - Language translation files
  - `en/` - Laravel default language files

## Component Hierarchy

```
master.blade.php
├── include.blade.php (CSS)
├── header.blade.php
├── sidebar.blade.php
├── [Page Content]
├── footer.blade.php
└── footer_js.blade.php (JavaScript)
```

## Key Features

### Global vs Page-Specific Files
- **Global**: Layout files, main CSS/JS files, language files
- **Page-Specific**: Individual module Blade templates in their respective directories

### Shared Components
- Navigation components (header, sidebar)
- Reusable UI elements (modals, forms, tables)
- Common JavaScript utilities

### Frontend Frameworks and Libraries
- **Laravel Blade** - Template engine
- **Vue.js** - Reactive components
- **Bootstrap** - CSS framework
- **jQuery** - DOM manipulation
- **Various Plugins** - Charts, tables, forms, etc.