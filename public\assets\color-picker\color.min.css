/** * asColorPicker v0.4.4 * https://github.com/amazingSurge/jquery-asColorPicker * * Copyright (c) amazingSurge * Released under the LGPL-3.0 license */
.asColorPicker-wrap{
    position:relative;
    display:inline-block
}
.asColorPicker_hideInput,.asColorPicker_hideInput .asColorPicker-clear{
    display:none
}
.asColorPicker-dropdown{
    position:absolute;
    z-index:9999;
    display:none;
    -webkit-user-select:none;
    -moz-user-select:none;
    -ms-user-select:none;
    user-select:none
}
.asColorPicker-dropdown *{
    padding:0;
    margin:0
}
.asColorPicker_open{
    display:block
}
.asColorPicker-mask{
    position:fixed;
    top:0;
    left:0;
    z-index:9998;
    width:100%;
    height:100%
}
.asColorPicker-trigger{
    position:relative;
    display:inline-block;
    /* width:18px; */
    height:20px;
    cursor:pointer;
    background-image:url(images/transparent.png)
}
.asColorPicker-trigger span{
    display:inline-block;
    width:100%;
    height:100%
}
.asColorPicker-input,.asColorPicker-trigger{
    vertical-align:middle
}
.asColorPicker-clear{
    position:absolute;
    top:0;
    right:26px;
    display:none;
    color:#777;
    text-decoration:none
}
.asColorPicker-clear:after{
    content:"x"
}
.asColorPicker-wrap:hover .asColorPicker-clear{
    display:inline-block
}
.asColorPicker-preview{
    float:left;
    list-style:none
}
.asColorPicker-preview li{
    display:inline-block;
    vertical-align:top;
    background-image:url(images/transparent.png);
    *display:inline;
    *zoom:1
}
.asColorPicker-preview li span{
    display:block;
    height:100%
}
.asColorPicker-preview-previous{
    cursor:pointer
}
.asColorPicker-palettes ul{
    display:block
}
.asColorPicker-palettes ul:after,.asColorPicker-palettes ul:before{
    display:table;
    content:""
}
.asColorPicker-palettes ul:after{
    clear:both
}
.asColorPicker-palettes li{
    display:block;
    float:left;
    overflow:hidden;
    text-indent:100%;
    white-space:nowrap;
    cursor:pointer;
    background-image:url(images/transparent.png)
}
.asColorPicker-palettes li span{
    display:block;
    height:100%
}
.asColorPicker-saturation{
    position:relative;
    display:inline-block;
    width:175px;
    height:175px;
    clear:both;
    background-image:url(images/saturation.png);
    *display:inline;
    *zoom:1
}
.asColorPicker-saturation i{
    position:absolute
}
.asColorPicker-hue{
    position:relative;
    display:inline-block;
    width:20px;
    height:175px;
    cursor:pointer;
    *display:inline;
    *zoom:1
}
.asColorPicker-hue i{
    position:absolute;
    cursor:row-resize
}
.asColorPicker-hue{
    background-image:url(images/hue.png)
}

.asColorPicker-buttons a,.asColorPicker-gradient-control a{
    text-decoration:none;
    cursor:pointer
}
.asColorPicker-gradient{
    display:none
}
.asColorPicker-gradient_enable{
    display:block
}
.asColorPicker-gradient-preview{
    float:left;
    height:20px
}
.asColorPicker-gradient-markers{
    position:relative;
    width:100%
}
.asColorPicker-gradient-marker{
    position:absolute;
    outline:none
}
.asColorPicker-gradient-wheel{
    position:relative;
    float:left;
    width:20px;
    height:20px;
    border:1px solid #bbb;
    border-radius:100%
}
.asColorPicker-gradient-wheel i{
    position:absolute;
    width:3px;
    height:3px;
    border-radius:100%
}
.asColorPicker-gradient-angle{
    float:left
}
.asColorPicker-dropdown{
    min-width:205px;
    max-width:235px;
    padding:10px;
    background:#fefefe;
    border:1px solid #bbb
}
[data-mode=palettes] .asColorPicker-dropdown{
    min-width:auto;
    max-width:auto
}
.asColorPicker-trigger{
    border:1px solid #bbb
}
.asColorPicker-saturation{
    -webkit-box-shadow:inset 0 0 0 1px rgba(0,0,0,.05);
    box-shadow:inset 0 0 0 1px rgba(0,0,0,.05)
}
.asColorPicker-saturation i{
    width:5px;
    height:5px;
    margin-top:-2px;
    margin-left:-2px;
    border:2px solid #fff;
    border-radius:100%
}
.asColorPicker-hue{
    margin-left:10px;
    -webkit-box-shadow:inset 0 0 0 1px rgba(0,0,0,.05);
    box-shadow:inset 0 0 0 1px rgba(0,0,0,.05)
}
.asColorPicker-hue i{
    left:0px;
    width:20px;
    height:2px;
    margin-top:-2px;
    border:2px solid #fff
}
.asColorPicker-preview{
    position:relative;
    height:33px;
    margin-right:10px;
    margin-bottom:10px
}
.asColorPicker-preview:after{
    position:absolute;
    top:0;
    left:0;
    width:100%;
    height:100%;
    pointer-events:none;
    content:"";
    -webkit-box-shadow:inset 0 0 0 1px rgba(0,0,0,.05);
    box-shadow:inset 0 0 0 1px rgba(0,0,0,.05)
}
.asColorPicker-preview li{
    width:48px;
    height:33px
}
.asColorPicker-hex{
    width:100px;
    border-color:rgba(0,0,0,.05)
}
.asColorPicker-palettes li{
    width:21px;
    height:15px;
    margin-right:6px;
    margin-bottom:3px
}
.asColorPicker-palettes li span{
    -webkit-box-sizing:border-box;
    box-sizing:border-box;
    border:1px solid rgba(0,0,0,.05)
}
.asColorPicker-palettes li:nth-child(5n){
    margin-right:0
}
[data-mode=palettes] .asColorPicker-palettes li:nth-child(5n){
    margin-right:6px
}
.asColorPicker-buttons,.asColorPicker-gradient-control{
    float:right
}
.asColorPicker-buttons a,.asColorPicker-gradient-control a{
    margin-left:5px
}
.asColorPicker-gradient{
    padding-top:20px;
    margin-top:10px;
    border-top:1px solid rgba(0,0,0,.05)
}
.asColorPicker-gradient-preview{
    position:relative;
    width:160px;
    border:1px solid rgba(0,0,0,.05)
}
.asColorPicker-gradient-preview:after{
    position:absolute;
    top:0;
    left:0;
    z-index:-1;
    width:100%;
    height:100%;
    content:"";
    background-image:url(images/transparent.png)
}
.asColorPicker-gradient-markers{
    top:-16px;
    display:block;
    width:160px;
    height:16px;
    padding:0;
    margin:0;
    list-style:none
}
.asColorPicker-gradient-marker{
    width:10px;
    height:10px;
    margin-left:-6px;
    background:#fff;
    border:1px solid #bbb
}
.asColorPicker-gradient-marker span{
    display:block;
    width:100%;
    height:100%
}
.asColorPicker-gradient-marker i{
    position:absolute;
    bottom:-3px;
    left:2px;
    width:4px;
    height:4px;
    background:#fff;
    border:1px solid transparent;
    border-right-color:rgba(0,0,0,.05);
    border-bottom-color:rgba(0,0,0,.05);
    -webkit-transform:rotate(45deg);
    -ms-transform:rotate(45deg);
    transform:rotate(45deg)
}
.asColorPicker-gradient-marker_active{
    z-index:1;
    border:2px solid #41a9e5
}
.asColorPicker-gradient-marker_active i{
    left:1px;
    border:2px solid transparent;
    border-right-color:#41a9e5;
    border-bottom-color:#41a9e5
}
.asColorPicker-gradient-wheel{
    margin-left:10px
}
.asColorPicker-gradient-wheel i{
    background-color:#888
}
.asColorPicker-gradient-angle{
    width:24px;
    margin-left:10px
}
