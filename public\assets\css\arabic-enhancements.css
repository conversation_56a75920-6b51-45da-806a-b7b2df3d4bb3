/* Arabic Enhancements CSS */

/* Arabic Font Support */
@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap');

/* Apply Arabic font to all RTL content */
[dir="rtl"] {
    font-family: '<PERSON><PERSON><PERSON>', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Theme Colors for RTL */
:root {
    --theme-primary: #177775;
    --theme-secondary: #28D2BF;
    --theme-bg: #E5F4F3;
    --theme-font: #09322F;
    --theme-accent: #1d4643;
}

[dir="rtl"] .sidebar .nav .nav-item .nav-link i.menu-icon {
    color: var(--theme-primary);
    background: linear-gradient(45deg, var(--theme-primary), var(--theme-secondary));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

[dir="rtl"] .arabic-text,
[dir="rtl"] .menu-title,
[dir="rtl"] .card-title,
[dir="rtl"] h1,
[dir="rtl"] h2,
[dir="rtl"] h3,
[dir="rtl"] h4,
[dir="rtl"] h5,
[dir="rtl"] h6,
[dir="rtl"] p,
[dir="rtl"] span,
[dir="rtl"] div,
[dir="rtl"] a,
[dir="rtl"] button,
[dir="rtl"] input,
[dir="rtl"] select,
[dir="rtl"] textarea,
[dir="rtl"] label,
[dir="rtl"] th,
[dir="rtl"] td {
    font-family: 'Tajawal', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Improved Arabic text rendering */
[dir="rtl"] .arabic-content {
    text-align: right;
    line-height: 1.8;
    letter-spacing: 0.2px;
}

/* Navigation adjustments for RTL */
[dir="rtl"] .sidebar .nav {
    padding-right: 0;
}

[dir="rtl"] .sidebar .nav .nav-item .nav-link {
    text-align: right;
}

[dir="rtl"] .sidebar .nav .nav-item .nav-link .menu-title {
    margin-right: 20px;
    margin-left: 0;
}

[dir="rtl"] .sidebar .nav .nav-item .nav-link .menu-icon {
    float: right;
    margin-left: 15px;
    margin-right: 0;
}

/* Form adjustments for RTL */
[dir="rtl"] .form-group label {
    text-align: right;
}

[dir="rtl"] .form-control {
    text-align: right;
}

/* Table adjustments for RTL */
[dir="rtl"] .table th,
[dir="rtl"] .table td {
    text-align: right;
}

[dir="rtl"] .table .text-right {
    text-align: left !important;
}

[dir="rtl"] .table .text-left {
    text-align: right !important;
}

/* Button adjustments for RTL */
[dir="rtl"] .btn-group .btn:not(:first-child) {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    border-top-left-radius: 0.25rem;
    border-bottom-left-radius: 0.25rem;
}

[dir="rtl"] .btn-group .btn:not(:last-child) {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    border-top-right-radius: 0.25rem;
    border-bottom-right-radius: 0.25rem;
}

/* Modal adjustments for RTL */
[dir="rtl"] .modal-header .close {
    margin: -1rem auto -1rem -1rem;
}

/* Pagination adjustments for RTL */
[dir="rtl"] .pagination {
    flex-direction: row-reverse;
}

[dir="rtl"] .page-item:first-child .page-link {
    border-top-right-radius: 0.375rem;
    border-bottom-right-radius: 0.375rem;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}

[dir="rtl"] .page-item:last-child .page-link {
    border-top-left-radius: 0.375rem;
    border-bottom-left-radius: 0.375rem;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}

/* Breadcrumb adjustments for RTL */
[dir="rtl"] .breadcrumb {
    text-align: right;
}

[dir="rtl"] .breadcrumb-item + .breadcrumb-item::before {
    float: right;
    padding-left: 0.5rem;
    padding-right: 0;
}

/* Card adjustments for RTL */
[dir="rtl"] .card .card-header h4 {
    text-align: right;
}

/* Dashboard improvements */
[dir="rtl"] .dashboard-card {
    text-align: right;
}

[dir="rtl"] .dashboard-card .float-right {
    float: left !important;
}

[dir="rtl"] .dashboard-card .float-left {
    float: right !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    [dir="rtl"] .sidebar .nav .nav-item .nav-link .menu-title {
        margin-right: 10px;
    }
    
    [dir="rtl"] .sidebar .nav .nav-item .nav-link .menu-icon {
        margin-left: 10px;
    }
}