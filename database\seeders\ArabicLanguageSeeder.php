<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Artisan;

class ArabicLanguageSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Add Arabic language to the languages table
        DB::table('languages')->updateOrInsert(
            ['code' => 'ar'],
            [
                'name' => 'Arabic',
                'code' => 'ar',
                'file' => 'ar.json',
                'status' => 1,
                'is_rtl' => 1,
                'created_at' => now(),
                'updated_at' => now()
            ]
        );

        // Clear cache to ensure changes take effect
        Artisan::call('config:clear');
        Artisan::call('cache:clear');
        Artisan::call('view:clear');
        Artisan::call('route:clear');
    }
}