/**
 * New Color Theme for Madrasa Admin Panel
 * Based on the branding colors: 
 * - Background: #E5F4F3
 * - Icon Gradient: #177775 to #28D2BF
 * - Font: #09322F
 * - Slogan: #1d4643
 */

/* Sidebar Background */
.sidebar {
  background: #E5F4F3;
}

/* Menu Item Text Color */
.sidebar .nav .nav-item .nav-link .menu-title {
  color: #09322F;
}

/* Icon Color */
.sidebar .nav .nav-item .nav-link i.menu-icon {
  color: #177775;
  background: linear-gradient(45deg, #177775, #28D2BF);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* Active Menu Item */
.sidebar .nav .nav-item.active > .nav-link {
  background: linear-gradient(45deg, rgba(23, 119, 117, 0.1), rgba(40, 210, 191, 0.1));
}

.sidebar .nav .nav-item.active > .nav-link i.menu-icon,
.sidebar .nav .nav-item.active > .nav-link .menu-title {
  color: #177775;
}

.sidebar .nav .nav-item.active > .nav-link i.menu-icon {
  background: #177775;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* Hover Effect */
.sidebar .nav .nav-item:hover > .nav-link {
  background: linear-gradient(45deg, rgba(23, 119, 117, 0.05), rgba(40, 210, 191, 0.05));
}

.sidebar .nav .nav-item:hover > .nav-link i.menu-icon {
  color: #28D2BF;
}

/* Sub Menu */
.sidebar .nav .nav-item .collapse .nav .nav-item .nav-link {
  color: #1d4643;
}

/* Navigation Submenu Background */
.sidebar .nav.sub-menu {
  background: rgba(229, 244, 243, 0.7);
}

/* Main Content Area */
.content-wrapper {
  background-color: #fff;
}

/* Cards */
.card {
  border-color: #E5F4F3;
}

.card-header {
  background-color: #E5F4F3;
  color: #09322F;
}

/* Theme Color Classes */
.bg-theme {
  background: linear-gradient(45deg, #177775, #28D2BF) !important;
}

.text-theme {
  color: #177775 !important;
}

.btn-theme {
  background: linear-gradient(45deg, #177775, #28D2BF) !important;
  border-color: #177775 !important;
  color: white !important;
}

/* Change navbar background */
.navbar.default-layout-navbar {
  background: linear-gradient(to right, #177775, #28D2BF);
}

/* Navbar brand area (logo area) */
.navbar .navbar-brand-wrapper {
  background: #E5F4F3;
}

/* Navbar menu text */
.navbar.default-layout-navbar .navbar-menu-wrapper .navbar-nav .nav-item .nav-link {
  color: white;
}

/* Ensure dropdown menus are visible */
.dropdown-menu {
  background-color: #fff;
  color: #09322F;
}

/* Tajawal font for Arabic text */
@font-face {
  font-family: 'Tajawal';
  src: url('/assets/fonts/Tajawal-Regular.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: 'Tajawal';
  src: url('/assets/fonts/Tajawal-Bold.ttf') format('truetype');
  font-weight: bold;
  font-style: normal;
}

html[dir="rtl"] body {
  font-family: 'Tajawal', sans-serif;
}

/* Improve RTL support */
html[dir="rtl"] .sidebar .nav .nav-item .nav-link i.menu-icon {
  margin-left: 1rem;
  margin-right: 0;
}