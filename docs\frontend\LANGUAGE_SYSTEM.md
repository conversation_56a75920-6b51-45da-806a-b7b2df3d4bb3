# Language System Documentation

## Overview
This document explains how the language and internationalization (i18n) system works in the Madrasa Admin Panel application.

## Language Implementation

### Language Storage
Languages are stored in the database through the `languages` table with the following structure:
- `id` - Primary key
- `name` - Language name (e.g., "English", "Hindi")
- `code` - Language code (e.g., "en", "hi")
- `file` - JSON file name containing translations
- `status` - Active status (1 = active, 0 = inactive)
- `is_rtl` - RTL support flag (1 = RTL, 0 = LTR)
- `created_at`, `updated_at` - Timestamps

### Translation Files
Translation files are stored in `resources/lang/` directory:
- JSON files for each language (EN.json, HN.json, UR.json, guj.json)
- Standard Laravel language files in `resources/lang/en/` directory

### Language Loading Process
1. LanguageManager middleware checks for locale in session
2. If found, sets the application locale using `app()->setLocale()`
3. If not found, uses default locale

### Language Switching
1. User selects language from header dropdown
2. Request sent to `/set-language/{lang}` route
3. LanguageController stores selected language in session
4. Redirects back to previous page with new language

### Translation Usage
Translations are used in Blade templates with the `__()` helper function:
```php
{{ __('dashboard') }}
```

This function looks up the key in the appropriate language JSON file based on the current locale.

## RTL (Right-to-Left) Support

### RTL Implementation
The application supports RTL languages through:
1. `is_rtl` flag in the languages table
2. Conditional loading of RTL stylesheet in `include.blade.php`:
   ```php
   @if ($lang)
       @if ($lang->is_rtl)
           <link rel="stylesheet" href="{{ asset('/assets/css/rtl.css') }}">
       @else
           <link rel="stylesheet" href="{{ asset('/assets/css/style.css') }}">
       @endif
   @else
       <link rel="stylesheet" href="{{ asset('/assets/css/style.css') }}">
   @endif
   ```

### RTL CSS
- `public/assets/css/rtl.css` - Contains RTL-specific styles
- Based on the same template as `style.css` but with direction-specific adjustments

### Language Detection Logic
1. Session-based: Language stored in user session
2. Database-based: Language information retrieved from `languages` table
3. File-based: Translation strings loaded from JSON files

## Available Languages
1. English (EN) - LTR
2. Hindi (HN) - LTR
3. Urdu (UR) - RTL
4. Gujarati (guj) - LTR

## Adding New Languages
1. Create new language entry in database
2. Upload JSON translation file
3. Set RTL flag if needed
4. Activate language in settings

## Language Controller Functions
- `index()` - Display language settings page
- `language_sample()` - Download sample language file
- `store()` - Add new language
- `show()` - List all languages
- `update()` - Update existing language
- `destroy()` - Delete language
- `set_language()` - Switch current language

## Middleware
`LanguageManager` middleware automatically sets the application locale based on session data on each request.

## Future Improvements
1. Dynamic language loading without page refresh
2. Enhanced RTL support with automatic layout flipping
3. Language-specific asset loading
4. Improved translation management interface