/**
 * Font Awesome Icon Color Enhancement
 * This file specifically targets Font Awesome icons in the sidebar
 * and applies the new color theme gradients to them.
 */

/* Icon Container Styling */
.sidebar .nav .nav-item .nav-link .menu-icon {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  background-color: rgba(23, 119, 117, 0.1);
  margin-right: 0.75rem;
  color: #177775;
  transition: all 0.3s ease;
}

/* Apply gradient to icons */
.sidebar .nav .nav-item .nav-link .menu-icon:before {
  background: linear-gradient(45deg, #177775, #28D2BF);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  opacity: 1;
}

/* Active state for icons */
.sidebar .nav .nav-item.active .nav-link .menu-icon {
  background-color: rgba(23, 119, 117, 0.2);
  box-shadow: 0 0 8px rgba(23, 119, 117, 0.3);
}

/* Hover effect for icons */
.sidebar .nav .nav-item .nav-link:hover .menu-icon {
  background-color: rgba(40, 210, 191, 0.2);
  transform: scale(1.05);
}

/* RTL support for icons */
[dir="rtl"] .sidebar .nav .nav-item .nav-link .menu-icon {
  margin-right: 0;
  margin-left: 0.75rem;
}

/* Icon size adjustment */
.fa {
  font-size: 1.1rem;
}

/* Dashboard icon color */
.sidebar .nav .nav-item:first-child .nav-link .menu-icon {
  background-color: rgba(40, 210, 191, 0.2);
}

/* Apply specific colors to specific sections */
.sidebar .nav .nav-item:nth-child(2n) .nav-link .menu-icon {
  background-color: rgba(23, 119, 117, 0.1);
}

.sidebar .nav .nav-item:nth-child(2n+1) .nav-link .menu-icon {
  background-color: rgba(40, 210, 191, 0.1);
}

/* Make colors more vibrant */
.sidebar .nav .nav-item .nav-link .menu-icon:before {
  font-weight: 900;
}