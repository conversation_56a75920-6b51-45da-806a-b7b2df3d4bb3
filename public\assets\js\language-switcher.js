/**
 * Language Switcher JavaScript for Madrasa Admin Panel
 * Ensures proper language switching and RTL/LTR handling
 */

document.addEventListener('DOMContentLoaded', function() {
    // Get all language links
    var languageLinks = document.querySelectorAll('.language-link');
    
    // Add click handlers to each link
    languageLinks.forEach(function(link) {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            var url = this.getAttribute('href');
            
            // Create a form to submit via POST for more reliable session handling
            var form = document.createElement('form');
            form.method = 'POST';
            form.action = url;
            form.style.display = 'none';
            
            // Add CSRF token
            var csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
            var csrfInput = document.createElement('input');
            csrfInput.type = 'hidden';
            csrfInput.name = '_token';
            csrfInput.value = csrfToken;
            form.appendChild(csrfInput);
            
            // Add method override
            var methodInput = document.createElement('input');
            methodInput.type = 'hidden';
            methodInput.name = '_method';
            methodInput.value = 'GET';
            form.appendChild(methodInput);
            
            // Submit the form
            document.body.appendChild(form);
            form.submit();
        });
    });
    
    // Handle RTL/LTR directly through JS as a fallback
    function setRTL(isRTL) {
        document.documentElement.dir = isRTL ? 'rtl' : 'ltr';
        document.body.classList.toggle('rtl-mode', isRTL);
    }
    
    // Check URL for language parameter
    function checkURLForLanguage() {
        if (window.location.href.indexOf('/set-language/ar') > -1) {
            setRTL(true);
        } else if (window.location.href.indexOf('/set-language/en') > -1) {
            setRTL(false);
        }
    }
    
    // Run check
    checkURLForLanguage();
});