/**
 * Language Dropdown Styling for Madrasa Admin Panel
 */

/* Language dropdown button styling */
.navbar .navbar-menu-wrapper .navbar-nav .nav-item .nav-link i.fa-language {
  color: #fff;
  font-size: 20px;
  vertical-align: middle;
}

.navbar .navbar-menu-wrapper .navbar-nav .nav-item .nav-link span {
  color: #fff;
  font-size: 14px;
  font-weight: 500;
  vertical-align: middle;
}

/* Language dropdown styling */
.language-dropdown {
  min-width: 220px !important;
  padding: 0 !important;
}

.language-dropdown .dropdown-header {
  background: linear-gradient(45deg, rgba(23, 119, 117, 0.1), rgba(40, 210, 191, 0.1));
  color: #177775;
  font-weight: 600;
  font-size: 16px;
  padding: 15px 20px;
  border-radius: 8px 8px 0 0;
}

.language-dropdown .dropdown-item {
  padding: 12px 20px;
  transition: all 0.3s ease;
}

.language-dropdown .dropdown-item:hover {
  background: linear-gradient(45deg, rgba(23, 119, 117, 0.05), rgba(40, 210, 191, 0.05));
}

.language-dropdown .dropdown-item .preview-icon {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.language-dropdown .dropdown-item .preview-icon i {
  font-size: 16px;
}

.language-dropdown .dropdown-item .preview-subject {
  font-size: 15px;
  color: #09322F;
  font-weight: 500 !important;
}

/* RTL Support */
[dir="rtl"] .navbar .navbar-menu-wrapper .navbar-nav .nav-item .nav-link span {
  margin-right: 8px;
  margin-left: 0;
}

[dir="rtl"] .language-dropdown .dropdown-item .preview-item-content {
  text-align: right;
}