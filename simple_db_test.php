<?php
// Simple database connection test using the same credentials as Laravel

echo "Testing database connection with exact Laravel credentials...\n";

$host = '127.0.0.1';
$dbname = 'madrasa';
$username = 'root';
$password = '';
$port = 3306;

try {
    $pdo = new PDO("mysql:host=$host;port=$port;dbname=$dbname;charset=utf8mb4", $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_PERSISTENT => false,
        PDO::ATTR_TIMEOUT => 60,
    ]);
    
    echo "SUCCESS: Connected to the database!\n";
    
    // Test a simple query
    $stmt = $pdo->query("SELECT DATABASE()");
    $result = $stmt->fetch();
    echo "Connected to database: " . $result['DATABASE()'] . "\n";
    
    // Show some database info
    $stmt = $pdo->query("SELECT VERSION()");
    $version = $stmt->fetch();
    echo "MySQL Version: " . $version['VERSION()'] . "\n";
    
} catch (PDOException $e) {
    echo "ERROR: Connection failed: " . $e->getMessage() . "\n";
    echo "Error Code: " . $e->getCode() . "\n";
}
?>