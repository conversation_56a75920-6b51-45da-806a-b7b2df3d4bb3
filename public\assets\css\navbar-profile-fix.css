/**
 * Navbar Profile Image Fixes for Madrasa Admin Panel
 * This addresses issues with profile images and icons in the header
 */

/* Fix for navbar profile image container */
.navbar .navbar-menu-wrapper .navbar-nav .nav-item.nav-profile .nav-link .nav-profile-img {
  width: 40px; /* Increased size */
  height: 40px; /* Increased size */
  border-radius: 50%;
  overflow: hidden;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #E5F4F3;
  border: 2px solid rgba(40, 210, 191, 0.3);
  /* Prevent flickering with a minimum size */
  min-width: 40px; /* Increased size */
  min-height: 40px; /* Increased size */
}

/* Fix profile image itself */
.navbar .navbar-menu-wrapper .navbar-nav .nav-item.nav-profile .nav-link .nav-profile-img img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
  /* Prevent flickering during loading */
  opacity: 1;
  transition: opacity 0.2s ease-in-out;
}

/* Properly handle image loading */
.navbar .navbar-menu-wrapper .navbar-nav .nav-item.nav-profile .nav-link .nav-profile-img img:not([src]),
.navbar .navbar-menu-wrapper .navbar-nav .nav-item.nav-profile .nav-link .nav-profile-img img[src=""] {
  visibility: hidden;
  opacity: 0;
}

/* Better fallback image handling */
.error-profile-fallback {
  position: relative;
  /* Stable background during load */
  background-color: #E5F4F3 !important;
  /* Prevent flickering with stable size */
  width: 36px !important;
  height: 36px !important;
  border-radius: 50% !important;
  border: 2px solid rgba(40, 210, 191, 0.3) !important;
  object-fit: cover !important;
  opacity: 1 !important;
  transition: none !important;
}

/* Ensure consistent size for all profile images */
.navbar .navbar-menu-wrapper .navbar-nav .nav-item.nav-profile img {
  width: 36px !important;
  height: 36px !important;
  object-fit: cover !important;
  border-radius: 50% !important;
}

/* Ensure admin text is visible and properly styled */
.navbar .navbar-menu-wrapper .navbar-nav .nav-item.nav-profile .nav-link .nav-profile-text {
  margin-left: 12px; /* Increased margin */
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 120px;
}

.navbar .navbar-menu-wrapper .navbar-nav .nav-item.nav-profile .nav-link .nav-profile-text p {
  line-height: 1;
  margin-bottom: 0;
  color: white;
  font-weight: 500;
  font-size: 16px; /* Increased font size */
  text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

/* RTL Support */
[dir="rtl"] .navbar .navbar-menu-wrapper .navbar-nav .nav-item.nav-profile .nav-link .nav-profile-text {
  margin-left: 0;
  margin-right: 10px;
}

/* Fix dropdown styling */
.navbar .navbar-menu-wrapper .navbar-nav .nav-item.nav-profile .dropdown-menu {
  border: none;
  box-shadow: 0 3px 21px 0 rgba(0, 0, 0, 0.2);
  animation: none !important;
  transition: opacity 0.2s ease-in-out, visibility 0.2s ease-in-out !important;
}

.navbar .navbar-menu-wrapper .navbar-nav .nav-item.nav-profile .dropdown-menu:before {
  content: "";
  position: absolute;
  top: -10px;
  right: 20px;
  border: 5px solid transparent;
  border-bottom-color: white;
}

[dir="rtl"] .navbar .navbar-menu-wrapper .navbar-nav .nav-item.nav-profile .dropdown-menu:before {
  right: auto;
  left: 20px;
}

/* Admin label styling */
.admin-label {
  font-size: 14px;
  font-weight: 500;
  margin-right: 5px;
  color: white;
  text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

