/**
  * bootstrap-table - An extended table to integration with some of the most widely used CSS frameworks. (Supports Bootstrap, Semantic UI, Bulma, Material Design, Foundation)
  *
  * @version v1.22.1
  * @homepage https://bootstrap-table.com
  * <AUTHOR> <<EMAIL>> (http://wenzhixin.net.cn/)
  * @license MIT
  */

!function(t,r){"object"==typeof exports&&"undefined"!=typeof module?r(require("jquery")):"function"==typeof define&&define.amd?define(["jquery"],r):r((t="undefined"!=typeof globalThis?globalThis:t||self).jQuery)}(this,(function(t){"use strict";function r(t,r){if(!(t instanceof r))throw new TypeError("Cannot call a class as a function")}function n(t,r){for(var n=0;n<r.length;n++){var e=r[n];e.enumerable=e.enumerable||!1,e.configurable=!0,"value"in e&&(e.writable=!0),Object.defineProperty(t,(o=e.key,i=void 0,"symbol"==typeof(i=function(t,r){if("object"!=typeof t||null===t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var e=n.call(t,r||"default");if("object"!=typeof e)return e;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(t)}(o,"string"))?i:String(i)),e)}var o,i}function e(t){return e=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},e(t)}function o(t,r){return o=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,r){return t.__proto__=r,t},o(t,r)}function i(t,r){if(r&&("object"==typeof r||"function"==typeof r))return r;if(void 0!==r)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}function u(t){var r=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var n,o=e(t);if(r){var u=e(this).constructor;n=Reflect.construct(o,arguments,u)}else n=o.apply(this,arguments);return i(this,n)}}function a(t,r){for(;!Object.prototype.hasOwnProperty.call(t,r)&&null!==(t=e(t)););return t}function c(){return c="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(t,r,n){var e=a(t,r);if(e){var o=Object.getOwnPropertyDescriptor(e,r);return o.get?o.get.call(arguments.length<3?t:n):o.value}},c.apply(this,arguments)}function f(t){return function(t){if(Array.isArray(t))return s(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,r){if(!t)return;if("string"==typeof t)return s(t,r);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return s(t,r)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function s(t,r){(null==r||r>t.length)&&(r=t.length);for(var n=0,e=new Array(r);n<r;n++)e[n]=t[n];return e}var l="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},p=function(t){return t&&t.Math==Math&&t},y=p("object"==typeof globalThis&&globalThis)||p("object"==typeof window&&window)||p("object"==typeof self&&self)||p("object"==typeof l&&l)||function(){return this}()||Function("return this")(),d={},h=function(t){try{return!!t()}catch(t){return!0}},g=!h((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),b=!h((function(){var t=function(){}.bind();return"function"!=typeof t||t.hasOwnProperty("prototype")})),v=b,m=Function.prototype.call,w=v?m.bind(m):function(){return m.apply(m,arguments)},O={},S={}.propertyIsEnumerable,j=Object.getOwnPropertyDescriptor,D=j&&!S.call({1:2},1);O.f=D?function(t){var r=j(this,t);return!!r&&r.enumerable}:S;var P,T,x=function(t,r){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:r}},A=b,R=Function.prototype,E=R.call,I=A&&R.bind.bind(E,E),k=A?I:function(t){return function(){return E.apply(t,arguments)}},C=k,M=C({}.toString),_=C("".slice),F=function(t){return _(M(t),8,-1)},B=h,L=F,z=Object,N=k("".split),H=B((function(){return!z("z").propertyIsEnumerable(0)}))?function(t){return"String"==L(t)?N(t,""):z(t)}:z,$=function(t){return null==t},q=$,G=TypeError,U=function(t){if(q(t))throw G("Can't call method on "+t);return t},W=H,K=U,Q=function(t){return W(K(t))},V="object"==typeof document&&document.all,X={all:V,IS_HTMLDDA:void 0===V&&void 0!==V},Y=X.all,J=X.IS_HTMLDDA?function(t){return"function"==typeof t||t===Y}:function(t){return"function"==typeof t},Z=J,tt=X.all,rt=X.IS_HTMLDDA?function(t){return"object"==typeof t?null!==t:Z(t)||t===tt}:function(t){return"object"==typeof t?null!==t:Z(t)},nt=y,et=J,ot=function(t){return et(t)?t:void 0},it=function(t,r){return arguments.length<2?ot(nt[t]):nt[t]&&nt[t][r]},ut=k({}.isPrototypeOf),at=y,ct="undefined"!=typeof navigator&&String(navigator.userAgent)||"",ft=at.process,st=at.Deno,lt=ft&&ft.versions||st&&st.version,pt=lt&&lt.v8;pt&&(T=(P=pt.split("."))[0]>0&&P[0]<4?1:+(P[0]+P[1])),!T&&ct&&(!(P=ct.match(/Edge\/(\d+)/))||P[1]>=74)&&(P=ct.match(/Chrome\/(\d+)/))&&(T=+P[1]);var yt=T,dt=yt,ht=h,gt=!!Object.getOwnPropertySymbols&&!ht((function(){var t=Symbol();return!String(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&dt&&dt<41})),bt=gt&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,vt=it,mt=J,wt=ut,Ot=Object,St=bt?function(t){return"symbol"==typeof t}:function(t){var r=vt("Symbol");return mt(r)&&wt(r.prototype,Ot(t))},jt=String,Dt=function(t){try{return jt(t)}catch(t){return"Object"}},Pt=J,Tt=Dt,xt=TypeError,At=function(t){if(Pt(t))return t;throw xt(Tt(t)+" is not a function")},Rt=$,Et=w,It=J,kt=rt,Ct=TypeError,Mt={},_t={get exports(){return Mt},set exports(t){Mt=t}},Ft=y,Bt=Object.defineProperty,Lt=function(t,r){try{Bt(Ft,t,{value:r,configurable:!0,writable:!0})}catch(n){Ft[t]=r}return r},zt=Lt,Nt="__core-js_shared__",Ht=y[Nt]||zt(Nt,{}),$t=Ht;(_t.exports=function(t,r){return $t[t]||($t[t]=void 0!==r?r:{})})("versions",[]).push({version:"3.29.0",mode:"global",copyright:"© 2014-2023 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.29.0/LICENSE",source:"https://github.com/zloirock/core-js"});var qt=U,Gt=Object,Ut=function(t){return Gt(qt(t))},Wt=Ut,Kt=k({}.hasOwnProperty),Qt=Object.hasOwn||function(t,r){return Kt(Wt(t),r)},Vt=k,Xt=0,Yt=Math.random(),Jt=Vt(1..toString),Zt=function(t){return"Symbol("+(void 0===t?"":t)+")_"+Jt(++Xt+Yt,36)},tr=Mt,rr=Qt,nr=Zt,er=gt,or=bt,ir=y.Symbol,ur=tr("wks"),ar=or?ir.for||ir:ir&&ir.withoutSetter||nr,cr=function(t){return rr(ur,t)||(ur[t]=er&&rr(ir,t)?ir[t]:ar("Symbol."+t)),ur[t]},fr=w,sr=rt,lr=St,pr=function(t,r){var n=t[r];return Rt(n)?void 0:At(n)},yr=function(t,r){var n,e;if("string"===r&&It(n=t.toString)&&!kt(e=Et(n,t)))return e;if(It(n=t.valueOf)&&!kt(e=Et(n,t)))return e;if("string"!==r&&It(n=t.toString)&&!kt(e=Et(n,t)))return e;throw Ct("Can't convert object to primitive value")},dr=TypeError,hr=cr("toPrimitive"),gr=function(t,r){if(!sr(t)||lr(t))return t;var n,e=pr(t,hr);if(e){if(void 0===r&&(r="default"),n=fr(e,t,r),!sr(n)||lr(n))return n;throw dr("Can't convert object to primitive value")}return void 0===r&&(r="number"),yr(t,r)},br=St,vr=function(t){var r=gr(t,"string");return br(r)?r:r+""},mr=rt,wr=y.document,Or=mr(wr)&&mr(wr.createElement),Sr=function(t){return Or?wr.createElement(t):{}},jr=!g&&!h((function(){return 7!=Object.defineProperty(Sr("div"),"a",{get:function(){return 7}}).a})),Dr=g,Pr=w,Tr=O,xr=x,Ar=Q,Rr=vr,Er=Qt,Ir=jr,kr=Object.getOwnPropertyDescriptor;d.f=Dr?kr:function(t,r){if(t=Ar(t),r=Rr(r),Ir)try{return kr(t,r)}catch(t){}if(Er(t,r))return xr(!Pr(Tr.f,t,r),t[r])};var Cr={},Mr=g&&h((function(){return 42!=Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype})),_r=rt,Fr=String,Br=TypeError,Lr=function(t){if(_r(t))return t;throw Br(Fr(t)+" is not an object")},zr=g,Nr=jr,Hr=Mr,$r=Lr,qr=vr,Gr=TypeError,Ur=Object.defineProperty,Wr=Object.getOwnPropertyDescriptor,Kr="enumerable",Qr="configurable",Vr="writable";Cr.f=zr?Hr?function(t,r,n){if($r(t),r=qr(r),$r(n),"function"==typeof t&&"prototype"===r&&"value"in n&&Vr in n&&!n.writable){var e=Wr(t,r);e&&e.writable&&(t[r]=n.value,n={configurable:Qr in n?n.configurable:e.configurable,enumerable:Kr in n?n.enumerable:e.enumerable,writable:!1})}return Ur(t,r,n)}:Ur:function(t,r,n){if($r(t),r=qr(r),$r(n),Nr)try{return Ur(t,r,n)}catch(t){}if("get"in n||"set"in n)throw Gr("Accessors not supported");return"value"in n&&(t[r]=n.value),t};var Xr=Cr,Yr=x,Jr=g?function(t,r,n){return Xr.f(t,r,Yr(1,n))}:function(t,r,n){return t[r]=n,t},Zr={},tn={get exports(){return Zr},set exports(t){Zr=t}},rn=g,nn=Qt,en=Function.prototype,on=rn&&Object.getOwnPropertyDescriptor,un=nn(en,"name"),an={EXISTS:un,PROPER:un&&"something"===function(){}.name,CONFIGURABLE:un&&(!rn||rn&&on(en,"name").configurable)},cn=J,fn=Ht,sn=k(Function.toString);cn(fn.inspectSource)||(fn.inspectSource=function(t){return sn(t)});var ln,pn,yn,dn=fn.inspectSource,hn=J,gn=y.WeakMap,bn=hn(gn)&&/native code/.test(String(gn)),vn=Zt,mn=Mt("keys"),wn={},On=bn,Sn=y,jn=rt,Dn=Jr,Pn=Qt,Tn=Ht,xn=function(t){return mn[t]||(mn[t]=vn(t))},An=wn,Rn="Object already initialized",En=Sn.TypeError,In=Sn.WeakMap;if(On||Tn.state){var kn=Tn.state||(Tn.state=new In);kn.get=kn.get,kn.has=kn.has,kn.set=kn.set,ln=function(t,r){if(kn.has(t))throw En(Rn);return r.facade=t,kn.set(t,r),r},pn=function(t){return kn.get(t)||{}},yn=function(t){return kn.has(t)}}else{var Cn=xn("state");An[Cn]=!0,ln=function(t,r){if(Pn(t,Cn))throw En(Rn);return r.facade=t,Dn(t,Cn,r),r},pn=function(t){return Pn(t,Cn)?t[Cn]:{}},yn=function(t){return Pn(t,Cn)}}var Mn={set:ln,get:pn,has:yn,enforce:function(t){return yn(t)?pn(t):ln(t,{})},getterFor:function(t){return function(r){var n;if(!jn(r)||(n=pn(r)).type!==t)throw En("Incompatible receiver, "+t+" required");return n}}},_n=k,Fn=h,Bn=J,Ln=Qt,zn=g,Nn=an.CONFIGURABLE,Hn=dn,$n=Mn.enforce,qn=Mn.get,Gn=String,Un=Object.defineProperty,Wn=_n("".slice),Kn=_n("".replace),Qn=_n([].join),Vn=zn&&!Fn((function(){return 8!==Un((function(){}),"length",{value:8}).length})),Xn=String(String).split("String"),Yn=tn.exports=function(t,r,n){"Symbol("===Wn(Gn(r),0,7)&&(r="["+Kn(Gn(r),/^Symbol\(([^)]*)\)/,"$1")+"]"),n&&n.getter&&(r="get "+r),n&&n.setter&&(r="set "+r),(!Ln(t,"name")||Nn&&t.name!==r)&&(zn?Un(t,"name",{value:r,configurable:!0}):t.name=r),Vn&&n&&Ln(n,"arity")&&t.length!==n.arity&&Un(t,"length",{value:n.arity});try{n&&Ln(n,"constructor")&&n.constructor?zn&&Un(t,"prototype",{writable:!1}):t.prototype&&(t.prototype=void 0)}catch(t){}var e=$n(t);return Ln(e,"source")||(e.source=Qn(Xn,"string"==typeof r?r:"")),t};Function.prototype.toString=Yn((function(){return Bn(this)&&qn(this).source||Hn(this)}),"toString");var Jn=J,Zn=Cr,te=Zr,re=Lt,ne={},ee=Math.ceil,oe=Math.floor,ie=Math.trunc||function(t){var r=+t;return(r>0?oe:ee)(r)},ue=function(t){var r=+t;return r!=r||0===r?0:ie(r)},ae=ue,ce=Math.max,fe=Math.min,se=function(t,r){var n=ae(t);return n<0?ce(n+r,0):fe(n,r)},le=ue,pe=Math.min,ye=function(t){return t>0?pe(le(t),9007199254740991):0},de=function(t){return ye(t.length)},he=Q,ge=se,be=de,ve=function(t){return function(r,n,e){var o,i=he(r),u=be(i),a=ge(e,u);if(t&&n!=n){for(;u>a;)if((o=i[a++])!=o)return!0}else for(;u>a;a++)if((t||a in i)&&i[a]===n)return t||a||0;return!t&&-1}},me={includes:ve(!0),indexOf:ve(!1)},we=Qt,Oe=Q,Se=me.indexOf,je=wn,De=k([].push),Pe=function(t,r){var n,e=Oe(t),o=0,i=[];for(n in e)!we(je,n)&&we(e,n)&&De(i,n);for(;r.length>o;)we(e,n=r[o++])&&(~Se(i,n)||De(i,n));return i},Te=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"],xe=Pe,Ae=Te.concat("length","prototype");ne.f=Object.getOwnPropertyNames||function(t){return xe(t,Ae)};var Re={};Re.f=Object.getOwnPropertySymbols;var Ee=it,Ie=ne,ke=Re,Ce=Lr,Me=k([].concat),_e=Ee("Reflect","ownKeys")||function(t){var r=Ie.f(Ce(t)),n=ke.f;return n?Me(r,n(t)):r},Fe=Qt,Be=_e,Le=d,ze=Cr,Ne=h,He=J,$e=/#|\.prototype\./,qe=function(t,r){var n=Ue[Ge(t)];return n==Ke||n!=We&&(He(r)?Ne(r):!!r)},Ge=qe.normalize=function(t){return String(t).replace($e,".").toLowerCase()},Ue=qe.data={},We=qe.NATIVE="N",Ke=qe.POLYFILL="P",Qe=qe,Ve=y,Xe=d.f,Ye=Jr,Je=function(t,r,n,e){e||(e={});var o=e.enumerable,i=void 0!==e.name?e.name:r;if(Jn(n)&&te(n,i,e),e.global)o?t[r]=n:re(r,n);else{try{e.unsafe?t[r]&&(o=!0):delete t[r]}catch(t){}o?t[r]=n:Zn.f(t,r,{value:n,enumerable:!1,configurable:!e.nonConfigurable,writable:!e.nonWritable})}return t},Ze=Lt,to=function(t,r,n){for(var e=Be(r),o=ze.f,i=Le.f,u=0;u<e.length;u++){var a=e[u];Fe(t,a)||n&&Fe(n,a)||o(t,a,i(r,a))}},ro=Qe,no=function(t,r){var n,e,o,i,u,a=t.target,c=t.global,f=t.stat;if(n=c?Ve:f?Ve[a]||Ze(a,{}):(Ve[a]||{}).prototype)for(e in r){if(i=r[e],o=t.dontCallGetSet?(u=Xe(n,e))&&u.value:n[e],!ro(c?e:a+(f?".":"#")+e,t.forced)&&void 0!==o){if(typeof i==typeof o)continue;to(i,o)}(t.sham||o&&o.sham)&&Ye(i,"sham",!0),Je(n,e,i,t)}},eo=Pe,oo=Te,io=Object.keys||function(t){return eo(t,oo)},uo=g,ao=k,co=w,fo=h,so=io,lo=Re,po=O,yo=Ut,ho=H,go=Object.assign,bo=Object.defineProperty,vo=ao([].concat),mo=!go||fo((function(){if(uo&&1!==go({b:1},go(bo({},"a",{enumerable:!0,get:function(){bo(this,"b",{value:3,enumerable:!1})}}),{b:2})).b)return!0;var t={},r={},n=Symbol(),e="abcdefghijklmnopqrst";return t[n]=7,e.split("").forEach((function(t){r[t]=t})),7!=go({},t)[n]||so(go({},r)).join("")!=e}))?function(t,r){for(var n=yo(t),e=arguments.length,o=1,i=lo.f,u=po.f;e>o;)for(var a,c=ho(arguments[o++]),f=i?vo(so(c),i(c)):so(c),s=f.length,l=0;s>l;)a=f[l++],uo&&!co(u,c,a)||(n[a]=c[a]);return n}:go,wo=mo;no({target:"Object",stat:!0,arity:2,forced:Object.assign!==wo},{assign:wo});var Oo=F,So=Array.isArray||function(t){return"Array"==Oo(t)},jo=TypeError,Do=function(t){if(t>9007199254740991)throw jo("Maximum allowed index exceeded");return t},Po=vr,To=Cr,xo=x,Ao=function(t,r,n){var e=Po(r);e in t?To.f(t,e,xo(0,n)):t[e]=n},Ro={};Ro[cr("toStringTag")]="z";var Eo="[object z]"===String(Ro),Io=J,ko=F,Co=cr("toStringTag"),Mo=Object,_o="Arguments"==ko(function(){return arguments}()),Fo=k,Bo=h,Lo=J,zo=Eo?ko:function(t){var r,n,e;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(n=function(t,r){try{return t[r]}catch(t){}}(r=Mo(t),Co))?n:_o?ko(r):"Object"==(e=ko(r))&&Io(r.callee)?"Arguments":e},No=dn,Ho=function(){},$o=[],qo=it("Reflect","construct"),Go=/^\s*(?:class|function)\b/,Uo=Fo(Go.exec),Wo=!Go.exec(Ho),Ko=function(t){if(!Lo(t))return!1;try{return qo(Ho,$o,t),!0}catch(t){return!1}},Qo=function(t){if(!Lo(t))return!1;switch(zo(t)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return Wo||!!Uo(Go,No(t))}catch(t){return!0}};Qo.sham=!0;var Vo=!qo||Bo((function(){var t;return Ko(Ko.call)||!Ko(Object)||!Ko((function(){t=!0}))||t}))?Qo:Ko,Xo=So,Yo=Vo,Jo=rt,Zo=cr("species"),ti=Array,ri=function(t){var r;return Xo(t)&&(r=t.constructor,(Yo(r)&&(r===ti||Xo(r.prototype))||Jo(r)&&null===(r=r[Zo]))&&(r=void 0)),void 0===r?ti:r},ni=function(t,r){return new(ri(t))(0===r?0:r)},ei=h,oi=yt,ii=cr("species"),ui=function(t){return oi>=51||!ei((function(){var r=[];return(r.constructor={})[ii]=function(){return{foo:1}},1!==r[t](Boolean).foo}))},ai=no,ci=h,fi=So,si=rt,li=Ut,pi=de,yi=Do,di=Ao,hi=ni,gi=ui,bi=yt,vi=cr("isConcatSpreadable"),mi=bi>=51||!ci((function(){var t=[];return t[vi]=!1,t.concat()[0]!==t})),wi=function(t){if(!si(t))return!1;var r=t[vi];return void 0!==r?!!r:fi(t)};ai({target:"Array",proto:!0,arity:1,forced:!mi||!gi("concat")},{concat:function(t){var r,n,e,o,i,u=li(this),a=hi(u,0),c=0;for(r=-1,e=arguments.length;r<e;r++)if(wi(i=-1===r?u:arguments[r]))for(o=pi(i),yi(c+o),n=0;n<o;n++,c++)n in i&&di(a,c,i[n]);else yi(c+1),di(a,c++,i);return a.length=c,a}});var Oi=F,Si=k,ji=h,Di=no,Pi=me.indexOf,Ti=function(t,r){var n=[][t];return!!n&&ji((function(){n.call(null,r||function(){return 1},1)}))},xi=function(t){if("Function"===Oi(t))return Si(t)}([].indexOf),Ai=!!xi&&1/xi([1],1,-0)<0;Di({target:"Array",proto:!0,forced:Ai||!Ti("indexOf")},{indexOf:function(t){var r=arguments.length>1?arguments[1]:void 0;return Ai?xi(this,t,r)||0:Pi(this,t,r)}});var Ri=g,Ei=So,Ii=TypeError,ki=Object.getOwnPropertyDescriptor,Ci=Ri&&!function(){if(void 0!==this)return!0;try{Object.defineProperty([],"length",{writable:!1}).length=1}catch(t){return t instanceof TypeError}}(),Mi=Dt,_i=TypeError,Fi=no,Bi=Ut,Li=se,zi=ue,Ni=de,Hi=Ci?function(t,r){if(Ei(t)&&!ki(t,"length").writable)throw Ii("Cannot set read only .length");return t.length=r}:function(t,r){return t.length=r},$i=Do,qi=ni,Gi=Ao,Ui=function(t,r){if(!delete t[r])throw _i("Cannot delete property "+Mi(r)+" of "+Mi(t))},Wi=ui("splice"),Ki=Math.max,Qi=Math.min;Fi({target:"Array",proto:!0,forced:!Wi},{splice:function(t,r){var n,e,o,i,u,a,c=Bi(this),f=Ni(c),s=Li(t,f),l=arguments.length;for(0===l?n=e=0:1===l?(n=0,e=f-s):(n=l-2,e=Qi(Ki(zi(r),0),f-s)),$i(f+n-e),o=qi(c,e),i=0;i<e;i++)(u=s+i)in c&&Gi(o,i,c[u]);if(o.length=e,n<e){for(i=s;i<f-e;i++)a=i+n,(u=i+e)in c?c[a]=c[u]:Ui(c,a);for(i=f;i>f-e+n;i--)Ui(c,i-1)}else if(n>e)for(i=f-e;i>s;i--)a=i+n-1,(u=i+e-1)in c?c[a]=c[u]:Ui(c,a);for(i=0;i<n;i++)c[i+s]=arguments[i+2];return Hi(c,f-e+n),o}});var Vi=function(t,r){return{id:"customId_".concat(r)}};Object.assign(t.fn.bootstrapTable.defaults,{reorderableRows:!1,onDragStyle:null,onDropStyle:null,onDragClass:"reorder-rows-on-drag-class",dragHandle:">tbody>tr>td:not(.bs-checkbox)",useRowAttrFunc:!1,onReorderRowsDrag:function(t){return!1},onReorderRowsDrop:function(t){return!1},onReorderRow:function(t){return!1},onDragStop:function(){},onAllowDrop:function(){return!0}}),Object.assign(t.fn.bootstrapTable.events,{"reorder-row.bs.table":"onReorderRow"}),t.BootstrapTable=function(i){!function(t,r){if("function"!=typeof r&&null!==r)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(r&&r.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),r&&o(t,r)}(y,i);var a,s,l,p=u(y);function y(){return r(this,y),p.apply(this,arguments)}return a=y,s=[{key:"init",value:function(){for(var t,r=this,n=arguments.length,o=new Array(n),i=0;i<n;i++)o[i]=arguments[i];if(this.options.reorderableRows){this.options.useRowAttrFunc&&(this.options.rowAttributes=Vi);var u=this.options.onPostBody;this.options.onPostBody=function(){setTimeout((function(){r.makeRowsReorderable(),u.call(r.options,r.options.data)}),1)},(t=c(e(y.prototype),"init",this)).call.apply(t,[this].concat(o))}else{var a;(a=c(e(y.prototype),"init",this)).call.apply(a,[this].concat(o))}}},{key:"makeRowsReorderable",value:function(){var t=this;this.$el.tableDnD({onDragStyle:this.options.onDragStyle,onDropStyle:this.options.onDropStyle,onDragClass:this.options.onDragClass,onAllowDrop:function(r,n){return t.onAllowDrop(r,n)},onDragStop:function(r,n){return t.onDragStop(r,n)},onDragStart:function(r,n){return t.onDropStart(r,n)},onDrop:function(r,n){return t.onDrop(r,n)},dragHandle:this.options.dragHandle})}},{key:"onDropStart",value:function(r,n){this.$draggingTd=t(n).css("cursor","move"),this.draggingIndex=t(this.$draggingTd.parent()).data("index"),this.options.onReorderRowsDrag(this.data[this.draggingIndex])}},{key:"onDragStop",value:function(r,n){var e=t(n).data("index"),o=this.data[e];this.options.onDragStop(r,o,n)}},{key:"onAllowDrop",value:function(r,n){var e=t(n).data("index"),o=t(r).data("index"),i=this.data[e],u=this.data[o];return this.options.onAllowDrop(u,i,r,n)}},{key:"onDrop",value:function(r){this.$draggingTd.css("cursor","");for(var n=[],e=0;e<r.tBodies[0].rows.length;e++){var o=t(r.tBodies[0].rows[e]);n.push(this.data[o.data("index")]),o.data("index",e)}var i=this.data[this.draggingIndex],u=n.indexOf(this.data[this.draggingIndex]),a=this.data[u],c=this.options.data.indexOf(this.data[u]);this.options.data.splice(this.options.data.indexOf(i),1),this.options.data.splice(c,0,i),this.initSearch(),"server"===this.options.sidePagination&&(this.data=f(this.options.data)),this.options.onReorderRowsDrop(a),this.trigger("reorder-row",n,i,a)}},{key:"initSearch",value:function(){this.ignoreInitSort=!0,c(e(y.prototype),"initSearch",this).call(this)}},{key:"initSort",value:function(){this.ignoreInitSort?this.ignoreInitSort=!1:c(e(y.prototype),"initSort",this).call(this)}}],s&&n(a.prototype,s),l&&n(a,l),Object.defineProperty(a,"prototype",{writable:!1}),y}(t.BootstrapTable)}));
