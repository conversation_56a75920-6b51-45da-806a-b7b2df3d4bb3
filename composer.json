{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^8.0|^8.1|^8.2", "ext-curl": "*", "ext-zip": "*", "awobaz/compoships": "^2.1", "barryvdh/laravel-dompdf": "^2.0", "doctrine/dbal": "^3.3", "fruitcake/laravel-cors": "^2.0.5", "guzzlehttp/guzzle": "^7.2", "intervention/image": "^2.7", "laravel/framework": "^9.0", "laravel/sanctum": "^2.14", "laravel/tinker": "^2.7", "laravel/ui": "^3.4", "laravelcollective/html": "^6.3", "maatwebsite/excel": "^3.1", "psr/simple-cache": "^2.0", "razorpay/razorpay": "2.*", "spatie/laravel-permission": "^5.5", "stripe/stripe-php": "^10.0"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.6", "fakerphp/faker": "^1.9.1", "laravel/sail": "^1.0.1", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^6.1", "phpunit/phpunit": "^9.5.10"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "files": ["app/Helpers/settings_helper.php", "app/Helpers/notification_helper.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true}, "minimum-stability": "dev", "prefer-stable": true}