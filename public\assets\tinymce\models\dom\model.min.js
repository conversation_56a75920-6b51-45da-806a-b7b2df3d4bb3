/**
 * TinyMCE version 6.0.2 (2022-04-27)
 */
!function(){"use strict";var e=tinymce.util.Tools.resolve("tinymce.ModelManager");const t=e=>t=>(e=>{const t=typeof e;return null===e?"null":"object"===t&&Array.isArray(e)?"array":"object"===t&&(o=n=e,(r=String).prototype.isPrototypeOf(o)||(null===(s=n.constructor)||void 0===s?void 0:s.name)===r.name)?"string":t;var o,n,r,s})(t)===e,o=e=>t=>typeof t===e,n=t("string"),r=t("object"),s=t("array"),l=(null,e=>null===e);const a=o("boolean"),c=e=>!(e=>null==e)(e),i=o("function"),m=o("number"),d=()=>{},u=e=>()=>e,f=e=>e,g=(e,t)=>e===t;function h(e,...t){return(...o)=>{const n=t.concat(o);return e.apply(null,n)}}const p=e=>t=>!e(t),w=e=>e(),b=u(!1),v=u(!0);class y{constructor(e,t){this.tag=e,this.value=t}static some(e){return new y(!0,e)}static none(){return y.singletonNone}fold(e,t){return this.tag?t(this.value):e()}isSome(){return this.tag}isNone(){return!this.tag}map(e){return this.tag?y.some(e(this.value)):y.none()}bind(e){return this.tag?e(this.value):y.none()}exists(e){return this.tag&&e(this.value)}forall(e){return!this.tag||e(this.value)}filter(e){return!this.tag||e(this.value)?this:y.none()}getOr(e){return this.tag?this.value:e}or(e){return this.tag?this:e}getOrThunk(e){return this.tag?this.value:e()}orThunk(e){return this.tag?this:e()}getOrDie(e){if(this.tag)return this.value;throw new Error(null!=e?e:"Called getOrDie on None")}static from(e){return c(e)?y.some(e):y.none()}getOrNull(){return this.tag?this.value:null}getOrUndefined(){return this.value}each(e){this.tag&&e(this.value)}toArray(){return this.tag?[this.value]:[]}toString(){return this.tag?`some(${this.value})`:"none()"}}y.singletonNone=new y(!1);const x=Array.prototype.slice,C=Array.prototype.indexOf,S=Array.prototype.push,T=(e,t)=>((e,t)=>C.call(e,t))(e,t)>-1,R=(e,t)=>{for(let o=0,n=e.length;o<n;o++)if(t(e[o],o))return!0;return!1},D=(e,t)=>{const o=[];for(let n=0;n<e;n++)o.push(t(n));return o},O=(e,t)=>{const o=e.length,n=new Array(o);for(let r=0;r<o;r++){const o=e[r];n[r]=t(o,r)}return n},k=(e,t)=>{for(let o=0,n=e.length;o<n;o++)t(e[o],o)},E=(e,t)=>{const o=[],n=[];for(let r=0,s=e.length;r<s;r++){const s=e[r];(t(s,r)?o:n).push(s)}return{pass:o,fail:n}},N=(e,t)=>{const o=[];for(let n=0,r=e.length;n<r;n++){const r=e[n];t(r,n)&&o.push(r)}return o},B=(e,t,o)=>(((e,t)=>{for(let o=e.length-1;o>=0;o--)t(e[o],o)})(e,((e,n)=>{o=t(o,e,n)})),o),z=(e,t,o)=>(k(e,((e,n)=>{o=t(o,e,n)})),o),A=(e,t)=>((e,t,o)=>{for(let n=0,r=e.length;n<r;n++){const r=e[n];if(t(r,n))return y.some(r);if(o(r,n))break}return y.none()})(e,t,b),W=(e,t)=>{for(let o=0,n=e.length;o<n;o++)if(t(e[o],o))return y.some(o);return y.none()},L=e=>{const t=[];for(let o=0,n=e.length;o<n;++o){if(!s(e[o]))throw new Error("Arr.flatten item "+o+" was not an array, input: "+e);S.apply(t,e[o])}return t},M=(e,t)=>L(O(e,t)),j=(e,t)=>{for(let o=0,n=e.length;o<n;++o)if(!0!==t(e[o],o))return!1;return!0},_=(e,t)=>{const o={};for(let n=0,r=e.length;n<r;n++){const r=e[n];o[String(r)]=t(r,n)}return o},I=(e,t)=>t>=0&&t<e.length?y.some(e[t]):y.none(),P=e=>I(e,0),F=e=>I(e,e.length-1),H=(e,t)=>{for(let o=0;o<e.length;o++){const n=t(e[o],o);if(n.isSome())return n}return y.none()},q=Object.keys,V=Object.hasOwnProperty,$=(e,t)=>{const o=q(e);for(let n=0,r=o.length;n<r;n++){const r=o[n];t(e[r],r)}},U=(e,t)=>G(e,((e,o)=>({k:o,v:t(e,o)}))),G=(e,t)=>{const o={};return $(e,((e,n)=>{const r=t(e,n);o[r.k]=r.v})),o},K=(e,t)=>{const o={};return((e,t,o,n)=>{$(e,((e,r)=>{(t(e,r)?o:n)(e,r)}))})(e,t,(e=>(t,o)=>{e[o]=t})(o),d),o},Y=(e,t)=>{const o=[];return $(e,((e,n)=>{o.push(t(e,n))})),o},J=e=>Y(e,f),Q=(e,t)=>V.call(e,t);"undefined"!=typeof window?window:Function("return this;")();const X=e=>e.dom.nodeName.toLowerCase(),Z=e=>e.dom.nodeType,ee=e=>t=>Z(t)===e,te=e=>8===Z(e)||"#comment"===X(e),oe=ee(1),ne=ee(3),re=ee(9),se=ee(11),le=e=>t=>oe(t)&&X(t)===e,ae=(e,t,o)=>{if(!(n(o)||a(o)||m(o)))throw console.error("Invalid call to Attribute.set. Key ",t,":: Value ",o,":: Element ",e),new Error("Attribute value was not simple");e.setAttribute(t,o+"")},ce=(e,t,o)=>{ae(e.dom,t,o)},ie=(e,t)=>{const o=e.dom;$(t,((e,t)=>{ae(o,t,e)}))},me=(e,t)=>{const o=e.dom.getAttribute(t);return null===o?void 0:o},de=(e,t)=>y.from(me(e,t)),ue=(e,t)=>{e.dom.removeAttribute(t)},fe=e=>z(e.dom.attributes,((e,t)=>(e[t.name]=t.value,e)),{}),ge=e=>{if(null==e)throw new Error("Node cannot be null or undefined");return{dom:e}},he={fromHtml:(e,t)=>{const o=(t||document).createElement("div");if(o.innerHTML=e,!o.hasChildNodes()||o.childNodes.length>1){const t="HTML does not have a single root node";throw console.error(t,e),new Error(t)}return ge(o.childNodes[0])},fromTag:(e,t)=>{const o=(t||document).createElement(e);return ge(o)},fromText:(e,t)=>{const o=(t||document).createTextNode(e);return ge(o)},fromDom:ge,fromPoint:(e,t,o)=>y.from(e.dom.elementFromPoint(t,o)).map(ge)},pe=(e,t)=>{const o=e.dom;if(1!==o.nodeType)return!1;{const e=o;if(void 0!==e.matches)return e.matches(t);if(void 0!==e.msMatchesSelector)return e.msMatchesSelector(t);if(void 0!==e.webkitMatchesSelector)return e.webkitMatchesSelector(t);if(void 0!==e.mozMatchesSelector)return e.mozMatchesSelector(t);throw new Error("Browser lacks native selectors")}},we=e=>1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType||0===e.childElementCount,be=(e,t)=>{const o=void 0===t?document:t.dom;return we(o)?y.none():y.from(o.querySelector(e)).map(he.fromDom)},ve=(e,t)=>e.dom===t.dom,ye=(e,t)=>{const o=e.dom,n=t.dom;return o!==n&&o.contains(n)},xe=pe,Ce=e=>he.fromDom(e.dom.ownerDocument),Se=e=>re(e)?e:Ce(e),Te=e=>y.from(e.dom.parentNode).map(he.fromDom),Re=(e,t)=>{const o=i(t)?t:b;let n=e.dom;const r=[];for(;null!==n.parentNode&&void 0!==n.parentNode;){const e=n.parentNode,t=he.fromDom(e);if(r.push(t),!0===o(t))break;n=e}return r},De=e=>y.from(e.dom.previousSibling).map(he.fromDom),Oe=e=>y.from(e.dom.nextSibling).map(he.fromDom),ke=e=>O(e.dom.childNodes,he.fromDom),Ee=(e,t)=>{const o=e.dom.childNodes;return y.from(o[t]).map(he.fromDom)},Ne=(e,t)=>{Te(e).each((o=>{o.dom.insertBefore(t.dom,e.dom)}))},Be=(e,t)=>{Oe(e).fold((()=>{Te(e).each((e=>{Ae(e,t)}))}),(e=>{Ne(e,t)}))},ze=(e,t)=>{const o=(e=>Ee(e,0))(e);o.fold((()=>{Ae(e,t)}),(o=>{e.dom.insertBefore(t.dom,o.dom)}))},Ae=(e,t)=>{e.dom.appendChild(t.dom)},We=(e,t)=>{Ne(e,t),Ae(t,e)},Le=(e,t)=>{k(t,((o,n)=>{const r=0===n?e:t[n-1];Be(r,o)}))},Me=(e,t)=>{k(t,(t=>{Ae(e,t)}))},je=e=>{e.dom.textContent="",k(ke(e),(e=>{_e(e)}))},_e=e=>{const t=e.dom;null!==t.parentNode&&t.parentNode.removeChild(t)},Ie=e=>{const t=ke(e);t.length>0&&Le(e,t),_e(e)},Pe=(e,t)=>he.fromDom(e.dom.cloneNode(t)),Fe=e=>Pe(e,!1),He=e=>Pe(e,!0),qe=(e,t)=>{const o=he.fromTag(t),n=fe(e);return ie(o,n),o},Ve=["tfoot","thead","tbody","colgroup"],$e=(e,t,o)=>({element:e,rowspan:t,colspan:o}),Ue=(e,t,o)=>({element:e,cells:t,section:o}),Ge=(e,t,o)=>({element:e,isNew:t,isLocked:o}),Ke=(e,t,o,n)=>({element:e,cells:t,section:o,isNew:n}),Ye=i(Element.prototype.attachShadow)&&i(Node.prototype.getRootNode),Je=u(Ye),Qe=Ye?e=>he.fromDom(e.dom.getRootNode()):Se,Xe=e=>he.fromDom(e.dom.host),Ze=e=>{const t=ne(e)?e.dom.parentNode:e.dom;if(null==t||null===t.ownerDocument)return!1;const o=t.ownerDocument;return(e=>{const t=Qe(e);return se(o=t)&&c(o.dom.host)?y.some(t):y.none();var o})(he.fromDom(t)).fold((()=>o.body.contains(t)),(n=Ze,r=Xe,e=>n(r(e))));var n,r},et=e=>{const t=e.dom.body;if(null==t)throw new Error("Body is not available yet");return he.fromDom(t)},tt=(e,t)=>{let o=[];return k(ke(e),(e=>{t(e)&&(o=o.concat([e])),o=o.concat(tt(e,t))})),o},ot=(e,t,o)=>((e,o,n)=>N(Re(e,n),(e=>pe(e,t))))(e,0,o),nt=(e,t)=>((e,o)=>N(ke(e),(e=>pe(e,t))))(e),rt=(e,t)=>((e,t)=>{const o=void 0===t?document:t.dom;return we(o)?[]:O(o.querySelectorAll(e),he.fromDom)})(t,e);var st=(e,t,o,n,r)=>e(o,n)?y.some(o):i(r)&&r(o)?y.none():t(o,n,r);const lt=(e,t,o)=>{let n=e.dom;const r=i(o)?o:b;for(;n.parentNode;){n=n.parentNode;const e=he.fromDom(n);if(t(e))return y.some(e);if(r(e))break}return y.none()},at=(e,t,o)=>lt(e,(e=>pe(e,t)),o),ct=(e,t)=>((e,o)=>A(e.dom.childNodes,(e=>{return o=he.fromDom(e),pe(o,t);var o})).map(he.fromDom))(e),it=(e,t)=>be(t,e),mt=(e,t,o)=>st(((e,t)=>pe(e,t)),at,e,t,o),dt=(e,t,o=g)=>e.exists((e=>o(e,t))),ut=e=>{const t=[],o=e=>{t.push(e)};for(let t=0;t<e.length;t++)e[t].each(o);return t},ft=(e,t)=>e?y.some(t):y.none(),gt=(e,t,o)=>""===t||e.length>=t.length&&e.substr(o,o+t.length)===t,ht=(e,t)=>-1!==e.indexOf(t),pt=(e,t)=>gt(e,t,0),wt=(e,t)=>gt(e,t,e.length-t.length),bt=(e=>t=>t.replace(e,""))(/^\s+|\s+$/g),vt=e=>e.length>0,yt=e=>void 0!==e.style&&i(e.style.getPropertyValue),xt=(e,t,o)=>{if(!n(o))throw console.error("Invalid call to CSS.set. Property ",t,":: Value ",o,":: Element ",e),new Error("CSS value must be a string: "+o);yt(e)&&e.style.setProperty(t,o)},Ct=(e,t,o)=>{const n=e.dom;xt(n,t,o)},St=(e,t)=>{const o=e.dom;$(t,((e,t)=>{xt(o,t,e)}))},Tt=(e,t)=>{const o=e.dom,n=window.getComputedStyle(o).getPropertyValue(t);return""!==n||Ze(e)?n:Rt(o,t)},Rt=(e,t)=>yt(e)?e.style.getPropertyValue(t):"",Dt=(e,t)=>{const o=e.dom,n=Rt(o,t);return y.from(n).filter((e=>e.length>0))},Ot=(e,t)=>{((e,t)=>{yt(e)&&e.style.removeProperty(t)})(e.dom,t),dt(de(e,"style").map(bt),"")&&ue(e,"style")},kt=(e,t,o=0)=>de(e,t).map((e=>parseInt(e,10))).getOr(o),Et=(e,t)=>kt(e,t,1),Nt=e=>le("col")(e)?kt(e,"span",1)>1:Et(e,"colspan")>1,Bt=e=>Et(e,"rowspan")>1,zt=(e,t)=>parseInt(Tt(e,t),10),At=u(10),Wt=u(10),Lt=(e,t)=>Mt(e,t,v),Mt=(e,t,o)=>M(ke(e),(e=>pe(e,t)?o(e)?[e]:[]:Mt(e,t,o))),jt=(e,t)=>((e,t,o=b)=>o(t)?y.none():T(e,X(t))?y.some(t):at(t,e.join(","),(e=>pe(e,"table")||o(e))))(["td","th"],e,t),_t=e=>Lt(e,"th,td"),It=e=>pe(e,"colgroup")?nt(e,"col"):M(Ht(e),(e=>nt(e,"col"))),Pt=(e,t)=>mt(e,"table",t),Ft=e=>Lt(e,"tr"),Ht=e=>Pt(e).fold(u([]),(e=>nt(e,"colgroup"))),qt=(e,t)=>O(e,(e=>{if("colgroup"===X(e)){const t=O(It(e),(e=>{const t=kt(e,"span",1);return $e(e,1,t)}));return Ue(e,t,"colgroup")}{const o=O(_t(e),(e=>{const t=kt(e,"rowspan",1),o=kt(e,"colspan",1);return $e(e,t,o)}));return Ue(e,o,t(e))}})),Vt=e=>Te(e).map((e=>{const t=X(e);return(e=>T(Ve,e))(t)?t:"tbody"})).getOr("tbody"),$t=e=>{const t=Ft(e),o=[...Ht(e),...t];return qt(o,Vt)},Ut=e=>{let t,o=!1;return(...n)=>(o||(o=!0,t=e.apply(null,n)),t)},Gt=()=>Kt(0,0),Kt=(e,t)=>({major:e,minor:t}),Yt={nu:Kt,detect:(e,t)=>{const o=String(t).toLowerCase();return 0===e.length?Gt():((e,t)=>{const o=((e,t)=>{for(let o=0;o<e.length;o++){const n=e[o];if(n.test(t))return n}})(e,t);if(!o)return{major:0,minor:0};const n=e=>Number(t.replace(o,"$"+e));return Kt(n(1),n(2))})(e,o)},unknown:Gt},Jt=(e,t)=>{const o=String(t).toLowerCase();return A(e,(e=>e.search(o)))},Qt=/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,Xt=e=>t=>ht(t,e),Zt=[{name:"Edge",versionRegexes:[/.*?edge\/ ?([0-9]+)\.([0-9]+)$/],search:e=>ht(e,"edge/")&&ht(e,"chrome")&&ht(e,"safari")&&ht(e,"applewebkit")},{name:"Chromium",brand:"Chromium",versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/,Qt],search:e=>ht(e,"chrome")&&!ht(e,"chromeframe")},{name:"IE",versionRegexes:[/.*?msie\ ?([0-9]+)\.([0-9]+).*/,/.*?rv:([0-9]+)\.([0-9]+).*/],search:e=>ht(e,"msie")||ht(e,"trident")},{name:"Opera",versionRegexes:[Qt,/.*?opera\/([0-9]+)\.([0-9]+).*/],search:Xt("opera")},{name:"Firefox",versionRegexes:[/.*?firefox\/\ ?([0-9]+)\.([0-9]+).*/],search:Xt("firefox")},{name:"Safari",versionRegexes:[Qt,/.*?cpu os ([0-9]+)_([0-9]+).*/],search:e=>(ht(e,"safari")||ht(e,"mobile/"))&&ht(e,"applewebkit")}],eo=[{name:"Windows",search:Xt("win"),versionRegexes:[/.*?windows\ nt\ ?([0-9]+)\.([0-9]+).*/]},{name:"iOS",search:e=>ht(e,"iphone")||ht(e,"ipad"),versionRegexes:[/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,/.*cpu os ([0-9]+)_([0-9]+).*/,/.*cpu iphone os ([0-9]+)_([0-9]+).*/]},{name:"Android",search:Xt("android"),versionRegexes:[/.*?android\ ?([0-9]+)\.([0-9]+).*/]},{name:"macOS",search:Xt("mac os x"),versionRegexes:[/.*?mac\ os\ x\ ?([0-9]+)_([0-9]+).*/]},{name:"Linux",search:Xt("linux"),versionRegexes:[]},{name:"Solaris",search:Xt("sunos"),versionRegexes:[]},{name:"FreeBSD",search:Xt("freebsd"),versionRegexes:[]},{name:"ChromeOS",search:Xt("cros"),versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/]}],to={browsers:u(Zt),oses:u(eo)},oo="Edge",no="Chromium",ro="Opera",so="Firefox",lo="Safari",ao=e=>{const t=e.current,o=e.version,n=e=>()=>t===e;return{current:t,version:o,isEdge:n(oo),isChromium:n(no),isIE:n("IE"),isOpera:n(ro),isFirefox:n(so),isSafari:n(lo)}},co=()=>ao({current:void 0,version:Yt.unknown()}),io=ao,mo=(u(oo),u(no),u("IE"),u(ro),u(so),u(lo),"Windows"),uo="Android",fo="Linux",go="macOS",ho="Solaris",po="FreeBSD",wo="ChromeOS",bo=e=>{const t=e.current,o=e.version,n=e=>()=>t===e;return{current:t,version:o,isWindows:n(mo),isiOS:n("iOS"),isAndroid:n(uo),isMacOS:n(go),isLinux:n(fo),isSolaris:n(ho),isFreeBSD:n(po),isChromeOS:n(wo)}},vo=()=>bo({current:void 0,version:Yt.unknown()}),yo=bo,xo=(u(mo),u("iOS"),u(uo),u(fo),u(go),u(ho),u(po),u(wo),e=>window.matchMedia(e).matches);let Co=Ut((()=>((e,t,o)=>{const n=to.browsers(),r=to.oses(),s=t.bind((e=>((e,t)=>H(t.brands,(t=>{const o=t.brand.toLowerCase();return A(e,(e=>{var t;return o===(null===(t=e.brand)||void 0===t?void 0:t.toLowerCase())})).map((e=>({current:e.name,version:Yt.nu(parseInt(t.version,10),0)})))})))(n,e))).orThunk((()=>((e,t)=>Jt(e,t).map((e=>{const o=Yt.detect(e.versionRegexes,t);return{current:e.name,version:o}})))(n,e))).fold(co,io),l=((e,t)=>Jt(e,t).map((e=>{const o=Yt.detect(e.versionRegexes,t);return{current:e.name,version:o}})))(r,e).fold(vo,yo),a=((e,t,o,n)=>{const r=e.isiOS()&&!0===/ipad/i.test(o),s=e.isiOS()&&!r,l=e.isiOS()||e.isAndroid(),a=l||n("(pointer:coarse)"),c=r||!s&&l&&n("(min-device-width:768px)"),i=s||l&&!c,m=t.isSafari()&&e.isiOS()&&!1===/safari/i.test(o),d=!i&&!c&&!m;return{isiPad:u(r),isiPhone:u(s),isTablet:u(c),isPhone:u(i),isTouch:u(a),isAndroid:e.isAndroid,isiOS:e.isiOS,isWebView:u(m),isDesktop:u(d)}})(l,s,e,o);return{browser:s,os:l,deviceType:a}})(navigator.userAgent,y.from(navigator.userAgentData),xo)));const So=()=>Co(),To=(e,t)=>{const o=o=>{const n=t(o);if(n<=0||null===n){const t=Tt(o,e);return parseFloat(t)||0}return n},n=(e,t)=>z(t,((t,o)=>{const n=Tt(e,o),r=void 0===n?0:parseInt(n,10);return isNaN(r)?t:t+r}),0);return{set:(t,o)=>{if(!m(o)&&!o.match(/^[0-9]+$/))throw new Error(e+".set accepts only positive integer values. Value was "+o);const n=t.dom;yt(n)&&(n.style[e]=o+"px")},get:o,getOuter:o,aggregate:n,max:(e,t,o)=>{const r=n(e,o);return t>r?t-r:0}}},Ro=(e,t,o)=>((e,t)=>(e=>{const t=parseFloat(e);return isNaN(t)?y.none():y.some(t)})(e).getOr(t))(Tt(e,t),o),Do=To("width",(e=>e.dom.offsetWidth)),Oo=e=>Do.get(e),ko=e=>Do.getOuter(e),Eo=e=>((e,t)=>{const o=e.dom,n=o.getBoundingClientRect().width||o.offsetWidth;return"border-box"===t?n:((e,t,o,n)=>t-Ro(e,"padding-left",0)-Ro(e,"padding-right",0)-Ro(e,"border-left-width",0)-Ro(e,"border-right-width",0))(e,n)})(e,"content-box"),No=(e,t,o)=>{const n=e.cells,r=n.slice(0,t),s=n.slice(t),l=r.concat(o).concat(s);return Ao(e,l)},Bo=(e,t,o)=>No(e,t,[o]),zo=(e,t,o)=>{e.cells[t]=o},Ao=(e,t)=>Ke(e.element,t,e.section,e.isNew),Wo=(e,t)=>e.cells[t],Lo=(e,t)=>Wo(e,t).element,Mo=e=>e.cells.length,jo=e=>{const t=E(e,(e=>"colgroup"===e.section));return{rows:t.fail,cols:t.pass}},_o=(e,t,o)=>{const n=O(e.cells,o);return Ke(t(e.element),n,e.section,!0)},Io="data-snooker-locked-cols",Po=e=>de(e,Io).bind((e=>y.from(e.match(/\d+/g)))).map((e=>_(e,v))),Fo=e=>{const t=z(jo(e).rows,((e,t)=>(k(t.cells,((t,o)=>{t.isLocked&&(e[o]=!0)})),e)),{}),o=Y(t,((e,t)=>parseInt(t,10)));return((e,t)=>{const o=x.call(e,0);return o.sort(void 0),o})(o)},Ho=(e,t)=>e+","+t,qo=(e,t)=>{const o=M(e.all,(e=>e.cells));return N(o,t)},Vo=e=>{const t={},o=[],n=P(e).map((e=>e.element)).bind(Pt).bind(Po).getOr({});let r=0,s=0,l=0;const{pass:a,fail:c}=E(e,(e=>"colgroup"===e.section));k(c,(e=>{const a=[];k(e.cells,(e=>{let o=0;for(;void 0!==t[Ho(l,o)];)o++;const r=((e,t)=>Q(e,t)&&void 0!==e[t]&&null!==e[t])(n,o.toString()),c=((e,t,o,n,r,s)=>({element:e,rowspan:t,colspan:o,row:n,column:r,isLocked:s}))(e.element,e.rowspan,e.colspan,l,o,r);for(let n=0;n<e.colspan;n++)for(let r=0;r<e.rowspan;r++){const e=o+n,a=Ho(l+r,e);t[a]=c,s=Math.max(s,e+1)}a.push(c)})),r++,o.push(Ue(e.element,a,e.section)),l++}));const{columns:i,colgroups:m}=F(a).map((e=>{const t=(e=>{const t={};let o=0;return k(e.cells,(e=>{const n=e.colspan;D(n,(r=>{const s=o+r;t[s]=((e,t,o)=>({element:e,colspan:t,column:o}))(e.element,n,s)})),o+=n})),t})(e),o=((e,t)=>({element:e,columns:t}))(e.element,J(t));return{colgroups:[o],columns:t}})).getOrThunk((()=>({colgroups:[],columns:{}}))),d=((e,t)=>({rows:e,columns:t}))(r,s);return{grid:d,access:t,all:o,columns:i,colgroups:m}},$o=e=>{const t=$t(e);return Vo(t)},Uo=Vo,Go=(e,t,o)=>y.from(e.access[Ho(t,o)]),Ko=(e,t,o)=>{const n=qo(e,(e=>o(t,e.element)));return n.length>0?y.some(n[0]):y.none()},Yo=qo,Jo=e=>M(e.all,(e=>e.cells)),Qo=e=>J(e.columns),Xo=e=>q(e.columns).length>0,Zo=(e,t)=>y.from(e.columns[t]),en=(e,t=v)=>{const o=e.grid,n=D(o.columns,f),r=D(o.rows,f);return O(n,(o=>tn((()=>M(r,(t=>Go(e,t,o).filter((e=>e.column===o)).toArray()))),(e=>1===e.colspan&&t(e.element)),(()=>Go(e,0,o)))))},tn=(e,t,o)=>{const n=e();return A(n,t).orThunk((()=>y.from(n[0]).orThunk(o))).map((e=>e.element))},on=e=>{const t=e.grid,o=D(t.rows,f),n=D(t.columns,f);return O(o,(t=>tn((()=>M(n,(o=>Go(e,t,o).filter((e=>e.row===t)).fold(u([]),(e=>[e]))))),(e=>1===e.rowspan),(()=>Go(e,t,0)))))},nn=(e,t)=>o=>"rtl"===rn(o)?t:e,rn=e=>"rtl"===Tt(e,"direction")?"rtl":"ltr",sn=To("height",(e=>{const t=e.dom;return Ze(e)?t.getBoundingClientRect().height:t.offsetHeight})),ln=e=>sn.get(e),an=e=>sn.getOuter(e),cn=(e,t)=>({left:e,top:t,translate:(o,n)=>cn(e+o,t+n)}),mn=cn,dn=(e,t)=>void 0!==e?e:void 0!==t?t:0,un=e=>{const t=e.dom.ownerDocument,o=t.body,n=t.defaultView,r=t.documentElement;if(o===e.dom)return mn(o.offsetLeft,o.offsetTop);const s=dn(null==n?void 0:n.pageYOffset,r.scrollTop),l=dn(null==n?void 0:n.pageXOffset,r.scrollLeft),a=dn(r.clientTop,o.clientTop),c=dn(r.clientLeft,o.clientLeft);return fn(e).translate(l-c,s-a)},fn=e=>{const t=e.dom,o=t.ownerDocument.body;return o===t?mn(o.offsetLeft,o.offsetTop):Ze(e)?(e=>{const t=e.getBoundingClientRect();return mn(t.left,t.top)})(t):mn(0,0)},gn=(e,t)=>({row:e,y:t}),hn=(e,t)=>({col:e,x:t}),pn=e=>un(e).left+ko(e),wn=e=>un(e).left,bn=(e,t)=>hn(e,wn(t)),vn=(e,t)=>hn(e,pn(t)),yn=e=>un(e).top,xn=(e,t)=>gn(e,yn(t)),Cn=(e,t)=>gn(e,yn(t)+an(t)),Sn=(e,t,o)=>{if(0===o.length)return[];const n=O(o.slice(1),((t,o)=>t.map((t=>e(o,t))))),r=o[o.length-1].map((e=>t(o.length-1,e)));return n.concat([r])},Tn={delta:f,positions:e=>Sn(xn,Cn,e),edge:yn},Rn=nn({delta:f,edge:wn,positions:e=>Sn(bn,vn,e)},{delta:e=>-e,edge:pn,positions:e=>Sn(vn,bn,e)}),Dn={delta:(e,t)=>Rn(t).delta(e,t),positions:(e,t)=>Rn(t).positions(e,t),edge:e=>Rn(e).edge(e)},On={unsupportedLength:["em","ex","cap","ch","ic","rem","lh","rlh","vw","vh","vi","vb","vmin","vmax","cm","mm","Q","in","pc","pt","px"],fixed:["px","pt"],relative:["%"],empty:[""]},kn=(()=>{const e="[0-9]+",t="[eE][+-]?[0-9]+",o=e=>`(?:${e})?`,n=["Infinity","[0-9]+\\."+o(e)+o(t),"\\.[0-9]+"+o(t),e+o(t)].join("|");return new RegExp(`^([+-]?(?:${n}))(.*)$`)})(),En=/(\d+(\.\d+)?)%/,Nn=/(\d+(\.\d+)?)px|em/,Bn=le("col"),zn=(e,t,o)=>{const n=(r=e,y.from(r.dom.parentElement).map(he.fromDom)).getOrThunk((()=>et(Ce(e))));var r;return t(e)/o(n)*100},An=(e,t)=>{Ct(e,"width",t+"px")},Wn=(e,t)=>{Ct(e,"width",t+"%")},Ln=(e,t)=>{Ct(e,"height",t+"px")},Mn=e=>{const t=(e=>{return Ro(t=e,"height",t.dom.offsetHeight)+"px";var t})(e);return t?((e,t,o,n)=>{const r=parseFloat(e);return wt(e,"%")&&"table"!==X(t)?((e,t,o,n)=>{const r=Pt(e).map((e=>{const n=o(e);return Math.floor(t/100*n)})).getOr(t);return n(e,r),r})(t,r,o,n):r})(t,e,ln,Ln):ln(e)},jn=(e,t)=>Dt(e,t).orThunk((()=>de(e,t).map((e=>e+"px")))),_n=e=>jn(e,"width"),In=e=>zn(e,Oo,Eo),Pn=e=>{return Bn(e)?Oo(e):Ro(t=e,"width",t.dom.offsetWidth);var t},Fn=e=>((e,t,o)=>o(e)/Et(e,"rowspan"))(e,0,Mn),Hn=(e,t,o)=>{Ct(e,"width",t+o)},qn=e=>zn(e,Oo,Eo)+"%",Vn=u(En),$n=le("col"),Un=e=>_n(e).getOrThunk((()=>Pn(e)+"px")),Gn=e=>{return(t=e,jn(t,"height")).getOrThunk((()=>Fn(e)+"px"));var t},Kn=(e,t,o,n,r,s)=>e.filter(n).fold((()=>s(((e,t)=>{if(t<0||t>=e.length-1)return y.none();const o=e[t].fold((()=>{const o=(e=>{const t=x.call(e,0);return t.reverse(),t})(e.slice(0,t));return H(o,((e,t)=>e.map((e=>({value:e,delta:t+1})))))}),(e=>y.some({value:e,delta:0}))),n=e[t+1].fold((()=>{const o=e.slice(t+1);return H(o,((e,t)=>e.map((e=>({value:e,delta:t+1})))))}),(e=>y.some({value:e,delta:1})));return o.bind((e=>n.map((t=>{const o=t.delta+e.delta;return Math.abs(t.value-e.value)/o}))))})(o,t))),(e=>r(e))),Yn=(e,t,o,n)=>{const r=en(e),s=Xo(e)?(e=>O(Qo(e),(e=>y.from(e.element))))(e):r,l=[y.some(Dn.edge(t))].concat(O(Dn.positions(r,t),(e=>e.map((e=>e.x))))),a=p(Nt);return O(s,((e,t)=>Kn(e,t,l,a,(e=>{if((e=>{const t=So().browser,o=t.isChromium()||t.isFirefox();return!$n(e)||o})(e))return o(e);{const e=null!=(s=r[t])?f(s):y.none();return Kn(e,t,l,a,(e=>n(y.some(Oo(e)))),n)}var s}),n)))},Jn=e=>e.map((e=>e+"px")).getOr(""),Qn=(e,t,o)=>Yn(e,t,Pn,(e=>e.getOrThunk(o.minCellWidth))),Xn=(e,t,o,n,r)=>{const s=on(e),l=[y.some(o.edge(t))].concat(O(o.positions(s,t),(e=>e.map((e=>e.y)))));return O(s,((e,t)=>Kn(e,t,l,p(Bt),n,r)))},Zn=(e,t)=>()=>Ze(e)?t(e):parseFloat(Dt(e,"width").getOr("0")),er=e=>{const t=Zn(e,(e=>parseFloat(qn(e)))),o=Zn(e,Oo);return{width:t,pixelWidth:o,getWidths:(t,o)=>((e,t,o)=>Yn(e,t,In,(e=>e.fold((()=>o.minCellWidth()),(e=>e/o.pixelWidth()*100)))))(t,e,o),getCellDelta:e=>e/o()*100,singleColumnWidth:(e,t)=>[100-e],minCellWidth:()=>At()/o()*100,setElementWidth:Wn,adjustTableWidth:o=>{const n=t();Wn(e,n+o/100*n)},isRelative:!0,label:"percent"}},tr=e=>{const t=Zn(e,Oo);return{width:t,pixelWidth:t,getWidths:(t,o)=>Qn(t,e,o),getCellDelta:f,singleColumnWidth:(e,t)=>[Math.max(At(),e+t)-e],minCellWidth:At,setElementWidth:An,adjustTableWidth:o=>{const n=t()+o;An(e,n)},isRelative:!1,label:"pixel"}},or=e=>_n(e).fold((()=>(e=>{const t=Zn(e,Oo),o=u(0);return{width:t,pixelWidth:t,getWidths:(t,o)=>Qn(t,e,o),getCellDelta:o,singleColumnWidth:u([0]),minCellWidth:o,setElementWidth:d,adjustTableWidth:d,isRelative:!0,label:"none"}})(e)),(t=>((e,t)=>null!==Vn().exec(t)?er(e):tr(e))(e,t))),nr=tr,rr=er,sr=(e,t,o)=>{const n=e[o].element,r=he.fromTag("td");Ae(r,he.fromTag("br")),(t?Ae:ze)(n,r)},lr=((e,t)=>{const o=t=>e(t)?y.from(t.dom.nodeValue):y.none();return{get:t=>{if(!e(t))throw new Error("Can only get text value of a text node");return o(t).getOr("")},getOption:o,set:(t,o)=>{if(!e(t))throw new Error("Can only set raw text value of a text node");t.dom.nodeValue=o}}})(ne),ar=e=>lr.get(e),cr=e=>lr.getOption(e),ir=(e,t)=>lr.set(e,t),mr=e=>"img"===X(e)?1:cr(e).fold((()=>ke(e).length),(e=>e.length)),dr=["img","br"],ur=e=>cr(e).filter((e=>0!==e.trim().length||e.indexOf("\xa0")>-1)).isSome()||T(dr,X(e)),fr=e=>((e,t)=>{const o=e=>{for(let n=0;n<e.childNodes.length;n++){const r=he.fromDom(e.childNodes[n]);if(t(r))return y.some(r);const s=o(e.childNodes[n]);if(s.isSome())return s}return y.none()};return o(e.dom)})(e,ur),gr=e=>hr(e,ur),hr=(e,t)=>{const o=e=>{const n=ke(e);for(let e=n.length-1;e>=0;e--){const r=n[e];if(t(r))return y.some(r);const s=o(r);if(s.isSome())return s}return y.none()};return o(e)},pr={scope:["row","col"]},wr=e=>()=>{const t=he.fromTag("td",e.dom);return Ae(t,he.fromTag("br",e.dom)),t},br=e=>()=>he.fromTag("col",e.dom),vr=e=>()=>he.fromTag("colgroup",e.dom),yr=e=>()=>he.fromTag("tr",e.dom),xr=(e,t,o)=>{const n=((e,t)=>{const o=qe(e,t),n=ke(He(e));return Me(o,n),o})(e,t);return $(o,((e,t)=>{null===e?ue(n,t):ce(n,t,e)})),n},Cr=e=>e,Sr=(e,t,o)=>{const n=(e,t)=>{((e,t)=>{const o=e.dom,n=t.dom;yt(o)&&yt(n)&&(n.style.cssText=o.style.cssText)})(e.element,t),Ot(t,"height"),1!==e.colspan&&Ot(t,"width")};return{col:o=>{const r=he.fromTag(X(o.element),t.dom);return n(o,r),e(o.element,r),r},colgroup:vr(t),row:yr(t),cell:r=>{const s=he.fromTag(X(r.element),t.dom),l=o.getOr(["strong","em","b","i","span","font","h1","h2","h3","h4","h5","h6","p","div"]),a=l.length>0?((e,t,o)=>fr(e).map((n=>{const r=o.join(","),s=ot(n,r,(t=>ve(t,e)));return B(s,((e,t)=>{const o=Fe(t);return ue(o,"contenteditable"),Ae(e,o),o}),t)})).getOr(t))(r.element,s,l):s;return Ae(a,he.fromTag("br")),n(r,s),((e,t)=>{$(pr,((o,n)=>de(e,n).filter((e=>T(o,e))).each((e=>ce(t,n,e)))))})(r.element,s),e(r.element,s),s},replace:xr,colGap:br(t),gap:wr(t)}},Tr=e=>({col:br(e),colgroup:vr(e),row:yr(e),cell:wr(e),replace:Cr,colGap:br(e),gap:wr(e)}),Rr=e=>he.fromDom(e.getBody()),Dr=e=>t=>ve(t,Rr(e)),Or=e=>{ue(e,"data-mce-style");const t=e=>ue(e,"data-mce-style");k(_t(e),t),k(It(e),t),k(Ft(e),t)},kr=e=>he.fromDom(e.selection.getStart()),Er=e=>e.getBoundingClientRect().width,Nr=e=>e.getBoundingClientRect().height,Br=(e,t)=>{const o=t.column,n=t.column+t.colspan-1,r=t.row,s=t.row+t.rowspan-1;return o<=e.finishCol&&n>=e.startCol&&r<=e.finishRow&&s>=e.startRow},zr=(e,t)=>t.column>=e.startCol&&t.column+t.colspan-1<=e.finishCol&&t.row>=e.startRow&&t.row+t.rowspan-1<=e.finishRow,Ar=(e,t,o)=>{const n=Ko(e,t,ve),r=Ko(e,o,ve);return n.bind((e=>r.map((t=>{return o=e,n=t,{startRow:Math.min(o.row,n.row),startCol:Math.min(o.column,n.column),finishRow:Math.max(o.row+o.rowspan-1,n.row+n.rowspan-1),finishCol:Math.max(o.column+o.colspan-1,n.column+n.colspan-1)};var o,n}))))},Wr=(e,t,o)=>Ar(e,t,o).map((t=>{const o=Yo(e,h(Br,t));return O(o,(e=>e.element))})),Lr=(e,t)=>Ko(e,t,((e,t)=>ye(t,e))).map((e=>e.element)),Mr=(e,t,o)=>{const n=_r(e);return Wr(n,t,o)},jr=(e,t,o,n,r)=>{const s=_r(e),l=ve(e,o)?y.some(t):Lr(s,t),a=ve(e,r)?y.some(n):Lr(s,n);return l.bind((e=>a.bind((t=>Wr(s,e,t)))))},_r=$o;var Ir=["body","p","div","article","aside","figcaption","figure","footer","header","nav","section","ol","ul","li","table","thead","tbody","tfoot","caption","tr","td","th","h1","h2","h3","h4","h5","h6","blockquote","pre","address"],Pr=()=>({up:u({selector:at,closest:mt,predicate:lt,all:Re}),down:u({selector:rt,predicate:tt}),styles:u({get:Tt,getRaw:Dt,set:Ct,remove:Ot}),attrs:u({get:me,set:ce,remove:ue,copyTo:(e,t)=>{const o=fe(e);ie(t,o)}}),insert:u({before:Ne,after:Be,afterAll:Le,append:Ae,appendAll:Me,prepend:ze,wrap:We}),remove:u({unwrap:Ie,remove:_e}),create:u({nu:he.fromTag,clone:e=>he.fromDom(e.dom.cloneNode(!1)),text:he.fromText}),query:u({comparePosition:(e,t)=>e.dom.compareDocumentPosition(t.dom),prevSibling:De,nextSibling:Oe}),property:u({children:ke,name:X,parent:Te,document:e=>Se(e).dom,isText:ne,isComment:te,isElement:oe,isSpecial:e=>{const t=X(e);return T(["script","noscript","iframe","noframes","noembed","title","style","textarea","xmp"],t)},getLanguage:e=>oe(e)?de(e,"lang"):y.none(),getText:ar,setText:ir,isBoundary:e=>!!oe(e)&&("body"===X(e)||T(Ir,X(e))),isEmptyTag:e=>!!oe(e)&&T(["br","img","hr","input"],X(e)),isNonEditable:e=>oe(e)&&"false"===me(e,"contenteditable")}),eq:ve,is:xe});const Fr=(e,t,o,n)=>{const r=t(e,o);return B(n,((o,n)=>{const r=t(e,n);return Hr(e,o,r)}),r)},Hr=(e,t,o)=>t.bind((t=>o.filter(h(e.eq,t)))),qr=Pr(),Vr=(e,t)=>((e,t,o)=>o.length>0?((e,t,o,n)=>n(e,t,o[0],o.slice(1)))(e,t,o,Fr):y.none())(qr,((t,o)=>e(o)),t),$r=e=>at(e,"table"),Ur=(e,t,o)=>{const n=e=>t=>void 0!==o&&o(t)||ve(t,e);return ve(e,t)?y.some({boxes:y.some([e]),start:e,finish:t}):$r(e).bind((r=>$r(t).bind((s=>{if(ve(r,s))return y.some({boxes:Mr(r,e,t),start:e,finish:t});if(ye(r,s)){const o=ot(t,"td,th",n(r)),l=o.length>0?o[o.length-1]:t;return y.some({boxes:jr(r,e,r,t,s),start:e,finish:l})}if(ye(s,r)){const o=ot(e,"td,th",n(s)),l=o.length>0?o[o.length-1]:e;return y.some({boxes:jr(s,e,r,t,s),start:e,finish:l})}return((e,t,o)=>((e,t,o,n=b)=>{const r=[t].concat(e.up().all(t)),s=[o].concat(e.up().all(o)),l=e=>W(e,n).fold((()=>e),(t=>e.slice(0,t+1))),a=l(r),c=l(s),i=A(a,(t=>R(c,((e,t)=>h(e.eq,t))(e,t))));return{firstpath:a,secondpath:c,shared:i}})(qr,e,t,void 0))(e,t).shared.bind((l=>mt(l,"table",o).bind((o=>{const l=ot(t,"td,th",n(o)),a=l.length>0?l[l.length-1]:t,c=ot(e,"td,th",n(o)),i=c.length>0?c[c.length-1]:e;return y.some({boxes:jr(o,e,r,t,s),start:i,finish:a})}))))}))))},Gr=(e,t)=>{const o=rt(e,t);return o.length>0?y.some(o):y.none()},Kr=(e,t,o)=>it(e,t).bind((t=>it(e,o).bind((e=>Vr($r,[t,e]).map((o=>({first:t,last:e,table:o}))))))),Yr=(e,t,o,n,r)=>((e,t)=>A(e,(e=>pe(e,t))))(e,r).bind((e=>((e,t,o)=>Pt(e).bind((n=>((e,t,o,n)=>Ko(e,t,ve).bind((t=>{const r=o>0?t.row+t.rowspan-1:t.row,s=n>0?t.column+t.colspan-1:t.column;return Go(e,r+o,s+n).map((e=>e.element))})))(_r(n),e,t,o))))(e,t,o).bind((e=>((e,t)=>at(e,"table").bind((o=>it(o,t).bind((t=>Ur(t,e).bind((e=>e.boxes.map((t=>({boxes:t,start:e.start,finish:e.finish}))))))))))(e,n))))),Jr=(e,t)=>Gr(e,t),Qr=(e,t,o)=>Kr(e,t,o).bind((t=>{const o=t=>ve(e,t),n="thead,tfoot,tbody,table",r=at(t.first,n,o),s=at(t.last,n,o);return r.bind((e=>s.bind((o=>ve(e,o)?((e,t,o)=>((e,t,o)=>Ar(e,t,o).bind((t=>((e,t)=>{let o=!0;const n=h(zr,t);for(let r=t.startRow;r<=t.finishRow;r++)for(let s=t.startCol;s<=t.finishCol;s++)o=o&&Go(e,r,s).exists(n);return o?y.some(t):y.none()})(e,t))))(_r(e),t,o))(t.table,t.first,t.last):y.none()))))})),Xr=f,Zr=e=>{const t=(e,t)=>de(e,t).exists((e=>parseInt(e,10)>1));return e.length>0&&j(e,(e=>t(e,"rowspan")||t(e,"colspan")))?y.some(e):y.none()},es=(e,t,o)=>t.length<=1?y.none():Qr(e,o.firstSelectedSelector,o.lastSelectedSelector).map((e=>({bounds:e,cells:t}))),ts={selected:"data-mce-selected",selectedSelector:"td[data-mce-selected],th[data-mce-selected]",firstSelected:"data-mce-first-selected",firstSelectedSelector:"td[data-mce-first-selected],th[data-mce-first-selected]",lastSelected:"data-mce-last-selected",lastSelectedSelector:"td[data-mce-last-selected],th[data-mce-last-selected]"},os=(e,t,o)=>({element:o,mergable:es(t,e,ts),unmergable:Zr(e),selection:Xr(e)}),ns=e=>(t,o)=>{const n=X(t),r="col"===n||"colgroup"===n?Pt(s=t).bind((e=>Jr(e,ts.firstSelectedSelector))).fold(u(s),(e=>e[0])):t;var s;return mt(r,e,o)},rs=ns("th,td,caption"),ss=ns("th,td"),ls=e=>{return t=e.model.table.getSelectedCells(),O(t,he.fromDom);var t},as=(e,t)=>{e.on("BeforeGetContent",(t=>{const o=o=>{t.preventDefault(),(e=>Pt(e[0]).map((e=>{const t=((e,t)=>{const o=e=>pe(e.element,t),n=He(e),r=$t(n),s=or(e),l=Uo(r),a=((e,t)=>{const o=e.grid.columns;let n=e.grid.rows,r=o,s=0,l=0;const a=[],c=[];return $(e.access,(e=>{if(a.push(e),t(e)){c.push(e);const t=e.row,o=t+e.rowspan-1,a=e.column,i=a+e.colspan-1;t<n?n=t:o>s&&(s=o),a<r?r=a:i>l&&(l=i)}})),((e,t,o,n,r,s)=>({minRow:e,minCol:t,maxRow:o,maxCol:n,allCells:r,selectedCells:s}))(n,r,s,l,a,c)})(l,o),c="th:not("+t+"),td:not("+t+")",i=Mt(n,"th,td",(e=>pe(e,c)));k(i,_e),((e,t,o,n)=>{const r=N(e,(e=>"colgroup"!==e.section)),s=t.grid.columns,l=t.grid.rows;for(let e=0;e<l;e++){let l=!1;for(let a=0;a<s;a++)e<o.minRow||e>o.maxRow||a<o.minCol||a>o.maxCol||(Go(t,e,a).filter(n).isNone()?sr(r,l,e):l=!0)}})(r,l,a,o);const m=((e,t,o,n)=>{if(0===n.minCol&&t.grid.columns===n.maxCol+1)return 0;const r=Qn(t,e,o),s=z(r,((e,t)=>e+t),0),l=z(r.slice(n.minCol,n.maxCol+1),((e,t)=>e+t),0),a=l/s*o.pixelWidth()-o.pixelWidth();return o.getCellDelta(a)})(e,$o(e),s,a);return((e,t,o,n)=>{$(o.columns,(e=>{(e.column<t.minCol||e.column>t.maxCol)&&_e(e.element)}));const r=N(Lt(e,"tr"),(e=>0===e.dom.childElementCount));k(r,_e),t.minCol!==t.maxCol&&t.minRow!==t.maxRow||k(Lt(e,"th,td"),(e=>{ue(e,"rowspan"),ue(e,"colspan")})),ue(e,Io),ue(e,"data-snooker-col-series"),or(e).adjustTableWidth(n)})(n,a,l,m),n})(e,"[data-mce-selected]");return Or(t),[t]})))(o).each((o=>{t.content="text"===t.format?(e=>O(e,(e=>e.dom.innerText)).join(""))(o):((e,t)=>O(t,(t=>e.selection.serializer.serialize(t.dom,{}))).join(""))(e,o)}))};if(!0===t.selection){const t=(e=>N(ls(e),(e=>pe(e,ts.selectedSelector))))(e);t.length>=1&&o(t)}})),e.on("BeforeSetContent",(o=>{if(!0===o.selection&&!0===o.paste){const n=ls(e);P(n).each((n=>{Pt(n).each((r=>{const s=N(((e,t)=>{const o=document.createElement("div");return o.innerHTML=e,ke(he.fromDom(o))})(o.content),(e=>"meta"!==X(e))),l=le("table");if(1===s.length&&l(s[0])){o.preventDefault();const l=he.fromDom(e.getDoc()),a=Tr(l),c=((e,t,o)=>({element:e,clipboard:t,generators:o}))(n,s[0],a);t.pasteCells(r,c).each((()=>{e.focus()}))}}))}))}}))},cs=(e,t)=>({element:e,offset:t}),is=(e,t,o)=>e.property().isText(t)&&0===e.property().getText(t).trim().length||e.property().isComment(t)?o(t).bind((t=>is(e,t,o).orThunk((()=>y.some(t))))):y.none(),ms=(e,t)=>e.property().isText(t)?e.property().getText(t).length:e.property().children(t).length,ds=(e,t)=>{const o=is(e,t,e.query().prevSibling).getOr(t);if(e.property().isText(o))return cs(o,ms(e,o));const n=e.property().children(o);return n.length>0?ds(e,n[n.length-1]):cs(o,ms(e,o))},us=ds,fs=Pr(),gs=(e,t)=>{if(!Nt(e)){const o=(e=>_n(e).bind((e=>{return t=e,o=["fixed","relative","empty"],y.from(kn.exec(t)).bind((e=>{const t=Number(e[1]),n=e[2];return((e,t)=>R(t,(t=>R(On[t],(t=>e===t)))))(n,o)?y.some({value:t,unit:n}):y.none()}));var t,o})))(e);o.each((o=>{const n=o.value/2;Hn(e,n,o.unit),Hn(t,n,o.unit)}))}},hs=e=>O(e,u(0)),ps=(e,t,o,n,r)=>r(e.slice(0,t)).concat(n).concat(r(e.slice(o))),ws=e=>(t,o,n,r)=>{if(e(n)){const e=Math.max(r,t[o]-Math.abs(n)),s=Math.abs(e-t[o]);return n>=0?s:-s}return n},bs=ws((e=>e<0)),vs=ws(v),ys=()=>{const e=(e,t,o,n)=>{const r=(100+o)/100,s=Math.max(n,(e[t]+o)/r);return O(e,((e,o)=>(o===t?s:e/r)-e))},t=(t,o,n,r,s,l)=>l?e(t,o,r,s):((e,t,o,n,r)=>{const s=bs(e,t,n,r);return ps(e,t,o+1,[s,0],hs)})(t,o,n,r,s);return{resizeTable:(e,t)=>e(t),clampTableDelta:bs,calcLeftEdgeDeltas:t,calcMiddleDeltas:(e,o,n,r,s,l,a)=>t(e,n,r,s,l,a),calcRightEdgeDeltas:(t,o,n,r,s,l)=>{if(l)return e(t,n,r,s);{const e=bs(t,n,r,s);return hs(t.slice(0,n)).concat([e])}},calcRedestributedWidths:(e,t,o,n)=>{if(n){const n=(t+o)/t,r=O(e,(e=>e/n));return{delta:100*n-100,newSizes:r}}return{delta:o,newSizes:e}}}},xs=()=>{const e=(e,t,o,n,r)=>{const s=vs(e,n>=0?o:t,n,r);return ps(e,t,o+1,[s,-s],hs)};return{resizeTable:(e,t,o)=>{o&&e(t)},clampTableDelta:(e,t,o,n,r)=>{if(r){if(o>=0)return o;{const t=z(e,((e,t)=>e+t-n),0);return Math.max(-t,o)}}return bs(e,t,o,n)},calcLeftEdgeDeltas:e,calcMiddleDeltas:(t,o,n,r,s,l)=>e(t,n,r,s,l),calcRightEdgeDeltas:(e,t,o,n,r,s)=>{if(s)return hs(e);{const t=n/e.length;return O(e,u(t))}},calcRedestributedWidths:(e,t,o,n)=>({delta:0,newSizes:e})}},Cs=e=>$o(e).grid,Ss=le("th"),Ts=e=>j(e,(e=>Ss(e.element))),Rs=(e,t)=>e&&t?"sectionCells":e?"section":"cells",Ds=e=>{const t="thead"===e.section,o=dt(Os(e.cells),"th");return"tfoot"===e.section?{type:"footer"}:t||o?{type:"header",subType:Rs(t,o)}:{type:"body"}},Os=e=>{const t=N(e,(e=>Ss(e.element)));return 0===t.length?y.some("td"):t.length===e.length?y.some("th"):y.none()},ks=(e,t,o)=>Ge(o(e.element,t),!0,e.isLocked),Es=(e,t)=>e.section!==t?Ke(e.element,e.cells,t,e.isNew):e,Ns=()=>({transformRow:Es,transformCell:(e,t,o)=>{const n=o(e.element,t),r="td"!==X(n)?((e,t)=>{const o=qe(e,"td");Be(e,o);const n=ke(e);return Me(o,n),_e(e),o})(n):n;return Ge(r,e.isNew,e.isLocked)}}),Bs=()=>({transformRow:Es,transformCell:ks}),zs=()=>({transformRow:(e,t)=>Es(e,"thead"===t?"tbody":t),transformCell:ks}),As=Ns,Ws=Bs,Ls=zs,Ms=()=>({transformRow:f,transformCell:ks}),js=e=>mt(e,"[contenteditable]"),_s=(e,t=!1)=>Ze(e)?e.dom.isContentEditable:js(e).fold(u(t),(e=>"true"===Is(e))),Is=e=>e.dom.contentEditable,Ps=(e,t,o,n)=>{o===n?ue(e,t):ce(e,t,o)},Fs=(e,t,o)=>{F(nt(e,t)).fold((()=>ze(e,o)),(e=>Be(e,o)))},Hs=(e,t)=>{const o=[],n=[],r=e=>O(e,(e=>{e.isNew&&o.push(e.element);const t=e.element;return je(t),k(e.cells,(e=>{e.isNew&&n.push(e.element),Ps(e.element,"colspan",e.colspan,1),Ps(e.element,"rowspan",e.rowspan,1),Ae(t,e.element)})),t})),s=e=>M(e,(e=>O(e.cells,(e=>(Ps(e.element,"span",e.colspan,1),e.element))))),l=(t,o)=>{const n=((e,t)=>{const o=ct(e,t).getOrThunk((()=>{const o=he.fromTag(t,Ce(e).dom);return"thead"===t?Fs(e,"caption,colgroup",o):"colgroup"===t?Fs(e,"caption",o):Ae(e,o),o}));return je(o),o})(e,o),l=("colgroup"===o?s:r)(t);Me(n,l)},a=(t,o)=>{t.length>0?l(t,o):(t=>{ct(e,t).each(_e)})(o)},c=[],i=[],m=[],d=[];return k(t,(e=>{switch(e.section){case"thead":c.push(e);break;case"tbody":i.push(e);break;case"tfoot":m.push(e);break;case"colgroup":d.push(e)}})),a(d,"colgroup"),a(c,"thead"),a(i,"tbody"),a(m,"tfoot"),{newRows:o,newCells:n}},qs=(e,t)=>{if(0===e.length)return 0;const o=e[0];return W(e,(e=>!t(o.element,e.element))).getOr(e.length)},Vs=(e,t)=>{const o=O(e,(e=>O(e.cells,b)));return O(e,((n,r)=>{const s=M(n.cells,((n,s)=>{if(!1===o[r][s]){const m=((e,t,o,n)=>{const r=((e,t)=>e[t])(e,t),s="colgroup"===r.section,l=qs(r.cells.slice(o),n),a=s?1:qs(((e,t)=>O(e,(e=>Wo(e,t))))(e.slice(t),o),n);return{colspan:l,rowspan:a}})(e,r,s,t);return((e,t,n,r)=>{for(let s=e;s<e+n;s++)for(let e=t;e<t+r;e++)o[s][e]=!0})(r,s,m.rowspan,m.colspan),[(l=n.element,a=m.rowspan,c=m.colspan,i=n.isNew,{element:l,rowspan:a,colspan:c,isNew:i})]}return[];var l,a,c,i}));return((e,t,o,n)=>({element:e,cells:t,section:o,isNew:n}))(n.element,s,n.section,n.isNew)}))},$s=(e,t,o)=>{const n=[];k(e.colgroups,(r=>{const s=[];for(let n=0;n<e.grid.columns;n++){const r=Zo(e,n).map((e=>Ge(e.element,o,!1))).getOrThunk((()=>Ge(t.colGap(),!0,!1)));s.push(r)}n.push(Ke(r.element,s,"colgroup",o))}));for(let r=0;r<e.grid.rows;r++){const s=[];for(let n=0;n<e.grid.columns;n++){const l=Go(e,r,n).map((e=>Ge(e.element,o,e.isLocked))).getOrThunk((()=>Ge(t.gap(),!0,!1)));s.push(l)}const l=e.all[r],a=Ke(l.element,s,l.section,o);n.push(a)}return n},Us=e=>Vs(e,ve),Gs=(e,t)=>H(e.all,(e=>A(e.cells,(e=>ve(t,e.element))))),Ks=(e,t,o)=>{const n=O(t.selection,(t=>jt(t).bind((t=>Gs(e,t))).filter(o))),r=ut(n);return ft(r.length>0,r)},Ys=(e,t,o,n,r)=>(s,l,a,c)=>{const i=$o(s),m=y.from(null==c?void 0:c.section).getOrThunk(Ms);return t(i,l).map((t=>{const o=((e,t)=>$s(e,t,!1))(i,a),n=e(o,t,ve,r(a),m),s=Fo(n.grid);return{info:t,grid:Us(n.grid),cursor:n.cursor,lockedColumns:s}})).bind((e=>{const t=Hs(s,e.grid),r=y.from(null==c?void 0:c.sizing).getOrThunk((()=>or(s))),l=y.from(null==c?void 0:c.resize).getOrThunk(xs);return o(s,e.grid,e.info,{sizing:r,resize:l,section:m}),n(s),ue(s,Io),e.lockedColumns.length>0&&ce(s,Io,e.lockedColumns.join(",")),y.some({cursor:e.cursor,newRows:t.newRows,newCells:t.newCells})}))},Js=(e,t)=>Ks(e,t,v).map((e=>({cells:e,generators:t.generators,clipboard:t.clipboard}))),Qs=(e,t)=>Ks(e,t,v),Xs=(e,t)=>Ks(e,t,(e=>!e.isLocked)),Zs=(e,t)=>j(t,(t=>((e,t)=>Gs(e,t).exists((e=>!e.isLocked)))(e,t))),el=(e,t,o,n)=>{const r=jo(e).rows;let s=!0;for(let e=0;e<r.length;e++)for(let l=0;l<Mo(r[0]);l++){const a=r[e],c=Wo(a,l),i=o(c.element,t);i&&!s?zo(a,l,Ge(n(),!0,c.isLocked)):i&&(s=!1)}return e},tl=e=>{const t=t=>t(e),o=u(e),n=()=>r,r={tag:!0,inner:e,fold:(t,o)=>o(e),isValue:v,isError:b,map:t=>nl.value(t(e)),mapError:n,bind:t,exists:t,forall:t,getOr:o,or:n,getOrThunk:o,orThunk:n,getOrDie:o,each:t=>{t(e)},toOptional:()=>y.some(e)};return r},ol=e=>{const t=()=>o,o={tag:!1,inner:e,fold:(t,o)=>t(e),isValue:b,isError:v,map:t,mapError:t=>nl.error(t(e)),bind:t,exists:b,forall:v,getOr:f,or:f,getOrThunk:w,orThunk:w,getOrDie:(n=String(e),()=>{throw new Error(n)}),each:d,toOptional:y.none};var n;return o},nl={value:tl,error:ol,fromOption:(e,t)=>e.fold((()=>ol(t)),tl)},rl=(e,t)=>({rowDelta:0,colDelta:Mo(e[0])-Mo(t[0])}),sl=(e,t)=>({rowDelta:e.length-t.length,colDelta:0}),ll=(e,t,o,n)=>{const r="colgroup"===t.section?o.col:o.cell;return D(e,(e=>Ge(r(),!0,n(e))))},al=(e,t,o,n)=>{const r=e[e.length-1];return e.concat(D(t,(()=>{const e="colgroup"===r.section?o.colgroup:o.row,t=_o(r,e,f),s=ll(t.cells.length,t,o,(e=>Q(n,e.toString())));return Ao(t,s)})))},cl=(e,t,o,n)=>O(e,(e=>{const r=ll(t,e,o,b);return No(e,n,r)})),il=(e,t,o)=>{const n=t.colDelta<0?cl:f,r=t.rowDelta<0?al:f,s=Fo(e),l=Mo(e[0]),a=R(s,(e=>e===l-1)),c=n(e,Math.abs(t.colDelta),o,a?l-1:l),i=Fo(c);return r(c,Math.abs(t.rowDelta),o,_(i,v))},ml=(e,t,o,n)=>{const r=h(n,Wo(e[t],o).element),s=e[t];return e.length>1&&Mo(s)>1&&(o>0&&r(Lo(s,o-1))||o<s.cells.length-1&&r(Lo(s,o+1))||t>0&&r(Lo(e[t-1],o))||t<e.length-1&&r(Lo(e[t+1],o)))},dl=(e,t,o)=>N(o,(o=>o>=e.column&&o<=Mo(t[0])+e.column)),ul=(e,t,o,n,r)=>{((e,t,o,n)=>{t>0&&t<e[0].cells.length&&k(e,(e=>{const r=e.cells[t-1],s=e.cells[t];o(s.element,r.element)&&zo(e,t,Ge(n(),!0,s.isLocked))}))})(t,e,r,n.cell);const s=sl(o,t),l=il(o,s,n),a=sl(t,l),c=il(t,a,n);return O(c,((t,o)=>No(t,e,l[o].cells)))},fl=(e,t,o,n,r)=>{((e,t,o,n)=>{const r=jo(e).rows;if(t>0&&t<r.length){const e=((e,t)=>z(e,((e,o)=>R(e,(e=>t(e.element,o.element)))?e:e.concat([o])),[]))(r[t-1].cells,o);k(e,(e=>{let s=y.none();for(let l=t;l<r.length;l++)for(let t=0;t<Mo(r[0]);t++){const a=r[l],c=Wo(a,t);o(c.element,e.element)&&(s.isNone()&&(s=y.some(n())),s.each((e=>{zo(a,t,Ge(e,!0,c.isLocked))})))}}))}})(t,e,r,n.cell);const s=Fo(t),l=rl(t,o),a={...l,colDelta:l.colDelta-s.length},c=il(t,a,n),{cols:i,rows:m}=jo(c),d=Fo(c),u=rl(o,t),f={...u,colDelta:u.colDelta+d.length},g=(p=n,w=d,O(o,(e=>z(w,((t,o)=>{const n=ll(1,e,p,v)[0];return Bo(t,o,n)}),e)))),h=il(g,f,n);var p,w;return[...i,...m.slice(0,e),...h,...m.slice(e,m.length)]},gl=(e,t,o,n,r)=>{const{rows:s,cols:l}=jo(e),a=s.slice(0,t),c=s.slice(t);return[...l,...a,((e,t,o,n)=>_o(e,(e=>n(e,o)),t))(s[o],((e,o)=>t>0&&t<s.length&&n(Lo(s[t-1],o),Lo(s[t],o))?Wo(s[t],o):Ge(r(e.element,n),!0,e.isLocked)),n,r),...c]},hl=(e,t,o,n,r)=>O(e,(e=>{const s=t>0&&t<Mo(e)&&n(Lo(e,t-1),Lo(e,t)),l=((e,t,o,n,r,s,l)=>{if("colgroup"!==o&&n)return Wo(e,t);{const t=Wo(e,r);return Ge(l(t.element,s),!0,!1)}})(e,t,e.section,s,o,n,r);return Bo(e,t,l)})),pl=(e,t,o,n)=>((e,t,o,n)=>void 0!==Lo(e[t],o)&&t>0&&n(Lo(e[t-1],o),Lo(e[t],o)))(e,t,o,n)||((e,t,o)=>t>0&&o(Lo(e,t-1),Lo(e,t)))(e[t],o,n),wl=(e,t,o,n)=>{const r=e=>(e=>"row"===e?Bt(t):Nt(t))(e)?`${e}group`:e;return e?Ss(t)?r(o):null:n&&Ss(t)?r("row"===o?"col":"row"):null},bl=(e,t,o)=>Ge(o(e.element,t),!0,e.isLocked),vl=(e,t,o,n,r,s,l)=>O(e,((e,a)=>((e,c)=>{const i=e.cells,m=O(i,((e,c)=>{if((e=>R(t,(t=>o(e.element,t.element))))(e)){const t=l(e,a,c)?r(e,o,n):e;return s(t,a,c).each((e=>{var o,n;o=t.element,n={scope:y.from(e)},$(n,((e,t)=>{e.fold((()=>{ue(o,t)}),(e=>{ae(o.dom,t,e)}))}))})),t}return e}));return Ke(e.element,m,e.section,e.isNew)})(e))),yl=(e,t,o)=>M(e,((n,r)=>pl(e,r,t,o)?[]:[Wo(n,t)])),xl=(e,t,o,n,r)=>{const s=jo(e).rows,l=M(t,(e=>yl(s,e,n))),a=O(s,(e=>Ts(e.cells))),c=((e,t)=>j(t,f)&&Ts(e)?v:(e,o,n)=>!("th"===X(e.element)&&t[o]))(l,a),i=((e,t)=>(o,n)=>y.some(wl(e,o.element,"row",t[n])))(o,a);return vl(e,l,n,r,bl,i,c)},Cl=(e,t,o,n)=>{const r=jo(e).rows,s=O(t,(e=>Wo(r[e.row],e.column)));return vl(e,s,o,n,bl,y.none,v)},Sl=e=>{if(!s(e))throw new Error("cases must be an array");if(0===e.length)throw new Error("there must be at least one case");const t=[],o={};return k(e,((n,r)=>{const l=q(n);if(1!==l.length)throw new Error("one and only one name per case");const a=l[0],c=n[a];if(void 0!==o[a])throw new Error("duplicate key detected:"+a);if("cata"===a)throw new Error("cannot have a case named cata (sorry)");if(!s(c))throw new Error("case arguments must be an array");t.push(a),o[a]=(...o)=>{const n=o.length;if(n!==c.length)throw new Error("Wrong number of arguments to case "+a+". Expected "+c.length+" ("+c+"), got "+n);return{fold:(...t)=>{if(t.length!==e.length)throw new Error("Wrong number of arguments to fold. Expected "+e.length+", got "+t.length);return t[r].apply(null,o)},match:e=>{const n=q(e);if(t.length!==n.length)throw new Error("Wrong number of arguments to match. Expected: "+t.join(",")+"\nActual: "+n.join(","));if(!j(t,(e=>T(n,e))))throw new Error("Not all branches were specified when using match. Specified: "+n.join(", ")+"\nRequired: "+t.join(", "));return e[a].apply(null,o)},log:e=>{console.log(e,{constructors:t,constructor:a,params:o})}}}})),o},Tl={...Sl([{none:[]},{only:["index"]},{left:["index","next"]},{middle:["prev","index","next"]},{right:["prev","index"]}])},Rl=(e,t,o)=>{let n=0;for(let r=e;r<t;r++)n+=void 0!==o[r]?o[r]:0;return n},Dl=(e,t)=>{const o=Jo(e);return O(o,(e=>{const o=Rl(e.row,e.row+e.rowspan,t);return{element:e.element,height:o,rowspan:e.rowspan}}))},Ol=(e,t,o)=>{const n=((e,t)=>Xo(e)?((e,t)=>{const o=Qo(e);return O(o,((e,o)=>({element:e.element,width:t[o],colspan:e.colspan})))})(e,t):((e,t)=>{const o=Jo(e);return O(o,(e=>{const o=Rl(e.column,e.column+e.colspan,t);return{element:e.element,width:o,colspan:e.colspan}}))})(e,t))(e,t);k(n,(e=>{o.setElementWidth(e.element,e.width)}))},kl=(e,t,o,n,r)=>{const s=$o(e),l=r.getCellDelta(t),a=r.getWidths(s,r),c=o===s.grid.columns-1,i=n.clampTableDelta(a,o,l,r.minCellWidth(),c),m=((e,t,o,n,r)=>{const s=e.slice(0),l=((e,t)=>0===e.length?Tl.none():1===e.length?Tl.only(0):0===t?Tl.left(0,1):t===e.length-1?Tl.right(t-1,t):t>0&&t<e.length-1?Tl.middle(t-1,t,t+1):Tl.none())(e,t),a=u(O(s,u(0)));return l.fold(a,(e=>n.singleColumnWidth(s[e],o)),((e,t)=>r.calcLeftEdgeDeltas(s,e,t,o,n.minCellWidth(),n.isRelative)),((e,t,l)=>r.calcMiddleDeltas(s,e,t,l,o,n.minCellWidth(),n.isRelative)),((e,t)=>r.calcRightEdgeDeltas(s,e,t,o,n.minCellWidth(),n.isRelative)))})(a,o,i,r,n),d=O(m,((e,t)=>e+a[t]));Ol(s,d,r),n.resizeTable(r.adjustTableWidth,i,c)},El=e=>z(e,((e,t)=>R(e,(e=>e.column===t.column))?e:e.concat([t])),[]).sort(((e,t)=>e.column-t.column)),Nl=le("col"),Bl=le("colgroup"),zl=e=>"tr"===X(e)||Bl(e),Al=e=>({element:e,colspan:kt(e,"colspan",1),rowspan:kt(e,"rowspan",1)}),Wl=e=>de(e,"scope").map((e=>e.substr(0,3))),Ll=(e,t=Al)=>{const o=o=>{if(zl(o))return Bl((r={element:o}).element)?e.colgroup(r):e.row(r);{const r=o,s=(t=>Nl(t.element)?e.col(t):e.cell(t))(t(r));return n=y.some({item:r,replacement:s}),s}var r};let n=y.none();return{getOrInit:(e,t)=>n.fold((()=>o(e)),(n=>t(e,n.item)?n.replacement:o(e)))}},Ml=e=>t=>{const o=[],n=n=>{const r="td"===e?{scope:null}:{},s=t.replace(n,e,r);return o.push({item:n,sub:s}),s};return{replaceOrInit:(e,t)=>{if(zl(e)||Nl(e))return e;{const r=e;return((e,t)=>A(o,(o=>t(o.item,e))))(r,t).fold((()=>n(r)),(o=>t(e,o.item)?o.sub:n(r)))}}}},jl=e=>({unmerge:t=>{const o=Wl(t);return o.each((e=>ce(t,"scope",e))),()=>{const n=e.cell({element:t,colspan:1,rowspan:1});return Ot(n,"width"),Ot(t,"width"),o.each((e=>ce(n,"scope",e))),n}},merge:e=>(Ot(e[0],"width"),(()=>{const t=ut(O(e,Wl));if(0===t.length)return y.none();{const e=t[0],o=["row","col"];return R(t,(t=>t!==e&&T(o,t)))?y.none():y.from(e)}})().fold((()=>ue(e[0],"scope")),(t=>ce(e[0],"scope",t+"group"))),u(e[0]))}),_l=["body","p","div","article","aside","figcaption","figure","footer","header","nav","section","ol","ul","table","thead","tfoot","tbody","caption","tr","td","th","h1","h2","h3","h4","h5","h6","blockquote","pre","address"],Il=Pr(),Pl=e=>((e,t)=>{const o=e.property().name(t);return T(_l,o)})(Il,e),Fl=e=>((e,t)=>{const o=e.property().name(t);return T(["ol","ul"],o)})(Il,e),Hl=e=>{const t=le("br"),o=e=>gr(e).bind((o=>{const n=Oe(o).map((e=>!!Pl(e)||!!((e,t)=>T(["br","img","hr","input"],e.property().name(t)))(Il,e)&&"img"!==X(e))).getOr(!1);return Te(o).map((r=>{return!0===n||("li"===X(s=r)||lt(s,Fl).isSome())||t(o)||Pl(r)&&!ve(e,r)?[]:[he.fromTag("br")];var s}))})).getOr([]),n=(()=>{const n=M(e,(e=>{const n=ke(e);return(e=>j(e,(e=>t(e)||ne(e)&&0===ar(e).trim().length)))(n)?[]:n.concat(o(e))}));return 0===n.length?[he.fromTag("br")]:n})();je(e[0]),Me(e[0],n)},ql=e=>_s(e,!0),Vl=e=>{0===_t(e).length&&_e(e)},$l=(e,t)=>({grid:e,cursor:t}),Ul=(e,t,o)=>{const n=((e,t,o)=>{var n,r;const s=jo(e).rows;return y.from(null===(r=null===(n=s[t])||void 0===n?void 0:n.cells[o])||void 0===r?void 0:r.element).filter(ql).orThunk((()=>(e=>H(e,(e=>H(e.cells,(e=>{const t=e.element;return ft(ql(t),t)})))))(s)))})(e,t,o);return $l(e,n)},Gl=e=>z(e,((e,t)=>R(e,(e=>e.row===t.row))?e:e.concat([t])),[]).sort(((e,t)=>e.row-t.row)),Kl=(e,t)=>(o,n,r,s,l)=>{const a=Gl(n),c=O(a,(e=>e.row)),i=((e,t,o,n,r,s,l)=>{const{cols:a,rows:c}=jo(e),i=c[t[0]],m=M(t,(e=>((e,t,o)=>{const n=e[t];return M(n.cells,((n,r)=>pl(e,t,r,o)?[]:[n]))})(c,e,r))),d=O(i.cells,((e,t)=>Ts(yl(c,t,r)))),u=[...c];k(t,(e=>{u[e]=l.transformRow(c[e],o)}));const g=[...a,...u],h=((e,t)=>j(t,f)&&Ts(e.cells)?v:(e,o,n)=>!("th"===X(e.element)&&t[n]))(i,d),p=((e,t)=>(o,n,r)=>y.some(wl(e,o.element,"col",t[r])))(n,d);return vl(g,m,r,s,l.transformCell,p,h)})(o,c,e,t,r,s.replaceOrInit,l);return Ul(i,n[0].row,n[0].column)},Yl=Kl("thead",!0),Jl=Kl("tbody",!1),Ql=Kl("tfoot",!1),Xl=(e,t,o)=>{const n=((e,t)=>qt(e,(()=>t)))(e,o.section),r=Uo(n);return $s(r,t,!0)},Zl=(e,t,o,n)=>((e,t,o,n)=>{const r=Uo(t),s=n.getWidths(r,n);Ol(r,s,n)})(0,t,0,n.sizing),ea=(e,t,o,n)=>((e,t,o,n,r)=>{const s=Uo(t),l=n.getWidths(s,n),a=n.pixelWidth(),{newSizes:c,delta:i}=r.calcRedestributedWidths(l,a,o.pixelDelta,n.isRelative);Ol(s,c,n),n.adjustTableWidth(i)})(0,t,o,n.sizing,n.resize),ta=(e,t)=>R(t,(e=>0===e.column&&e.isLocked)),oa=(e,t)=>R(t,(t=>t.column+t.colspan>=e.grid.columns&&t.isLocked)),na=(e,t)=>{const o=en(e),n=El(t);return z(n,((e,t)=>e+o[t.column].map(ko).getOr(0)),0)},ra=e=>(t,o)=>Qs(t,o).filter((o=>!(e?ta:oa)(t,o))).map((e=>({details:e,pixelDelta:na(t,e)}))),sa=e=>(t,o)=>Js(t,o).filter((o=>!(e?ta:oa)(t,o.cells))),la=Ml("th"),aa=Ml("td"),ca=Ys(((e,t,o,n)=>{const r=t[0].row,s=Gl(t),l=B(s,((e,t)=>({grid:gl(e.grid,r,t.row+e.delta,o,n.getOrInit),delta:e.delta+1})),{grid:e,delta:0}).grid;return Ul(l,r,t[0].column)}),Qs,d,d,Ll),ia=Ys(((e,t,o,n)=>{const r=Gl(t),s=r[r.length-1],l=s.row+s.rowspan,a=B(r,((e,t)=>gl(e,l,t.row,o,n.getOrInit)),e);return Ul(a,l,t[0].column)}),Qs,d,d,Ll),ma=Ys(((e,t,o,n)=>{const r=t.details,s=El(r),l=s[0].column,a=B(s,((e,t)=>({grid:hl(e.grid,l,t.column+e.delta,o,n.getOrInit),delta:e.delta+1})),{grid:e,delta:0}).grid;return Ul(a,r[0].row,l)}),ra(!0),ea,d,Ll),da=Ys(((e,t,o,n)=>{const r=t.details,s=r[r.length-1],l=s.column+s.colspan,a=El(r),c=B(a,((e,t)=>hl(e,l,t.column,o,n.getOrInit)),e);return Ul(c,r[0].row,l)}),ra(!1),ea,d,Ll),ua=Ys(((e,t,o,n)=>{const r=El(t.details),s=((e,t)=>M(e,(e=>{const o=e.cells,n=B(t,((e,t)=>t>=0&&t<e.length?e.slice(0,t).concat(e.slice(t+1)):e),o);return n.length>0?[Ke(e.element,n,e.section,e.isNew)]:[]})))(e,O(r,(e=>e.column))),l=s.length>0?s[0].cells.length-1:0;return Ul(s,r[0].row,Math.min(r[0].column,l))}),((e,t)=>Xs(e,t).map((t=>({details:t,pixelDelta:-na(e,t)})))),ea,Vl,Ll),fa=Ys(((e,t,o,n)=>{const r=Gl(t),s=((e,t,o)=>{const{rows:n,cols:r}=jo(e);return[...r,...n.slice(0,t),...n.slice(o+1)]})(e,r[0].row,r[r.length-1].row),l=s.length>0?s.length-1:0;return Ul(s,Math.min(t[0].row,l),t[0].column)}),Qs,d,Vl,Ll),ga=Ys(((e,t,o,n)=>{const r=El(t),s=O(r,(e=>e.column)),l=xl(e,s,!0,o,n.replaceOrInit);return Ul(l,t[0].row,t[0].column)}),Xs,d,d,la),ha=Ys(((e,t,o,n)=>{const r=El(t),s=O(r,(e=>e.column)),l=xl(e,s,!1,o,n.replaceOrInit);return Ul(l,t[0].row,t[0].column)}),Xs,d,d,aa),pa=Ys(Yl,Xs,d,d,la),wa=Ys(Jl,Xs,d,d,aa),ba=Ys(Ql,Xs,d,d,aa),va=Ys(((e,t,o,n)=>{const r=Cl(e,t,o,n.replaceOrInit);return Ul(r,t[0].row,t[0].column)}),Xs,d,d,la),ya=Ys(((e,t,o,n)=>{const r=Cl(e,t,o,n.replaceOrInit);return Ul(r,t[0].row,t[0].column)}),Xs,d,d,aa),xa=Ys(((e,t,o,n)=>{const r=t.cells;Hl(r);const s=((e,t,o,n)=>{const r=jo(e).rows;if(0===r.length)return e;for(let e=t.startRow;e<=t.finishRow;e++)for(let o=t.startCol;o<=t.finishCol;o++){const t=r[e],s=Wo(t,o).isLocked;zo(t,o,Ge(n(),!1,s))}return e})(e,t.bounds,0,n.merge(r));return $l(s,y.from(r[0]))}),((e,t)=>((e,t)=>t.mergable)(0,t).filter((t=>Zs(e,t.cells)))),Zl,d,jl),Ca=Ys(((e,t,o,n)=>{const r=B(t,((e,t)=>el(e,t,o,n.unmerge(t))),e);return $l(r,y.from(t[0]))}),((e,t)=>((e,t)=>t.unmergable)(0,t).filter((t=>Zs(e,t)))),Zl,d,jl),Sa=Ys(((e,t,o,n)=>{const r=((e,t)=>{const o=$o(e);return $s(o,t,!0)})(t.clipboard,t.generators);var s,l;return((e,t,o,n,r)=>{const s=Fo(t),l=((e,t,o)=>{const n=Mo(t[0]),r=jo(t).cols.length+e.row,s=D(n-e.column,(t=>t+e.column));return{row:r,column:A(s,(e=>j(o,(t=>t!==e)))).getOr(n-1)}})(e,t,s),a=jo(o).rows,c=dl(l,a,s),i=((e,t,o)=>{if(e.row>=t.length||e.column>Mo(t[0]))return nl.error("invalid start address out of table bounds, row: "+e.row+", column: "+e.column);const n=t.slice(e.row),r=n[0].cells.slice(e.column),s=Mo(o[0]),l=o.length;return nl.value({rowDelta:n.length-l,colDelta:r.length-s})})(l,t,a);return i.map((e=>{const o={...e,colDelta:e.colDelta-c.length},s=il(t,o,n),i=Fo(s),m=dl(l,a,i);return((e,t,o,n,r,s)=>{const l=e.row,a=e.column,c=l+o.length,i=a+Mo(o[0])+s.length,m=_(s,v);for(let e=l;e<c;e++){let s=0;for(let c=a;c<i;c++){if(m[c]){s++;continue}ml(t,e,c,r)&&el(t,Lo(t[e],c),r,n.cell);const i=c-a-s,d=Wo(o[e-l],i),u=d.element,f=n.replace(u);zo(t[e],c,Ge(f,!0,d.isLocked))}}return t})(l,s,a,n,r,m)}))})((s=t.row,l=t.column,{row:s,column:l}),e,r,t.generators,o).fold((()=>$l(e,y.some(t.element))),(e=>Ul(e,t.row,t.column)))}),((e,t)=>jt(t.element).bind((o=>Gs(e,o).map((e=>({...e,generators:t.generators,clipboard:t.clipboard})))))),Zl,d,Ll),Ta=Ys(((e,t,o,n)=>{const r=jo(e).rows,s=t.cells[0].column,l=r[t.cells[0].row],a=Xl(t.clipboard,t.generators,l),c=ul(s,e,a,t.generators,o);return Ul(c,t.cells[0].row,t.cells[0].column)}),sa(!0),d,d,Ll),Ra=Ys(((e,t,o,n)=>{const r=jo(e).rows,s=t.cells[t.cells.length-1].column+t.cells[t.cells.length-1].colspan,l=r[t.cells[0].row],a=Xl(t.clipboard,t.generators,l),c=ul(s,e,a,t.generators,o);return Ul(c,t.cells[0].row,t.cells[0].column)}),sa(!1),d,d,Ll),Da=Ys(((e,t,o,n)=>{const r=jo(e).rows,s=t.cells[0].row,l=r[s],a=Xl(t.clipboard,t.generators,l),c=fl(s,e,a,t.generators,o);return Ul(c,t.cells[0].row,t.cells[0].column)}),Js,d,d,Ll),Oa=Ys(((e,t,o,n)=>{const r=jo(e).rows,s=t.cells[t.cells.length-1].row+t.cells[t.cells.length-1].rowspan,l=r[t.cells[0].row],a=Xl(t.clipboard,t.generators,l),c=fl(s,e,a,t.generators,o);return Ul(c,t.cells[0].row,t.cells[0].column)}),Js,d,d,Ll),ka=(e,t)=>{const o=$o(e);return Qs(o,t).bind((e=>{const t=e[e.length-1],n=e[0].column,r=t.column+t.colspan,s=L(O(o.all,(e=>N(e.cells,(e=>e.column>=n&&e.column<r)))));return Os(s)})).getOr("")},Ea=(e,t)=>{const o=$o(e);return Qs(o,t).bind(Os).getOr("")},Na=(e,t)=>{const o=$o(e);return Qs(o,t).bind((e=>{const t=e[e.length-1],n=e[0].row,r=t.row+t.rowspan;return(e=>{const t=O(e,(e=>Ds(e).type)),o=T(t,"header"),n=T(t,"footer");if(o||n){const e=T(t,"body");return!o||e||n?o||e||!n?y.none():y.some("footer"):y.some("header")}return y.some("body")})(o.all.slice(n,r))})).getOr("")},Ba=(e,t)=>e.dispatch("NewRow",{node:t}),za=(e,t)=>e.dispatch("NewCell",{node:t}),Aa=(e,t,o)=>{e.dispatch("TableModified",{...o,table:t})},Wa={structure:!1,style:!0},La={structure:!0,style:!1},Ma={structure:!0,style:!0},ja=e=>t=>t.options.get(e),_a=e=>y.from(e.options.get("table_clone_elements")),Ia=ja("table_header_type"),Pa=ja("table_column_resizing"),Fa=e=>"preservetable"===Pa(e),Ha=e=>"resizetable"===Pa(e),qa=ja("table_sizing_mode"),Va=e=>"relative"===qa(e),$a=e=>"fixed"===qa(e),Ua=e=>"responsive"===qa(e),Ga=ja("table_resize_bars"),Ka=ja("table_default_attributes"),Ya=ja("table_use_colgroups"),Ja=(e,t)=>Va(e)?rr(t):$a(e)?nr(t):or(t),Qa=(e,t,o)=>{const n=e=>"table"===X(Rr(e)),r=_a(e),s=Ha(e)?d:gs,l=t=>{switch(Ia(e)){case"section":return As();case"sectionCells":return Ws();case"cells":return Ls();default:return((e,t)=>{var o;switch((o=$o(e),H(o.all,(e=>{const t=Ds(e);return"header"===t.type?y.from(t.subType):y.none()}))).getOr(t)){case"section":return Ns();case"sectionCells":return Bs();case"cells":return zs()}})(t,"section")}},a=(n,s,a,c)=>(i,m,d=!1)=>{Or(i);const u=he.fromDom(e.getDoc()),f=Sr(a,u,r),g={sizing:Ja(e,i),resize:Ha(e)?ys():xs(),section:l(i)};return s(i)?n(i,m,f,g).bind((n=>{t.refresh(i.dom),k(n.newRows,(t=>{Ba(e,t.dom)})),k(n.newCells,(t=>{za(e,t.dom)}));const r=((t,n)=>n.cursor.fold((()=>{const n=_t(t);return P(n).filter(Ze).map((n=>{o.clearSelectedCells(t.dom);const r=e.dom.createRng();return r.selectNode(n.dom),e.selection.setRng(r),ce(n,"data-mce-selected","1"),r}))}),(n=>{const r=us(fs,n),s=e.dom.createRng();return s.setStart(r.element.dom,r.offset),s.setEnd(r.element.dom,r.offset),e.selection.setRng(s),o.clearSelectedCells(t.dom),y.some(s)})))(i,n);return Ze(i)&&(Or(i),d||Aa(e,i.dom,c)),r.map((e=>({rng:e,effect:c})))})):y.none()},c=a(fa,(t=>!1===n(e)||Cs(t).rows>1),d,La),i=a(ua,(t=>!1===n(e)||Cs(t).columns>1),d,La);return{deleteRow:c,deleteColumn:i,insertRowsBefore:a(ca,v,d,La),insertRowsAfter:a(ia,v,d,La),insertColumnsBefore:a(ma,v,s,La),insertColumnsAfter:a(da,v,s,La),mergeCells:a(xa,v,d,La),unmergeCells:a(Ca,v,d,La),pasteColsBefore:a(Ta,v,d,La),pasteColsAfter:a(Ra,v,d,La),pasteRowsBefore:a(Da,v,d,La),pasteRowsAfter:a(Oa,v,d,La),pasteCells:a(Sa,v,d,Ma),makeCellsHeader:a(va,v,d,La),unmakeCellsHeader:a(ya,v,d,La),makeColumnsHeader:a(ga,v,d,La),unmakeColumnsHeader:a(ha,v,d,La),makeRowsHeader:a(pa,v,d,La),makeRowsBody:a(wa,v,d,La),makeRowsFooter:a(ba,v,d,La),getTableRowType:Na,getTableCellType:Ea,getTableColType:ka}},Xa=(e,t,o)=>{const n=kt(e,t,1);1===o||n<=1?ue(e,t):ce(e,t,Math.min(o,n))},Za=Sl([{invalid:["raw"]},{pixels:["value"]},{percent:["value"]}]),ec=(e,t,o)=>{const n=o.substring(0,o.length-e.length),r=parseFloat(n);return n===r.toString()?t(r):Za.invalid(o)},tc={...Za,from:e=>wt(e,"%")?ec("%",Za.percent,e):wt(e,"px")?ec("px",Za.pixels,e):Za.invalid(e)},oc=(e,t,o)=>{const n=tc.from(o),r=j(e,(e=>"0px"===e))?((e,t)=>{const o=e.fold((()=>u("")),(e=>u(e/t+"px")),(()=>u(100/t+"%")));return D(t,o)})(n,e.length):((e,t,o)=>e.fold((()=>t),(e=>((e,t,o)=>{const n=o/t;return O(e,(e=>tc.from(e).fold((()=>e),(e=>e*n+"px"),(e=>e/100*o+"px"))))})(t,o,e)),(e=>((e,t)=>O(e,(e=>tc.from(e).fold((()=>e),(e=>e/t*100+"%"),(e=>e+"%")))))(t,o))))(n,e,t);return sc(r)},nc=(e,t)=>0===e.length?t:B(e,((e,t)=>tc.from(t).fold(u(0),f,f)+e),0),rc=(e,t)=>tc.from(e).fold(u(e),(e=>e+t+"px"),(e=>e+t+"%")),sc=e=>{if(0===e.length)return e;const t=B(e,((e,t)=>{const o=tc.from(t).fold((()=>({value:t,remainder:0})),(e=>((e,t)=>{const o=Math.floor(e);return{value:o+"px",remainder:e-o}})(e)),(e=>({value:e+"%",remainder:0})));return{output:[o.value].concat(e.output),remainder:e.remainder+o.remainder}}),{output:[],remainder:0}),o=t.output;return o.slice(0,o.length-1).concat([rc(o[o.length-1],Math.round(t.remainder))])},lc=tc.from,ac=e=>lc(e).fold(u("px"),u("px"),u("%")),cc=(e,t,o)=>{const n=$o(e),r=n.all,s=Jo(n),l=Qo(n);t.each((t=>{const o=ac(t),r=Oo(e),a=((e,t)=>Yn(e,t,Un,Jn))(n,e),c=oc(a,r,t);Xo(n)?((e,t,o)=>{k(t,((t,n)=>{const r=nc([e[n]],At());Ct(t.element,"width",r+o)}))})(c,l,o):((e,t,o)=>{k(t,(t=>{const n=e.slice(t.column,t.colspan+t.column),r=nc(n,At());Ct(t.element,"width",r+o)}))})(c,s,o),Ct(e,"width",t)})),o.each((t=>{const o=ac(t),l=ln(e),a=((e,t,o)=>Xn(e,t,o,Gn,Jn))(n,e,Tn);((e,t,o,n)=>{k(o,(t=>{const o=e.slice(t.row,t.rowspan+t.row),r=nc(o,Wt());Ct(t.element,"height",r+n)})),k(t,((t,o)=>{Ct(t.element,"height",e[o])}))})(oc(a,l,t),r,s,o),Ct(e,"height",t)}))},ic=e=>_n(e).exists((e=>En.test(e))),mc=e=>_n(e).exists((e=>Nn.test(e))),dc=e=>_n(e).isNone(),uc=e=>{ue(e,"width")},fc=e=>{const t=qn(e);cc(e,y.some(t),y.none()),uc(e)},gc=e=>{const t=(e=>Oo(e)+"px")(e);cc(e,y.some(t),y.none()),uc(e)},hc=e=>{Ot(e,"width");const t=It(e),o=t.length>0?t:_t(e);k(o,(e=>{Ot(e,"width"),uc(e)})),uc(e)},pc={styles:{"border-collapse":"collapse",width:"100%"},attributes:{border:"1"},colGroups:!1},wc=(e,t,o,n)=>D(e,(e=>((e,t,o,n)=>{const r=he.fromTag("tr");for(let s=0;s<e;s++){const e=he.fromTag(n<t||s<o?"th":"td");s<o&&ce(e,"scope","row"),n<t&&ce(e,"scope","col"),Ae(e,he.fromTag("br")),Ae(r,e)}return r})(t,o,n,e))),bc=(e,t)=>{e.selection.select(t.dom,!0),e.selection.collapse(!0)},vc=(e,t,o,r,s)=>{const l=(e=>{const t=e.options,o=t.get("table_default_styles");return t.isSet("table_default_styles")?o:((e,t)=>{var o;if($a(e)){const n=e.dom,r=null!==(o=n.getParent(e.selection.getStart(),n.isBlock))&&void 0!==o?o:e.getBody(),s=Eo(he.fromDom(r));return{...t,width:s+"px"}}return Ua(e)?K(t,((e,t)=>"width"!==t)):t})(e,o)})(e),a={styles:l,attributes:Ka(e),colGroups:Ya(e)};return e.undoManager.ignore((()=>{const n=((e,t,o,n,r,s=pc)=>{const l=he.fromTag("table"),a="cells"!==r;St(l,s.styles),ie(l,s.attributes),s.colGroups&&Ae(l,(e=>{const t=he.fromTag("colgroup");return D(e,(()=>Ae(t,he.fromTag("col")))),t})(t));const c=Math.min(e,o);if(a&&o>0){const e=he.fromTag("thead");Ae(l,e);const s=wc(o,t,"sectionCells"===r?c:0,n);Me(e,s)}const i=he.fromTag("tbody");Ae(l,i);const m=wc(a?e-c:e,t,a?0:o,n);return Me(i,m),l})(o,t,s,r,Ia(e),a);ce(n,"data-mce-id","__mce");const l=(e=>{const t=he.fromTag("div"),o=he.fromDom(e.dom.cloneNode(!0));return Ae(t,o),(e=>e.dom.innerHTML)(t)})(n);e.insertContent(l),e.addVisual()})),it(Rr(e),'table[data-mce-id="__mce"]').map((t=>($a(e)?gc(t):Ua(e)?hc(t):(Va(e)||(e=>n(e)&&-1!==e.indexOf("%"))(l.width))&&fc(t),Or(t),ue(t,"data-mce-id"),((e,t)=>{k(rt(t,"tr"),(t=>{Ba(e,t.dom),k(rt(t,"th,td"),(t=>{za(e,t.dom)}))}))})(e,t),((e,t)=>{it(t,"td,th").each(h(bc,e))})(e,t),t.dom))).getOr(null)};var yc=tinymce.util.Tools.resolve("tinymce.FakeClipboard");const xc="x-tinymce/dom-table-",Cc=xc+"rows",Sc=xc+"columns",Tc=e=>{const t=yc.FakeClipboardItem(e);yc.write([t])},Rc=e=>{var t;const o=null!==(t=yc.read())&&void 0!==t?t:[];return H(o,(t=>y.from(t.getType(e))))},Dc=e=>{Rc(e).isSome()&&yc.clear()},Oc=e=>{e.fold(Ec,(e=>Tc({[Cc]:e})))},kc=()=>Rc(Cc),Ec=()=>Dc(Cc),Nc=e=>{e.fold(zc,(e=>Tc({[Sc]:e})))},Bc=()=>Rc(Sc),zc=()=>Dc(Sc),Ac=e=>rs(kr(e),Dr(e)),Wc=(e,t)=>{const o=Dr(e),s=e=>Pt(e,o),l=t=>(e=>ss(kr(e),Dr(e)))(e).bind((e=>s(e).map((o=>t(o,e))))),a=t=>{e.focus()},c=(t,o=!1)=>l(((n,r)=>{const s=os(ls(e),n,r);t(n,s,o).each(a)})),i=()=>l(((t,o)=>((e,t,o)=>{const n=$o(e);return Qs(n,t).bind((e=>{const t=$s(n,o,!1),r=jo(t).rows.slice(e[0].row,e[e.length-1].row+e[e.length-1].rowspan),s=M(r,(e=>{const t=N(e.cells,(e=>!e.isLocked));return t.length>0?[{...e,cells:t}]:[]})),l=Us(s);return ft(l.length>0,l)})).map((e=>O(e,(e=>{const t=Fe(e.element);return k(e.cells,(e=>{const o=He(e.element);Ps(o,"colspan",e.colspan,1),Ps(o,"rowspan",e.rowspan,1),Ae(t,o)})),t}))))})(t,os(ls(e),t,o),Sr(d,he.fromDom(e.getDoc()),y.none())))),u=()=>l(((t,o)=>((e,t)=>{const o=$o(e);return Xs(o,t).map((e=>{const t=e[e.length-1],n=e[0].column,r=t.column+t.colspan,s=((e,t,o)=>{if(Xo(e)){const n=N(Qo(e),(e=>e.column>=t&&e.column<o)),r=O(n,(e=>{const n=He(e.element);return Xa(n,"span",o-t),n})),s=he.fromTag("colgroup");return Me(s,r),[s]}return[]})(o,n,r),l=((e,t,o)=>O(e.all,(e=>{const n=N(e.cells,(e=>e.column>=t&&e.column<o)),r=O(n,(e=>{const n=He(e.element);return Xa(n,"colspan",o-t),n})),s=he.fromTag("tr");return Me(s,r),s})))(o,n,r);return[...s,...l]}))})(t,os(ls(e),t,o)))),f=(t,o)=>o().each((o=>{const n=O(o,(e=>He(e)));l(((o,r)=>{const s=Tr(he.fromDom(e.getDoc())),l=((e,t,o,n)=>({selection:Xr(e),clipboard:o,generators:n}))(ls(e),0,n,s);t(o,l).each(a)}))})),g=e=>(t,o)=>((e,t)=>Q(e,t)?y.from(e.type):y.none())(o,"type").each((t=>{c(e(t),o.no_events)}));$({mceTableSplitCells:()=>c(t.unmergeCells),mceTableMergeCells:()=>c(t.mergeCells),mceTableInsertRowBefore:()=>c(t.insertRowsBefore),mceTableInsertRowAfter:()=>c(t.insertRowsAfter),mceTableInsertColBefore:()=>c(t.insertColumnsBefore),mceTableInsertColAfter:()=>c(t.insertColumnsAfter),mceTableDeleteCol:()=>c(t.deleteColumn),mceTableDeleteRow:()=>c(t.deleteRow),mceTableCutCol:()=>u().each((e=>{Nc(e),c(t.deleteColumn)})),mceTableCutRow:()=>i().each((e=>{Oc(e),c(t.deleteRow)})),mceTableCopyCol:()=>u().each((e=>Nc(e))),mceTableCopyRow:()=>i().each((e=>Oc(e))),mceTablePasteColBefore:()=>f(t.pasteColsBefore,Bc),mceTablePasteColAfter:()=>f(t.pasteColsAfter,Bc),mceTablePasteRowBefore:()=>f(t.pasteRowsBefore,kc),mceTablePasteRowAfter:()=>f(t.pasteRowsAfter,kc),mceTableDelete:()=>Ac(e).each((t=>{Pt(t,o).filter(p(o)).each((t=>{const o=he.fromText("");if(Be(t,o),_e(t),e.dom.isEmpty(e.getBody()))e.setContent(""),e.selection.setCursorLocation();else{const t=e.dom.createRng();t.setStart(o.dom,0),t.setEnd(o.dom,0),e.selection.setRng(t),e.nodeChanged()}}))})),mceTableCellToggleClass:(t,o)=>{l((t=>{const n=ls(e),r=j(n,(t=>e.formatter.match("tablecellclass",{value:o},t.dom))),s=r?e.formatter.remove:e.formatter.apply;k(n,(e=>s("tablecellclass",{value:o},e.dom))),Aa(e,t.dom,Wa)}))},mceTableToggleClass:(t,o)=>{l((t=>{e.formatter.toggle("tableclass",{value:o},t.dom),Aa(e,t.dom,Wa)}))},mceTableToggleCaption:()=>{Ac(e).each((t=>{Pt(t,o).each((o=>{ct(o,"caption").fold((()=>{const t=he.fromTag("caption");Ae(t,he.fromText("Caption")),((e,t,o)=>{Ee(e,0).fold((()=>{Ae(e,t)}),(e=>{Ne(e,t)}))})(o,t),e.selection.setCursorLocation(t.dom,0)}),(n=>{le("caption")(t)&&be("td",o).each((t=>e.selection.setCursorLocation(t.dom,0))),_e(n)})),Aa(e,o.dom,La)}))}))},mceTableSizingMode:(t,n)=>(t=>Ac(e).each((n=>{Ua(e)||$a(e)||Va(e)||Pt(n,o).each((o=>{"relative"!==t||ic(o)?"fixed"!==t||mc(o)?"responsive"!==t||dc(o)||hc(o):gc(o):fc(o),Or(o),Aa(e,o.dom,La)}))})))(n),mceTableCellType:g((e=>"th"===e?t.makeCellsHeader:t.unmakeCellsHeader)),mceTableColType:g((e=>"th"===e?t.makeColumnsHeader:t.unmakeColumnsHeader)),mceTableRowType:g((e=>{switch(e){case"header":return t.makeRowsHeader;case"footer":return t.makeRowsFooter;default:return t.makeRowsBody}}))},((t,o)=>e.addCommand(o,t))),e.addCommand("mceInsertTable",((t,o)=>{((e,t,o,n={})=>{const r=e=>m(e)&&e>0;if(r(t)&&r(o)){const r=n.headerRows||0,s=n.headerColumns||0;return vc(e,o,t,s,r)}console.error("Invalid values for mceInsertTable - rows and columns values are required to insert a table.")})(e,o.rows,o.columns,o.options)})),e.addCommand("mceTableApplyCellStyle",((t,o)=>{const l=e=>"tablecell"+e.toLowerCase().replace("-","");if(!r(o))return;const a=ls(e);if(0===a.length)return;const c=K(o,((t,o)=>e.formatter.has(l(o))&&n(t)));(e=>{for(const t in e)if(V.call(e,t))return!1;return!0})(c)||($(c,((t,o)=>{const n=l(o);k(a,(o=>{""===t?e.formatter.remove(n,{value:null},o.dom,!0):e.formatter.apply(n,{value:t},o.dom)}))})),s(a[0]).each((t=>Aa(e,t.dom,Wa))))}))},Lc=Sl([{before:["element"]},{on:["element","offset"]},{after:["element"]}]),Mc={before:Lc.before,on:Lc.on,after:Lc.after,cata:(e,t,o,n)=>e.fold(t,o,n),getStart:e=>e.fold(f,f,f)},jc=(e,t)=>({selection:e,kill:t}),_c=(e,t)=>{const o=e.document.createRange();return o.selectNode(t.dom),o},Ic=(e,t)=>{const o=e.document.createRange();return Pc(o,t),o},Pc=(e,t)=>e.selectNodeContents(t.dom),Fc=(e,t,o)=>{const n=e.document.createRange();var r;return r=n,t.fold((e=>{r.setStartBefore(e.dom)}),((e,t)=>{r.setStart(e.dom,t)}),(e=>{r.setStartAfter(e.dom)})),((e,t)=>{t.fold((t=>{e.setEndBefore(t.dom)}),((t,o)=>{e.setEnd(t.dom,o)}),(t=>{e.setEndAfter(t.dom)}))})(n,o),n},Hc=(e,t,o,n,r)=>{const s=e.document.createRange();return s.setStart(t.dom,o),s.setEnd(n.dom,r),s},qc=e=>({left:e.left,top:e.top,right:e.right,bottom:e.bottom,width:e.width,height:e.height}),Vc=Sl([{ltr:["start","soffset","finish","foffset"]},{rtl:["start","soffset","finish","foffset"]}]),$c=(e,t,o)=>t(he.fromDom(o.startContainer),o.startOffset,he.fromDom(o.endContainer),o.endOffset),Uc=(e,t)=>{const o=((e,t)=>t.match({domRange:e=>({ltr:u(e),rtl:y.none}),relative:(t,o)=>({ltr:Ut((()=>Fc(e,t,o))),rtl:Ut((()=>y.some(Fc(e,o,t))))}),exact:(t,o,n,r)=>({ltr:Ut((()=>Hc(e,t,o,n,r))),rtl:Ut((()=>y.some(Hc(e,n,r,t,o))))})}))(e,t);return((e,t)=>{const o=t.ltr();return o.collapsed?t.rtl().filter((e=>!1===e.collapsed)).map((e=>Vc.rtl(he.fromDom(e.endContainer),e.endOffset,he.fromDom(e.startContainer),e.startOffset))).getOrThunk((()=>$c(0,Vc.ltr,o))):$c(0,Vc.ltr,o)})(0,o)},Gc=(e,t)=>Uc(e,t).match({ltr:(t,o,n,r)=>{const s=e.document.createRange();return s.setStart(t.dom,o),s.setEnd(n.dom,r),s},rtl:(t,o,n,r)=>{const s=e.document.createRange();return s.setStart(n.dom,r),s.setEnd(t.dom,o),s}});Vc.ltr,Vc.rtl;const Kc=(e,t,o,n)=>({start:e,soffset:t,finish:o,foffset:n}),Yc=(e,t,o,n)=>({start:Mc.on(e,t),finish:Mc.on(o,n)}),Jc=(e,t)=>{const o=Gc(e,t);return Kc(he.fromDom(o.startContainer),o.startOffset,he.fromDom(o.endContainer),o.endOffset)},Qc=Yc,Xc=(e,t,o,n,r)=>ve(o,n)?y.none():Ur(o,n,t).bind((t=>{const n=t.boxes.getOr([]);return n.length>1?(r(e,n,t.start,t.finish),y.some(jc(y.some(Qc(o,0,o,mr(o))),!0))):y.none()})),Zc=(e,t)=>({item:e,mode:t}),ei=(e,t,o,n=ti)=>e.property().parent(t).map((e=>Zc(e,n))),ti=(e,t,o,n=oi)=>o.sibling(e,t).map((e=>Zc(e,n))),oi=(e,t,o,n=oi)=>{const r=e.property().children(t);return o.first(r).map((e=>Zc(e,n)))},ni=[{current:ei,next:ti,fallback:y.none()},{current:ti,next:oi,fallback:y.some(ei)},{current:oi,next:oi,fallback:y.some(ti)}],ri=(e,t,o,n,r=ni)=>A(r,(e=>e.current===o)).bind((o=>o.current(e,t,n,o.next).orThunk((()=>o.fallback.bind((o=>ri(e,t,o,n))))))),si=(e,t,o,n,r,s)=>ri(e,t,n,r).bind((t=>s(t.item)?y.none():o(t.item)?y.some(t.item):si(e,t.item,o,t.mode,r,s))),li=e=>t=>0===e.property().children(t).length,ai=(e,t,o,n)=>si(e,t,o,ti,{sibling:(e,t)=>e.query().prevSibling(t),first:e=>e.length>0?y.some(e[e.length-1]):y.none()},n),ci=(e,t,o,n)=>si(e,t,o,ti,{sibling:(e,t)=>e.query().nextSibling(t),first:e=>e.length>0?y.some(e[0]):y.none()},n),ii=Pr(),mi=(e,t)=>((e,t,o)=>ai(e,t,li(e),o))(ii,e,t),di=(e,t)=>((e,t,o)=>ci(e,t,li(e),o))(ii,e,t),ui=Sl([{none:["message"]},{success:[]},{failedUp:["cell"]},{failedDown:["cell"]}]),fi=e=>mt(e,"tr"),gi={...ui,verify:(e,t,o,n,r,s,l)=>mt(n,"td,th",l).bind((o=>mt(t,"td,th",l).map((t=>ve(o,t)?ve(n,o)&&mr(o)===r?s(t):ui.none("in same cell"):Vr(fi,[o,t]).fold((()=>((e,t,o)=>{const n=e.getRect(t),r=e.getRect(o);return r.right>n.left&&r.left<n.right})(e,t,o)?ui.success():s(t)),(e=>s(t))))))).getOr(ui.none("default")),cata:(e,t,o,n,r)=>e.fold(t,o,n,r)},hi=le("br"),pi=(e,t,o)=>t(e,o).bind((e=>ne(e)&&0===ar(e).trim().length?pi(e,t,o):y.some(e))),wi=(e,t,o,n)=>((e,t)=>Ee(e,t).filter(hi).orThunk((()=>Ee(e,t-1).filter(hi))))(t,o).bind((t=>n.traverse(t).fold((()=>pi(t,n.gather,e).map(n.relative)),(e=>(e=>Te(e).bind((t=>{const o=ke(t);return((e,t)=>W(e,h(ve,t)))(o,e).map((n=>((e,t,o,n)=>({parent:e,children:t,element:o,index:n}))(t,o,e,n)))})))(e).map((e=>Mc.on(e.parent,e.index))))))),bi=(e,t)=>({left:e.left,top:e.top+t,right:e.right,bottom:e.bottom+t}),vi=(e,t)=>({left:e.left,top:e.top-t,right:e.right,bottom:e.bottom-t}),yi=(e,t,o)=>({left:e.left+t,top:e.top+o,right:e.right+t,bottom:e.bottom+o}),xi=e=>({left:e.left,top:e.top,right:e.right,bottom:e.bottom}),Ci=(e,t)=>y.some(e.getRect(t)),Si=(e,t,o)=>oe(t)?Ci(e,t).map(xi):ne(t)?((e,t,o)=>o>=0&&o<mr(t)?e.getRangedRect(t,o,t,o+1):o>0?e.getRangedRect(t,o-1,t,o):y.none())(e,t,o).map(xi):y.none(),Ti=(e,t)=>oe(t)?Ci(e,t).map(xi):ne(t)?e.getRangedRect(t,0,t,mr(t)).map(xi):y.none(),Ri=Sl([{none:[]},{retry:["caret"]}]),Di=(e,t,o)=>{return(n=t,r=Pl,st(((e,t)=>t(e)),lt,n,r,undefined)).fold(b,(t=>Ti(e,t).exists((e=>((e,t)=>e.left<t.left||Math.abs(t.right-e.left)<1||e.left>t.right)(o,e)))));var n,r},Oi={point:e=>e.bottom,adjuster:(e,t,o,n,r)=>{const s=bi(r,5);return Math.abs(o.bottom-n.bottom)<1||o.top>r.bottom?Ri.retry(s):o.top===r.bottom?Ri.retry(bi(r,1)):Di(e,t,r)?Ri.retry(yi(s,5,0)):Ri.none()},move:bi,gather:di},ki=(e,t,o,n,r)=>0===r?y.some(n):((e,t,o)=>e.elementFromPoint(t,o).filter((e=>"table"===X(e))).isSome())(e,n.left,t.point(n))?((e,t,o,n,r)=>ki(e,t,o,t.move(n,5),r))(e,t,o,n,r-1):e.situsFromPoint(n.left,t.point(n)).bind((s=>s.start.fold(y.none,(s=>Ti(e,s).bind((l=>t.adjuster(e,s,l,o,n).fold(y.none,(n=>ki(e,t,o,n,r-1))))).orThunk((()=>y.some(n)))),y.none))),Ei=(e,t,o)=>{const n=e.move(o,5),r=ki(t,e,o,n,100).getOr(n);return((e,t,o)=>e.point(t)>o.getInnerHeight()?y.some(e.point(t)-o.getInnerHeight()):e.point(t)<0?y.some(-e.point(t)):y.none())(e,r,t).fold((()=>t.situsFromPoint(r.left,e.point(r))),(o=>(t.scrollBy(0,o),t.situsFromPoint(r.left,e.point(r)-o))))},Ni={tryUp:h(Ei,{point:e=>e.top,adjuster:(e,t,o,n,r)=>{const s=vi(r,5);return Math.abs(o.top-n.top)<1||o.bottom<r.top?Ri.retry(s):o.bottom===r.top?Ri.retry(vi(r,1)):Di(e,t,r)?Ri.retry(yi(s,5,0)):Ri.none()},move:vi,gather:mi}),tryDown:h(Ei,Oi),getJumpSize:u(5)},Bi=(e,t,o)=>e.getSelection().bind((n=>((e,t,o,n)=>{const r=hi(t)?((e,t,o)=>o.traverse(t).orThunk((()=>pi(t,o.gather,e))).map(o.relative))(e,t,n):wi(e,t,o,n);return r.map((e=>({start:e,finish:e})))})(t,n.finish,n.foffset,o).fold((()=>y.some(cs(n.finish,n.foffset))),(r=>{const s=e.fromSitus(r);return l=gi.verify(e,n.finish,n.foffset,s.finish,s.foffset,o.failure,t),gi.cata(l,(e=>y.none()),(()=>y.none()),(e=>y.some(cs(e,0))),(e=>y.some(cs(e,mr(e)))));var l})))),zi=(e,t,o,n,r,s)=>0===s?y.none():Li(e,t,o,n,r).bind((l=>{const a=e.fromSitus(l),c=gi.verify(e,o,n,a.finish,a.foffset,r.failure,t);return gi.cata(c,(()=>y.none()),(()=>y.some(l)),(l=>ve(o,l)&&0===n?Ai(e,o,n,vi,r):zi(e,t,l,0,r,s-1)),(l=>ve(o,l)&&n===mr(l)?Ai(e,o,n,bi,r):zi(e,t,l,mr(l),r,s-1)))})),Ai=(e,t,o,n,r)=>Si(e,t,o).bind((t=>Wi(e,r,n(t,Ni.getJumpSize())))),Wi=(e,t,o)=>{const n=So().browser;return n.isChromium()||n.isSafari()||n.isFirefox()?t.retry(e,o):y.none()},Li=(e,t,o,n,r)=>Si(e,o,n).bind((t=>Wi(e,r,t))),Mi=(e,t,o,n,r)=>mt(n,"td,th",t).bind((n=>mt(n,"table",t).bind((s=>((e,t)=>lt(e,(e=>Te(e).exists((e=>ve(e,t)))),void 0).isSome())(r,s)?((e,t,o)=>Bi(e,t,o).bind((n=>zi(e,t,n.element,n.offset,o,20).map(e.fromSitus))))(e,t,o).bind((e=>mt(e.finish,"td,th",t).map((t=>({start:n,finish:t,range:e}))))):y.none())))),ji=(e,t,o,n,r,s)=>s(n,t).orThunk((()=>Mi(e,t,o,n,r).map((e=>{const t=e.range;return jc(y.some(Qc(t.start,t.soffset,t.finish,t.foffset)),!0)})))),_i=(e,t)=>mt(e,"tr",t).bind((e=>mt(e,"table",t).bind((o=>{const n=rt(o,"tr");return ve(e,n[0])?((e,t,o)=>ai(ii,e,(e=>gr(e).isSome()),o))(o,0,t).map((e=>{const t=mr(e);return jc(y.some(Qc(e,t,e,t)),!0)})):y.none()})))),Ii=(e,t)=>mt(e,"tr",t).bind((e=>mt(e,"table",t).bind((o=>{const n=rt(o,"tr");return ve(e,n[n.length-1])?((e,t,o)=>ci(ii,e,(e=>fr(e).isSome()),o))(o,0,t).map((e=>jc(y.some(Qc(e,0,e,0)),!0))):y.none()})))),Pi=(e,t,o,n,r,s,l)=>Mi(e,o,n,r,s).bind((e=>Xc(t,o,e.start,e.finish,l))),Fi=e=>{let t=e;return{get:()=>t,set:e=>{t=e}}},Hi=()=>{const e=(e=>{const t=Fi(y.none()),o=()=>t.get().each(e);return{clear:()=>{o(),t.set(y.none())},isSet:()=>t.get().isSome(),get:()=>t.get(),set:e=>{o(),t.set(y.some(e))}}})(d);return{...e,on:t=>e.get().each(t)}},qi=(e,t)=>mt(e,"td,th",t),Vi={traverse:Oe,gather:di,relative:Mc.before,retry:Ni.tryDown,failure:gi.failedDown},$i={traverse:De,gather:mi,relative:Mc.before,retry:Ni.tryUp,failure:gi.failedUp},Ui=e=>t=>t===e,Gi=Ui(38),Ki=Ui(40),Yi=e=>e>=37&&e<=40,Ji={isBackward:Ui(37),isForward:Ui(39)},Qi={isBackward:Ui(39),isForward:Ui(37)},Xi=Sl([{domRange:["rng"]},{relative:["startSitu","finishSitu"]},{exact:["start","soffset","finish","foffset"]}]),Zi={domRange:Xi.domRange,relative:Xi.relative,exact:Xi.exact,exactFromRange:e=>Xi.exact(e.start,e.soffset,e.finish,e.foffset),getWin:e=>{const t=(e=>e.match({domRange:e=>he.fromDom(e.startContainer),relative:(e,t)=>Mc.getStart(e),exact:(e,t,o,n)=>e}))(e);return he.fromDom(Se(t).dom.defaultView)},range:Kc},em=document.caretPositionFromPoint?(e,t,o)=>{var n,r;return y.from(null===(r=(n=e.dom).caretPositionFromPoint)||void 0===r?void 0:r.call(n,t,o)).bind((t=>{if(null===t.offsetNode)return y.none();const o=e.dom.createRange();return o.setStart(t.offsetNode,t.offset),o.collapse(),y.some(o)}))}:document.caretRangeFromPoint?(e,t,o)=>{var n,r;return y.from(null===(r=(n=e.dom).caretRangeFromPoint)||void 0===r?void 0:r.call(n,t,o))}:y.none,tm=(e,t)=>{const o=X(e);return"input"===o?Mc.after(e):T(["br","img"],o)?0===t?Mc.before(e):Mc.after(e):Mc.on(e,t)},om=e=>y.from(e.getSelection()),nm=(e,t)=>{om(e).each((e=>{e.removeAllRanges(),e.addRange(t)}))},rm=(e,t,o,n,r)=>{const s=Hc(e,t,o,n,r);nm(e,s)},sm=(e,t)=>Uc(e,t).match({ltr:(t,o,n,r)=>{rm(e,t,o,n,r)},rtl:(t,o,n,r)=>{om(e).each((s=>{if(s.setBaseAndExtent)s.setBaseAndExtent(t.dom,o,n.dom,r);else if(s.extend)try{((e,t,o,n,r,s)=>{t.collapse(o.dom,n),t.extend(r.dom,s)})(0,s,t,o,n,r)}catch(s){rm(e,n,r,t,o)}else rm(e,n,r,t,o)}))}}),lm=(e,t,o,n,r)=>{const s=((e,t,o,n)=>{const r=tm(e,t),s=tm(o,n);return Zi.relative(r,s)})(t,o,n,r);sm(e,s)},am=(e,t,o)=>{const n=((e,t)=>{const o=e.fold(Mc.before,tm,Mc.after),n=t.fold(Mc.before,tm,Mc.after);return Zi.relative(o,n)})(t,o);sm(e,n)},cm=e=>{if(e.rangeCount>0){const t=e.getRangeAt(0),o=e.getRangeAt(e.rangeCount-1);return y.some(Kc(he.fromDom(t.startContainer),t.startOffset,he.fromDom(o.endContainer),o.endOffset))}return y.none()},im=e=>{if(null===e.anchorNode||null===e.focusNode)return cm(e);{const t=he.fromDom(e.anchorNode),o=he.fromDom(e.focusNode);return((e,t,o,n)=>{const r=((e,t,o,n)=>{const r=Ce(e).dom.createRange();return r.setStart(e.dom,t),r.setEnd(o.dom,n),r})(e,t,o,n),s=ve(e,o)&&t===n;return r.collapsed&&!s})(t,e.anchorOffset,o,e.focusOffset)?y.some(Kc(t,e.anchorOffset,o,e.focusOffset)):cm(e)}},mm=(e,t,o=!0)=>{const n=(o?Ic:_c)(e,t);nm(e,n)},dm=e=>(e=>om(e).filter((e=>e.rangeCount>0)).bind(im))(e).map((e=>Zi.exact(e.start,e.soffset,e.finish,e.foffset))),um=e=>({elementFromPoint:(t,o)=>he.fromPoint(he.fromDom(e.document),t,o),getRect:e=>e.dom.getBoundingClientRect(),getRangedRect:(t,o,n,r)=>{const s=Zi.exact(t,o,n,r);return((e,t)=>(e=>{const t=e.getClientRects(),o=t.length>0?t[0]:e.getBoundingClientRect();return o.width>0||o.height>0?y.some(o).map(qc):y.none()})(Gc(e,t)))(e,s)},getSelection:()=>dm(e).map((t=>Jc(e,t))),fromSitus:t=>{const o=Zi.relative(t.start,t.finish);return Jc(e,o)},situsFromPoint:(t,o)=>((e,t,o)=>((e,t,o)=>{const n=he.fromDom(e.document);return em(n,t,o).map((e=>Kc(he.fromDom(e.startContainer),e.startOffset,he.fromDom(e.endContainer),e.endOffset)))})(e,t,o))(e,t,o).map((e=>Yc(e.start,e.soffset,e.finish,e.foffset))),clearSelection:()=>{(e=>{om(e).each((e=>e.removeAllRanges()))})(e)},collapseSelection:(t=!1)=>{dm(e).each((o=>o.fold((e=>e.collapse(t)),((o,n)=>{const r=t?o:n;am(e,r,r)}),((o,n,r,s)=>{const l=t?o:r,a=t?n:s;lm(e,l,a,l,a)}))))},setSelection:t=>{lm(e,t.start,t.soffset,t.finish,t.foffset)},setRelativeSelection:(t,o)=>{am(e,t,o)},selectNode:t=>{mm(e,t,!1)},selectContents:t=>{mm(e,t)},getInnerHeight:()=>e.innerHeight,getScrollY:()=>(e=>{const t=void 0!==e?e.dom:document,o=t.body.scrollLeft||t.documentElement.scrollLeft,n=t.body.scrollTop||t.documentElement.scrollTop;return mn(o,n)})(he.fromDom(e.document)).top,scrollBy:(t,o)=>{((e,t,o)=>{const n=(void 0!==o?o.dom:document).defaultView;n&&n.scrollBy(e,t)})(t,o,he.fromDom(e.document))}}),fm=(e,t)=>({rows:e,cols:t}),gm=e=>void 0!==e.dom.classList,hm=(e,t)=>((e,t,o)=>{const n=((e,t)=>{const o=me(e,t);return void 0===o||""===o?[]:o.split(" ")})(e,t).concat([o]);return ce(e,t,n.join(" ")),!0})(e,"class",t),pm=(e,t)=>{gm(e)?e.dom.classList.add(t):hm(e,t)},wm=(e,t)=>gm(e)&&e.dom.classList.contains(t),bm=()=>({tag:"none"}),vm=e=>({tag:"multiple",elements:e}),ym=e=>({tag:"single",element:e}),xm=e=>{const t=he.fromDom((e=>{if(Je()&&c(e.target)){const t=he.fromDom(e.target);if(oe(t)&&c(t.dom.shadowRoot)&&e.composed&&e.composedPath){const t=e.composedPath();if(t)return P(t)}}return y.from(e.target)})(e).getOr(e.target)),o=()=>e.stopPropagation(),n=()=>e.preventDefault(),r=(s=n,l=o,(...e)=>s(l.apply(null,e)));var s,l;return((e,t,o,n,r,s,l)=>({target:e,x:t,y:o,stop:n,prevent:r,kill:s,raw:l}))(t,e.clientX,e.clientY,o,n,r,e)},Cm=(e,t,o,n)=>{e.dom.removeEventListener(t,o,n)},Sm=v,Tm=(e,t,o)=>((e,t,o,n)=>((e,t,o,n,r)=>{const s=((e,t)=>o=>{e(o)&&t(xm(o))})(o,n);return e.dom.addEventListener(t,s,r),{unbind:h(Cm,e,t,s,r)}})(e,t,o,n,!1))(e,t,Sm,o),Rm=xm,Dm=e=>!1===wm(he.fromDom(e.target),"ephox-snooker-resizer-bar"),Om=(e,t)=>{const o=(r=ts.selectedSelector,{get:()=>Jr(he.fromDom(e.getBody()),r).fold((()=>ss(kr(e),Dr(e)).fold(bm,ym)),vm)}),n=((e,t,o)=>{const n=t=>{ue(t,e.selected),ue(t,e.firstSelected),ue(t,e.lastSelected)},r=t=>{ce(t,e.selected,"1")},s=e=>{l(e),o()},l=t=>{const o=rt(t,`${e.selectedSelector},${e.firstSelectedSelector},${e.lastSelectedSelector}`);k(o,n)};return{clearBeforeUpdate:l,clear:s,selectRange:(o,n,l,a)=>{s(o),k(n,r),ce(l,e.firstSelected,"1"),ce(a,e.lastSelected,"1"),t(n,l,a)},selectedSelector:e.selectedSelector,firstSelectedSelector:e.firstSelectedSelector,lastSelectedSelector:e.lastSelectedSelector}})(ts,((t,o,n)=>{Pt(o).each((r=>{const s=_a(e),l=Sr(d,he.fromDom(e.getDoc()),s),a=((e,t,o)=>{const n=$o(e);return Qs(n,t).map((e=>{const t=$s(n,o,!1),{rows:r}=jo(t),s=((e,t)=>{const o=e.slice(0,t[t.length-1].row+1),n=Us(o);return M(n,(e=>{const o=e.cells.slice(0,t[t.length-1].column+1);return O(o,(e=>e.element))}))})(r,e),l=((e,t)=>{const o=e.slice(t[0].row+t[0].rowspan-1,e.length),n=Us(o);return M(n,(e=>{const o=e.cells.slice(t[0].column+t[0].colspan-1,e.cells.length);return O(o,(e=>e.element))}))})(r,e);return{upOrLeftCells:s,downOrRightCells:l}}))})(r,{selection:ls(e)},l);((e,t,o,n,r)=>{e.dispatch("TableSelectionChange",{cells:t,start:o,finish:n,otherCells:r})})(e,t,o,n,a)}))}),(()=>(e=>{e.dispatch("TableSelectionClear")})(e)));var r;return e.on("init",(o=>{const r=e.getWin(),s=Rr(e),l=Dr(e),a=((e,t,o,n)=>{const r=((e,t,o,n)=>{const r=Hi(),s=r.clear,l=s=>{r.on((r=>{n.clearBeforeUpdate(t),qi(s.target,o).each((l=>{Ur(r,l,o).each((o=>{const r=o.boxes.getOr([]);if(1===r.length){const o=r[0],l="false"===Is(o),a=dt(js(s.target),o,ve);l&&a&&(n.selectRange(t,r,o,o),e.selectContents(o))}else r.length>1&&(n.selectRange(t,r,o.start,o.finish),e.selectContents(l))}))}))}))};return{clearstate:s,mousedown:e=>{n.clear(t),qi(e.target,o).each(r.set)},mouseover:e=>{l(e)},mouseup:e=>{l(e),s()}}})(um(e),t,o,n);return{clearstate:r.clearstate,mousedown:r.mousedown,mouseover:r.mouseover,mouseup:r.mouseup}})(r,s,l,n),c=((e,t,o,n)=>{const r=um(e),s=()=>(n.clear(t),y.none());return{keydown:(e,l,a,c,i,m)=>{const d=e.raw,u=d.which,f=!0===d.shiftKey,g=Gr(t,n.selectedSelector).fold((()=>(Yi(u)&&!f&&n.clearBeforeUpdate(t),Ki(u)&&f?h(Pi,r,t,o,Vi,c,l,n.selectRange):Gi(u)&&f?h(Pi,r,t,o,$i,c,l,n.selectRange):Ki(u)?h(ji,r,o,Vi,c,l,Ii):Gi(u)?h(ji,r,o,$i,c,l,_i):y.none)),(e=>{const o=o=>()=>{const s=H(o,(o=>((e,t,o,n,r)=>Yr(n,e,t,r.firstSelectedSelector,r.lastSelectedSelector).map((e=>(r.clearBeforeUpdate(o),r.selectRange(o,e.boxes,e.start,e.finish),e.boxes))))(o.rows,o.cols,t,e,n)));return s.fold((()=>Kr(t,n.firstSelectedSelector,n.lastSelectedSelector).map((e=>{const o=Ki(u)||m.isForward(u)?Mc.after:Mc.before;return r.setRelativeSelection(Mc.on(e.first,0),o(e.table)),n.clear(t),jc(y.none(),!0)}))),(e=>y.some(jc(y.none(),!0))))};return Ki(u)&&f?o([fm(1,0)]):Gi(u)&&f?o([fm(-1,0)]):m.isBackward(u)&&f?o([fm(0,-1),fm(-1,0)]):m.isForward(u)&&f?o([fm(0,1),fm(1,0)]):Yi(u)&&!f?s:y.none}));return g()},keyup:(e,r,s,l,a)=>Gr(t,n.selectedSelector).fold((()=>{const c=e.raw,i=c.which;return!0===c.shiftKey&&Yi(i)?((e,t,o,n,r,s,l)=>ve(o,r)&&n===s?y.none():mt(o,"td,th",t).bind((o=>mt(r,"td,th",t).bind((n=>Xc(e,t,o,n,l))))))(t,o,r,s,l,a,n.selectRange):y.none()}),y.none)}})(r,s,l,n),i=((e,t,o,n)=>{const r=um(e);return(e,s)=>{n.clearBeforeUpdate(t),Ur(e,s,o).each((e=>{const o=e.boxes.getOr([]);n.selectRange(t,o,e.start,e.finish),r.selectContents(s),r.collapseSelection()}))}})(r,s,l,n);e.on("TableSelectorChange",(e=>i(e.start,e.finish)));const m=(t,o)=>{(e=>!0===e.raw.shiftKey)(t)&&(o.kill&&t.kill(),o.selection.each((t=>{const o=Zi.relative(t.start,t.finish),n=Gc(r,o);e.selection.setRng(n)})))},u=e=>0===e.button,f=(()=>{const e=Fi(he.fromDom(s)),t=Fi(0);return{touchEnd:o=>{const n=he.fromDom(o.target);if(le("td")(n)||le("th")(n)){const r=e.get(),s=t.get();ve(r,n)&&o.timeStamp-s<300&&(o.preventDefault(),i(n,n))}e.set(n),t.set(o.timeStamp)}}})();e.on("dragstart",(e=>{a.clearstate()})),e.on("mousedown",(e=>{u(e)&&Dm(e)&&a.mousedown(Rm(e))})),e.on("mouseover",(e=>{var t;void 0!==(t=e).buttons&&0==(1&t.buttons)||!Dm(e)||a.mouseover(Rm(e))})),e.on("mouseup",(e=>{u(e)&&Dm(e)&&a.mouseup(Rm(e))})),e.on("touchend",f.touchEnd),e.on("keyup",(t=>{const o=Rm(t);if(o.raw.shiftKey&&Yi(o.raw.which)){const t=e.selection.getRng(),n=he.fromDom(t.startContainer),r=he.fromDom(t.endContainer);c.keyup(o,n,t.startOffset,r,t.endOffset).each((e=>{m(o,e)}))}})),e.on("keydown",(o=>{const n=Rm(o);t.hide();const r=e.selection.getRng(),s=he.fromDom(r.startContainer),l=he.fromDom(r.endContainer),a=nn(Ji,Qi)(he.fromDom(e.selection.getStart()));c.keydown(n,s,r.startOffset,l,r.endOffset,a).each((e=>{m(n,e)})),t.show()})),e.on("NodeChange",(()=>{const t=e.selection,o=he.fromDom(t.getStart()),r=he.fromDom(t.getEnd());Vr(Pt,[o,r]).fold((()=>n.clear(s)),d)}))})),e.on("PreInit",(()=>{e.serializer.addTempAttr(ts.firstSelected),e.serializer.addTempAttr(ts.lastSelected)})),{getSelectedCells:()=>((e,t,o,n)=>{switch(e.tag){case"none":return t();case"single":return(e=>[e.dom])(e.element);case"multiple":return(e=>O(e,(e=>e.dom)))(e.elements)}})(o.get(),u([])),clearSelectedCells:e=>n.clear(he.fromDom(e))}},km=e=>{let t=[];return{bind:e=>{if(void 0===e)throw new Error("Event bind error: undefined handler");t.push(e)},unbind:e=>{t=N(t,(t=>t!==e))},trigger:(...o)=>{const n={};k(e,((e,t)=>{n[e]=o[t]})),k(t,(e=>{e(n)}))}}},Em=e=>({registry:U(e,(e=>({bind:e.bind,unbind:e.unbind}))),trigger:U(e,(e=>e.trigger))}),Nm=e=>e.slice(0).sort(),Bm=(e,t)=>{const o=N(t,(t=>!T(e,t)));o.length>0&&(e=>{throw new Error("Unsupported keys for object: "+Nm(e).join(", "))})(o)},zm=e=>((e,t)=>((e,t,o)=>{if(0===t.length)throw new Error("You must specify at least one required field.");return((e,t)=>{if(!s(t))throw new Error("The required fields must be an array. Was: "+t+".");k(t,(t=>{if(!n(t))throw new Error("The value "+t+" in the "+e+" fields was not a string.")}))})("required",t),(e=>{const t=Nm(e);A(t,((e,o)=>o<t.length-1&&e===t[o+1])).each((e=>{throw new Error("The field: "+e+" occurs more than once in the combined fields: ["+t.join(", ")+"].")}))})(t),n=>{const r=q(n);j(t,(e=>T(r,e)))||((e,t)=>{throw new Error("All required keys ("+Nm(e).join(", ")+") were not specified. Specified keys were: "+Nm(t).join(", ")+".")})(t,r),e(t,r);const s=N(t,(e=>!o.validate(n[e],e)));return s.length>0&&((e,t)=>{throw new Error("All values need to be of type: "+t+". Keys ("+Nm(e).join(", ")+") were not.")})(s,o.label),n}})(e,t,{validate:i,label:"function"}))(Bm,e),Am=zm(["compare","extract","mutate","sink"]),Wm=zm(["element","start","stop","destroy"]),Lm=zm(["forceDrop","drop","move","delayDrop"]),Mm=()=>{const e=(()=>{const e=Em({move:km(["info"])});return{onEvent:d,reset:d,events:e.registry}})(),t=(()=>{let e=y.none();const t=Em({move:km(["info"])});return{onEvent:(o,n)=>{n.extract(o).each((o=>{const r=((t,o)=>{const n=e.map((e=>t.compare(e,o)));return e=y.some(o),n})(n,o);r.each((e=>{t.trigger.move(e)}))}))},reset:()=>{e=y.none()},events:t.registry}})();let o=e;return{on:()=>{o.reset(),o=t},off:()=>{o.reset(),o=e},isOn:()=>o===t,onEvent:(e,t)=>{o.onEvent(e,t)},events:t.events}},jm=e=>{const t=e.replace(/\./g,"-");return{resolve:e=>t+"-"+e}},_m=jm("ephox-dragster").resolve;var Im=Am({compare:(e,t)=>mn(t.left-e.left,t.top-e.top),extract:e=>y.some(mn(e.x,e.y)),sink:(e,t)=>{const o=(e=>{const t={layerClass:_m("blocker"),...e},o=he.fromTag("div");return ce(o,"role","presentation"),St(o,{position:"fixed",left:"0px",top:"0px",width:"100%",height:"100%"}),pm(o,_m("blocker")),pm(o,t.layerClass),{element:u(o),destroy:()=>{_e(o)}}})(t),n=Tm(o.element(),"mousedown",e.forceDrop),r=Tm(o.element(),"mouseup",e.drop),s=Tm(o.element(),"mousemove",e.move),l=Tm(o.element(),"mouseout",e.delayDrop);return Wm({element:o.element,start:e=>{Ae(e,o.element())},stop:()=>{_e(o.element())},destroy:()=>{o.destroy(),r.unbind(),s.unbind(),l.unbind(),n.unbind()}})},mutate:(e,t)=>{e.mutate(t.left,t.top)}});const Pm=jm("ephox-snooker").resolve,Fm=Pm("resizer-bar"),Hm=Pm("resizer-rows"),qm=Pm("resizer-cols"),Vm=e=>{const t=rt(e.parent(),"."+Fm);k(t,_e)},$m=(e,t,o)=>{const n=e.origin();k(t,(t=>{t.each((t=>{const r=o(n,t);pm(r,Fm),Ae(e.parent(),r)}))}))},Um=(e,t,o,n,r)=>{const s=un(o),l=t.isResizable,a=n.length>0?Tn.positions(n,o):[],c=a.length>0?((e,t)=>M(e.all,((e,o)=>t(e.element)?[o]:[])))(e,l):[];((e,t,o,n)=>{$m(e,t,((e,t)=>{const r=((e,t,o,n,r)=>{const s=he.fromTag("div");return St(s,{position:"absolute",left:t+"px",top:o-3.5+"px",height:"7px",width:n+"px"}),ie(s,{"data-row":e,role:"presentation"}),s})(t.row,o.left-e.left,t.y-e.top,n);return pm(r,Hm),r}))})(t,N(a,((e,t)=>R(c,(e=>t===e)))),s,ko(o));const i=r.length>0?Dn.positions(r,o):[],m=i.length>0?((e,t)=>{const o=[];return D(e.grid.columns,(n=>{Zo(e,n).map((e=>e.element)).forall(t)&&o.push(n)})),N(o,(o=>{const n=Yo(e,(e=>e.column===o));return j(n,(e=>t(e.element)))}))})(e,l):[];((e,t,o,n)=>{$m(e,t,((e,t)=>{const r=((e,t,o,n,r)=>{const s=he.fromTag("div");return St(s,{position:"absolute",left:t-3.5+"px",top:o+"px",height:r+"px",width:"7px"}),ie(s,{"data-column":e,role:"presentation"}),s})(t.col,t.x-e.left,o.top-e.top,0,n);return pm(r,qm),r}))})(t,N(i,((e,t)=>R(m,(e=>t===e)))),s,an(o))},Gm=(e,t)=>{if(Vm(e),e.isResizable(t)){const o=$o(t),n=on(o),r=en(o);Um(o,e,t,n,r)}},Km=(e,t)=>{const o=rt(e.parent(),"."+Fm);k(o,t)},Ym=e=>{Km(e,(e=>{Ct(e,"display","none")}))},Jm=e=>{Km(e,(e=>{Ct(e,"display","block")}))},Qm=Pm("resizer-bar-dragging"),Xm=e=>{const t=(()=>{const e=Em({drag:km(["xDelta","yDelta","target"])});let t=y.none();const o=(()=>{const e=Em({drag:km(["xDelta","yDelta"])});return{mutate:(t,o)=>{e.trigger.drag(t,o)},events:e.registry}})();return o.events.drag.bind((o=>{t.each((t=>{e.trigger.drag(o.xDelta,o.yDelta,t)}))})),{assign:e=>{t=y.some(e)},get:()=>t,mutate:o.mutate,events:e.registry}})(),o=((e,t={})=>{var o;return((e,t,o)=>{let n=!1;const r=Em({start:km([]),stop:km([])}),s=Mm(),a=()=>{m.stop(),s.isOn()&&(s.off(),r.trigger.stop())},c=((e,t)=>{let o=null;const n=()=>{l(o)||(clearTimeout(o),o=null)};return{cancel:n,throttle:(...t)=>{n(),o=setTimeout((()=>{o=null,e.apply(null,t)}),200)}}})(a);s.events.move.bind((o=>{t.mutate(e,o.info)}));const i=e=>(...t)=>{n&&e.apply(null,t)},m=t.sink(Lm({forceDrop:a,drop:i(a),move:i((e=>{c.cancel(),s.onEvent(e,t)})),delayDrop:i(c.throttle)}),o);return{element:m.element,go:e=>{m.start(e),s.on(),r.trigger.start()},on:()=>{n=!0},off:()=>{n=!1},destroy:()=>{m.destroy()},events:r.registry}})(e,null!==(o=t.mode)&&void 0!==o?o:Im,t)})(t,{});let n=y.none();const r=(e,t)=>y.from(me(e,t));t.events.drag.bind((e=>{r(e.target,"data-row").each((t=>{const o=zt(e.target,"top");Ct(e.target,"top",o+e.yDelta+"px")})),r(e.target,"data-column").each((t=>{const o=zt(e.target,"left");Ct(e.target,"left",o+e.xDelta+"px")}))}));const s=(e,t)=>zt(e,t)-kt(e,"data-initial-"+t,0);o.events.stop.bind((()=>{t.get().each((t=>{n.each((o=>{r(t,"data-row").each((e=>{const n=s(t,"top");ue(t,"data-initial-top"),d.trigger.adjustHeight(o,n,parseInt(e,10))})),r(t,"data-column").each((e=>{const n=s(t,"left");ue(t,"data-initial-left"),d.trigger.adjustWidth(o,n,parseInt(e,10))})),Gm(e,o)}))}))}));const a=(n,r)=>{d.trigger.startAdjust(),t.assign(n),ce(n,"data-initial-"+r,zt(n,r)),pm(n,Qm),Ct(n,"opacity","0.2"),o.go(e.parent())},c=Tm(e.parent(),"mousedown",(e=>{var t;t=e.target,wm(t,Hm)&&a(e.target,"top"),(e=>wm(e,qm))(e.target)&&a(e.target,"left")})),i=t=>ve(t,e.view()),m=Tm(e.view(),"mouseover",(t=>{var o;(o=t.target,mt(o,"table",i).filter(_s)).fold((()=>{Ze(t.target)&&Vm(e)}),(t=>{n=y.some(t),Gm(e,t)}))})),d=Em({adjustHeight:km(["table","delta","row"]),adjustWidth:km(["table","delta","column"]),startAdjust:km([])});return{destroy:()=>{c.unbind(),m.unbind(),o.destroy(),Vm(e)},refresh:t=>{Gm(e,t)},on:o.on,off:o.off,hideBars:h(Ym,e),showBars:h(Jm,e),events:d.registry}},Zm=(e,t,o)=>{const n=Tn,r=Dn,s=Xm(e),l=Em({beforeResize:km(["table","type"]),afterResize:km(["table","type"]),startDrag:km([])});return s.events.adjustHeight.bind((e=>{const t=e.table;l.trigger.beforeResize(t,"row");((e,t,o,n)=>{const r=$o(e),s=((e,t,o)=>Xn(e,t,o,Fn,(e=>e.getOrThunk(Wt))))(r,e,n),l=O(s,((e,n)=>o===n?Math.max(t+e,Wt()):e)),a=Dl(r,l),c=((e,t)=>O(e.all,((e,o)=>({element:e.element,height:t[o]}))))(r,l);k(c,(e=>{Ln(e.element,e.height)})),k(a,(e=>{Ln(e.element,e.height)}));const i=B(l,((e,t)=>e+t),0);Ln(e,i)})(t,n.delta(e.delta,t),e.row,n),l.trigger.afterResize(t,"row")})),s.events.startAdjust.bind((e=>{l.trigger.startDrag()})),s.events.adjustWidth.bind((e=>{const n=e.table;l.trigger.beforeResize(n,"col");const s=r.delta(e.delta,n),a=o(n);kl(n,s,e.column,t,a),l.trigger.afterResize(n,"col")})),{on:s.on,off:s.off,refreshBars:s.refresh,hideBars:s.hideBars,showBars:s.showBars,destroy:s.destroy,events:l.registry}},ed=e=>c(e)&&"TABLE"===e.tagName,td="bar-",od=e=>"false"!==me(e,"data-mce-resize"),nd=e=>{const t=Hi(),o=Hi(),n=Hi();let r,s;const l=t=>Ja(e,t),a=()=>Fa(e)?xs():ys();return e.on("init",(()=>{const r=((e,t)=>e.inline?((e,t,o)=>({parent:u(t),view:u(e),origin:u(mn(0,0)),isResizable:o}))(he.fromDom(e.getBody()),(()=>{const e=he.fromTag("div");return St(e,{position:"static",height:"0",width:"0",padding:"0",margin:"0",border:"0"}),Ae(et(he.fromDom(document)),e),e})(),t):((e,t)=>{const o=re(e)?(e=>he.fromDom(Se(e).dom.documentElement))(e):e;return{parent:u(o),view:u(e),origin:u(mn(0,0)),isResizable:t}})(he.fromDom(e.getDoc()),t))(e,od);if(n.set(r),(e=>{const t=e.options.get("object_resizing");return T(t.split(","),"table")})(e)&&Ga(e)){const n=a(),s=Zm(r,n,l);s.on(),s.events.startDrag.bind((o=>{t.set(e.selection.getRng())})),s.events.beforeResize.bind((t=>{const o=t.table.dom;((e,t,o,n,r)=>{e.dispatch("ObjectResizeStart",{target:t,width:o,height:n,origin:r})})(e,o,Er(o),Nr(o),td+t.type)})),s.events.afterResize.bind((o=>{const n=o.table,r=n.dom;Or(n),t.on((t=>{e.selection.setRng(t),e.focus()})),((e,t,o,n,r)=>{e.dispatch("ObjectResized",{target:t,width:o,height:n,origin:r})})(e,r,Er(r),Nr(r),td+o.type),e.undoManager.add()})),o.set(s)}})),e.on("ObjectResizeStart",(t=>{const o=t.target;if(ed(o)){const n=he.fromDom(o);k(e.dom.select(".mce-clonedresizable"),(t=>{e.dom.addClass(t,"mce-"+Pa(e)+"-columns")})),!mc(n)&&$a(e)?gc(n):!ic(n)&&Va(e)&&fc(n),dc(n)&&pt(t.origin,td)&&fc(n),r=t.width,s=Ua(e)?"":((e,t)=>{const o=e.dom.getStyle(t,"width")||e.dom.getAttrib(t,"width");return y.from(o).filter(vt)})(e,o).getOr("")}})),e.on("ObjectResized",(t=>{const o=t.target;if(ed(o)){const n=he.fromDom(o),c=t.origin;pt(c,"corner-")&&((t,o,n)=>{const c=wt(o,"e");if(""===s&&fc(t),n!==r&&""!==s){Ct(t,"width",s);const o=a(),i=l(t),m=Fa(e)||c?(e=>Cs(e).columns)(t)-1:0;kl(t,n-r,m,o,i)}else if((e=>/^(\d+(\.\d+)?)%$/.test(e))(s)){const e=parseFloat(s.replace("%",""));Ct(t,"width",n*e/r+"%")}(e=>/^(\d+(\.\d+)?)px$/.test(e))(s)&&(e=>{const t=$o(e);Xo(t)||k(_t(e),(e=>{const t=Tt(e,"width");Ct(e,"width",t),ue(e,"width")}))})(t)})(n,c,t.width),Or(n),Aa(e,n.dom,Wa)}})),e.on("SwitchMode",(()=>{o.on((t=>{e.mode.isReadOnly()?t.hideBars():t.showBars()}))})),e.on("remove",(()=>{o.on((e=>{e.destroy()})),n.on((t=>{((e,t)=>{e.inline&&_e(t.parent())})(e,t)}))})),{refresh:e=>{o.on((t=>t.refreshBars(he.fromDom(e))))},hide:()=>{o.on((e=>e.hideBars()))},show:()=>{o.on((e=>e.showBars()))}}},rd=e=>{(e=>{const t=e.options.register;t("table_clone_elements",{processor:"string[]"}),t("table_use_colgroups",{processor:"boolean",default:!0}),t("table_header_type",{processor:e=>{const t=T(["section","cells","sectionCells","auto"],e);return t?{value:e,valid:t}:{valid:!1,message:"Must be one of: section, cells, sectionCells or auto."}},default:"section"}),t("table_sizing_mode",{processor:"string",default:"auto"}),t("table_default_attributes",{processor:"object",default:{border:"1"}}),t("table_default_styles",{processor:"object",default:{"border-collapse":"collapse",width:"100%"}}),t("table_column_resizing",{processor:e=>{const t=T(["preservetable","resizetable"],e);return t?{value:e,valid:t}:{valid:!1,message:"Must be preservetable, or resizetable."}},default:"preservetable"}),t("table_resize_bars",{processor:"boolean",default:!0})})(e);const t=nd(e),o=Om(e,t),n=Qa(e,t,o);return Wc(e,n),((e,t)=>{const o=Dr(e),n=t=>ss(kr(e)).bind((n=>Pt(n,o).map((o=>{const r=os(ls(e),o,n);return t(o,r)})))).getOr("");$({mceTableRowType:()=>n(t.getTableRowType),mceTableCellType:()=>n(t.getTableCellType),mceTableColType:()=>n(t.getTableColType)},((t,o)=>e.addQueryValueHandler(o,t)))})(e,n),as(e,n),{getSelectedCells:o.getSelectedCells,clearSelectedCells:o.clearSelectedCells}};e.add("dom",(e=>({table:rd(e)})))}();