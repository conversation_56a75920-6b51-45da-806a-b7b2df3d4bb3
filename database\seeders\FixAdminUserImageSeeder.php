<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Storage;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;

class FixAdminUserImageSeeder extends Seeder
{
    /**
     * Run the database seeds to fix the admin user's image path.
     *
     * @return void
     */
    public function run()
    {
        // Get the admin user (ID 1)
        $adminUser = User::find(1);
        
        if ($adminUser) {
            // Check if the logo.svg file exists in public/logo
            $sourcePath = public_path('logo/logo.svg');
            
            if (File::exists($sourcePath)) {
                // Define the destination path in storage
                $destinationPath = 'logo.svg';
                
                // Copy the file to storage/app/public
                File::copy($sourcePath, storage_path('app/public/' . $destinationPath));
                
                // Update the user's image path
                $adminUser->image = $destinationPath;
                $adminUser->save();
                
                echo "Admin user image path has been updated successfully.\n";
            } else {
                echo "Source logo.svg file not found in public/logo directory.\n";
            }
        } else {
            echo "Admin user (ID 1) not found.\n";
        }
    }
}