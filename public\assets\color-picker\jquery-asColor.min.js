/**
* jQuery asColor v0.3.6
* https://github.com/amazingSurge/asColor
*
* Copyright (c) amazingSurge
* Released under the LGPL-3.0 license
*/
!function(e,t){if("function"==typeof define&&define.amd)define("AsColor",["exports","jquery"],t);else if("undefined"!=typeof exports)t(exports,require("jquery"));else{var r={exports:{}};t(r.exports,e.jQuery),e.AsColor=r.exports}}(this,function(e,t){"use strict";function r(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function a(e){return 0===e.indexOf("#")&&(e=e.substr(1)),e?(3===e.length&&(e=e[0]+e[0]+e[1]+e[1]+e[2]+e[2]),6===e.length?"#"+e:null):null}function n(e){return 0===e.indexOf("#")&&(e=e.substr(1)),6===e.length&&e[0]===e[1]&&e[2]===e[3]&&e[4]===e[5]&&(e=e[0]+e[2]+e[4]),"#"+e}function o(e){return parseInt(e,16)}function i(e){return"string"==typeof e&&e.indexOf("%")===e.length-1}function f(e){return parseInt(Math.round(2.55*e.slice(0,-1)),10)}function u(e){return parseFloat(e.slice(0,-1)/100,10)}Object.defineProperty(e,"__esModule",{value:!0});var s=function(e){return e&&e.__esModule?e:{default:e}}(t),l="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},h=function(){function e(e,t){for(var r=0;r<t.length;r++){var a=t[r];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(e,a.key,a)}}return function(t,r,a){return r&&e(t.prototype,r),a&&e(t,a),t}}(),d={format:!1,shortenHex:!1,hexUseName:!1,reduceAlpha:!1,alphaConvert:{RGB:"RGBA",HSL:"HSLA",HEX:"RGBA",NAMESPACE:"RGBA"},nameDegradation:"HEX",invalidValue:"",zeroAlphaAsTransparent:!0},c={aliceblue:"f0f8ff",antiquewhite:"faebd7",aqua:"0ff",aquamarine:"7fffd4",azure:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"000",blanchedalmond:"ffebcd",blue:"00f",blueviolet:"8a2be2",brown:"a52a2a",burlywood:"deb887",burntsienna:"ea7e5d",cadetblue:"5f9ea0",chartreuse:"7fff00",chocolate:"d2691e",coral:"ff7f50",cornflowerblue:"6495ed",cornsilk:"fff8dc",crimson:"dc143c",cyan:"0ff",darkblue:"00008b",darkcyan:"008b8b",darkgoldenrod:"b8860b",darkgray:"a9a9a9",darkgreen:"006400",darkgrey:"a9a9a9",darkkhaki:"bdb76b",darkmagenta:"8b008b",darkolivegreen:"556b2f",darkorange:"ff8c00",darkorchid:"9932cc",darkred:"8b0000",darksalmon:"e9967a",darkseagreen:"8fbc8f",darkslateblue:"483d8b",darkslategray:"2f4f4f",darkslategrey:"2f4f4f",darkturquoise:"00ced1",darkviolet:"9400d3",deeppink:"ff1493",deepskyblue:"00bfff",dimgray:"696969",dimgrey:"696969",dodgerblue:"1e90ff",firebrick:"b22222",floralwhite:"fffaf0",forestgreen:"228b22",fuchsia:"f0f",gainsboro:"dcdcdc",ghostwhite:"f8f8ff",gold:"ffd700",goldenrod:"daa520",gray:"808080",green:"008000",greenyellow:"adff2f",grey:"808080",honeydew:"f0fff0",hotpink:"ff69b4",indianred:"cd5c5c",indigo:"4b0082",ivory:"fffff0",khaki:"f0e68c",lavender:"e6e6fa",lavenderblush:"fff0f5",lawngreen:"7cfc00",lemonchiffon:"fffacd",lightblue:"add8e6",lightcoral:"f08080",lightcyan:"e0ffff",lightgoldenrodyellow:"fafad2",lightgray:"d3d3d3",lightgreen:"90ee90",lightgrey:"d3d3d3",lightpink:"ffb6c1",lightsalmon:"ffa07a",lightseagreen:"20b2aa",lightskyblue:"87cefa",lightslategray:"789",lightslategrey:"789",lightsteelblue:"b0c4de",lightyellow:"ffffe0",lime:"0f0",limegreen:"32cd32",linen:"faf0e6",magenta:"f0f",maroon:"800000",mediumaquamarine:"66cdaa",mediumblue:"0000cd",mediumorchid:"ba55d3",mediumpurple:"9370db",mediumseagreen:"3cb371",mediumslateblue:"7b68ee",mediumspringgreen:"00fa9a",mediumturquoise:"48d1cc",mediumvioletred:"c71585",midnightblue:"191970",mintcream:"f5fffa",mistyrose:"ffe4e1",moccasin:"ffe4b5",navajowhite:"ffdead",navy:"000080",oldlace:"fdf5e6",olive:"808000",olivedrab:"6b8e23",orange:"ffa500",orangered:"ff4500",orchid:"da70d6",palegoldenrod:"eee8aa",palegreen:"98fb98",paleturquoise:"afeeee",palevioletred:"db7093",papayawhip:"ffefd5",peachpuff:"ffdab9",peru:"cd853f",pink:"ffc0cb",plum:"dda0dd",powderblue:"b0e0e6",purple:"800080",red:"f00",rosybrown:"bc8f8f",royalblue:"4169e1",saddlebrown:"8b4513",salmon:"fa8072",sandybrown:"f4a460",seagreen:"2e8b57",seashell:"fff5ee",sienna:"a0522d",silver:"c0c0c0",skyblue:"87ceeb",slateblue:"6a5acd",slategray:"708090",slategrey:"708090",snow:"fffafa",springgreen:"00ff7f",steelblue:"4682b4",tan:"d2b48c",teal:"008080",thistle:"d8bfd8",tomato:"ff6347",turquoise:"40e0d0",violet:"ee82ee",wheat:"f5deb3",white:"fff",whitesmoke:"f5f5f5",yellow:"ff0",yellowgreen:"9acd32"},v=function(e){var t={};for(var r in e)e.hasOwnProperty(r)&&(t[e[r]]=r);return t}(c),b={HSLtoRGB:function(e){var t=e.h/360,r=e.s,a=e.l,n=void 0,o=void 0,i=void 0;return o=a<=.5?a*(r+1):a+r-a*r,n=2*a-o,i={r:this.hueToRGB(n,o,t+1/3),g:this.hueToRGB(n,o,t),b:this.hueToRGB(n,o,t-1/3)},void 0!==e.a&&(i.a=e.a),0===e.l&&(i.h=e.h),1===e.l&&(i.h=e.h),i},hueToRGB:function(e,t,r){var a=void 0;return r<0?r+=1:r>1&&(r-=1),a=6*r<1?e+(t-e)*r*6:2*r<1?t:3*r<2?e+(t-e)*(2/3-r)*6:e,Math.round(255*a)},RGBtoHSL:function(e){var t=e.r/255,r=e.g/255,a=e.b/255,n=Math.min(t,r,a),o=Math.max(t,r,a),i=o-n,f=o+n,u=.5*f,s=void 0,l=void 0;return s=n===o?0:t===o?60*(r-a)/i+360:r===o?60*(a-t)/i+120:60*(t-r)/i+240,l=0===i?0:u<=.5?i/f:i/(2-f),{h:Math.round(s)%360,s:l,l:u}},RGBtoHEX:function(e){var t=[e.r.toString(16),e.g.toString(16),e.b.toString(16)];return s.default.each(t,function(e,r){1===r.length&&(t[e]="0"+r)}),"#"+t.join("")},HSLtoHEX:function(e){var t=this.HSLtoRGB(e);return this.RGBtoHEX(t)},HSVtoHEX:function(e){var t=this.HSVtoRGB(e);return this.RGBtoHEX(t)},RGBtoHSV:function(e){var t=e.r/255,r=e.g/255,a=e.b/255,n=Math.max(t,r,a),o=Math.min(t,r,a),i=void 0,f=void 0,u=n,s=n-o;if(f=0===n?0:s/n,n===o)i=0;else{switch(n){case t:i=(r-a)/s+(r<a?6:0);break;case r:i=(a-t)/s+2;break;case a:i=(t-r)/s+4}i/=6}return{h:Math.round(360*i),s:f,v:u}},HSVtoRGB:function(e){var t=void 0,r=void 0,a=void 0,n=e.h%360/60,o=e.s,i=e.v,f=i*o,u=f*(1-Math.abs(n%2-1));t=r=a=i-f,t+=[f,u,0,0,u,f][n=~~n],r+=[u,f,f,u,0,0][n],a+=[0,0,u,f,f,u][n];var s={r:Math.round(255*t),g:Math.round(255*r),b:Math.round(255*a)};return void 0!==e.a&&(s.a=e.a),0===e.v&&(s.h=e.h),1===e.v&&0===e.s&&(s.h=e.h),s},HEXtoRGB:function(e){return 4===e.length&&(e=a(e)),{r:o(e.substr(1,2)),g:o(e.substr(3,2)),b:o(e.substr(5,2))}},isNAME:function(e){return!!c.hasOwnProperty(e)},NAMEtoHEX:function(e){return c.hasOwnProperty(e)?"#"+c[e]:null},NAMEtoRGB:function(e){var t=this.NAMEtoHEX(e);return t?this.HEXtoRGB(t):null},hasNAME:function(e){var t=this.RGBtoHEX(e);return 0===(t=n(t)).indexOf("#")&&(t=t.substr(1)),!!v.hasOwnProperty(t)&&v[t]},RGBtoNAME:function(e){var t=this.hasNAME(e);return t||null}},p="(?:[-\\+]?\\d*\\.\\d+%?)|(?:[-\\+]?\\d+%?)",g="[\\s|\\(]+("+p+")[,|\\s]+("+p+")[,|\\s]+("+p+")\\s*\\)",m="[\\s|\\(]+("+p+")[,|\\s]+("+p+")[,|\\s]+("+p+")[,|\\s]+("+p+")\\s*\\)",y={RGB:{match:new RegExp("^rgb"+g+"$","i"),parse:function(e){return{r:i(e[1])?f(e[1]):parseInt(e[1],10),g:i(e[2])?f(e[2]):parseInt(e[2],10),b:i(e[3])?f(e[3]):parseInt(e[3],10),a:1}},to:function(e){return"rgb("+e.r+", "+e.g+", "+e.b+")"}},RGBA:{match:new RegExp("^rgba"+m+"$","i"),parse:function(e){return{r:i(e[1])?f(e[1]):parseInt(e[1],10),g:i(e[2])?f(e[2]):parseInt(e[2],10),b:i(e[3])?f(e[3]):parseInt(e[3],10),a:i(e[4])?u(e[4]):parseFloat(e[4],10)}},to:function(e){return"rgba("+e.r+", "+e.g+", "+e.b+", "+e.a+")"}},HSL:{match:new RegExp("^hsl"+g+"$","i"),parse:function(e){var t={h:(e[1]%360+360)%360,s:i(e[2])?u(e[2]):parseFloat(e[2],10),l:i(e[3])?u(e[3]):parseFloat(e[3],10),a:1};return b.HSLtoRGB(t)},to:function(e){var t=b.RGBtoHSL(e);return"hsl("+parseInt(t.h,10)+", "+Math.round(100*t.s)+"%, "+Math.round(100*t.l)+"%)"}},HSLA:{match:new RegExp("^hsla"+m+"$","i"),parse:function(e){var t={h:(e[1]%360+360)%360,s:i(e[2])?u(e[2]):parseFloat(e[2],10),l:i(e[3])?u(e[3]):parseFloat(e[3],10),a:i(e[4])?u(e[4]):parseFloat(e[4],10)};return b.HSLtoRGB(t)},to:function(e){var t=b.RGBtoHSL(e);return"hsla("+parseInt(t.h,10)+", "+Math.round(100*t.s)+"%, "+Math.round(100*t.l)+"%, "+e.a+")"}},HEX:{match:/^#([a-f0-9]{6}|[a-f0-9]{3})$/i,parse:function(e){var t=e[0],r=b.HEXtoRGB(t);return{r:r.r,g:r.g,b:r.b,a:1}},to:function(e,t){var r=b.RGBtoHEX(e);if(t){if(t.options.hexUseName){var a=b.hasNAME(e);if(a)return a}t.options.shortenHex&&(r=n(r))}return""+r}},TRANSPARENT:{match:/^transparent$/i,parse:function(){return{r:0,g:0,b:0,a:0}},to:function(){return"transparent"}},NAME:{match:/^\w+$/i,parse:function(e){var t=b.NAMEtoRGB(e[0]);return t?{r:t.r,g:t.g,b:t.b,a:1}:null},to:function(e,t){var r=b.RGBtoNAME(e);return r||y[t.options.nameDegradation.toUpperCase()].to(e)}}};String.prototype.includes||(String.prototype.includes=function(e,t){return"number"!=typeof t&&(t=0),!(t+e.length>this.length)&&-1!==this.indexOf(e,t)});var k=function(){function e(t,a){r(this,e),"object"===(void 0===t?"undefined":l(t))&&void 0===a&&(a=t,t=void 0),"string"==typeof a&&(a={format:a}),this.options=s.default.extend(!0,{},d,a),this.value={r:0,g:0,b:0,h:0,s:0,v:0,a:1},this._format=!1,this._matchFormat="HEX",this._valid=!0,this.init(t)}return h(e,[{key:"init",value:function(e){return this.format(this.options.format),this.fromString(e),this}},{key:"isValid",value:function(){return this._valid}},{key:"val",value:function(e){return void 0===e?this.toString():(this.fromString(e),this)}},{key:"alpha",value:function(e){return void 0===e||isNaN(e)?this.value.a:((e=parseFloat(e))>1?e=1:e<0&&(e=0),this.value.a=e,this)}},{key:"matchString",value:function(t){return e.matchString(t)}},{key:"fromString",value:function(e,t){if("string"==typeof e){e=s.default.trim(e);var r=null,a=void 0;this._valid=!1;for(var n in y)if(null!==(r=y[n].match.exec(e))&&(a=y[n].parse(r))){this.set(a),"TRANSPARENT"===n&&(n="HEX"),this._matchFormat=n,!0===t&&this.format(n);break}}else"object"===(void 0===e?"undefined":l(e))&&this.set(e);return this}},{key:"format",value:function(e){return"string"==typeof e&&(e=e.toUpperCase())&&void 0!==y[e]?this._format="TRANSPARENT"!==e?e:"HEX":!1===e&&(this._format=!1),!1===this._format?this._matchFormat:this._format}},{key:"toRGBA",value:function(){return y.RGBA.to(this.value,this)}},{key:"toRGB",value:function(){return y.RGB.to(this.value,this)}},{key:"toHSLA",value:function(){return y.HSLA.to(this.value,this)}},{key:"toHSL",value:function(){return y.HSL.to(this.value,this)}},{key:"toHEX",value:function(){return y.HEX.to(this.value,this)}},{key:"toNAME",value:function(){return y.NAME.to(this.value,this)}},{key:"to",value:function(e){return"string"==typeof e&&(e=e.toUpperCase())&&void 0!==y[e]?y[e].to(this.value,this):this.toString()}},{key:"toString",value:function(){var e=this.value;if(!this._valid&&"string"==typeof(e=this.options.invalidValue))return e;if(0===e.a&&this.options.zeroAlphaAsTransparent)return y.TRANSPARENT.to(e,this);var t=void 0;if(t=!1===this._format?this._matchFormat:this._format,this.options.reduceAlpha&&1===e.a)switch(t){case"RGBA":t="RGB";break;case"HSLA":t="HSL"}return 1!==e.a&&"RGBA"!==t&&"HSLA"!==t&&this.options.alphaConvert&&("string"==typeof this.options.alphaConvert&&(t=this.options.alphaConvert),void 0!==this.options.alphaConvert[t]&&(t=this.options.alphaConvert[t])),y[t].to(e,this)}},{key:"get",value:function(){return this.value}},{key:"set",value:function(e){this._valid=!0;var t=0,r=0,a=void 0,n=void 0;for(var o in e)"hsv".includes(o)?(r++,this.value[o]=e[o]):"rgb".includes(o)?(t++,this.value[o]=e[o]):"a"===o&&(this.value.a=e.a);return t>r?(a=b.RGBtoHSV(this.value),0===this.value.r&&0===this.value.g&&0===this.value.b||(this.value.h=a.h),this.value.s=a.s,this.value.v=a.v):r>t&&(n=b.HSVtoRGB(this.value),this.value.r=n.r,this.value.g=n.g,this.value.b=n.b),this}}],[{key:"matchString",value:function(e){if("string"==typeof e){e=s.default.trim(e);var t=null;for(var r in y)if(null!==(t=y[r].match.exec(e))&&y[r].parse(t))return!0}return!1}},{key:"setDefaults",value:function(e){s.default.extend(!0,d,s.default.isPlainObject(e)&&e)}}]),e}(),R={version:"0.3.6"},S=s.default.asColor,H=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return new(Function.prototype.bind.apply(k,[null].concat(t)))};s.default.asColor=H,s.default.asColor.Constructor=k,s.default.extend(s.default.asColor,{matchString:k.matchString,setDefaults:k.setDefaults,noConflict:function(){return s.default.asColor=S,H}},b,R);var A=s.default.asColor;e.default=A});
//# sourceMappingURL=jquery-asColor.min.js.map
