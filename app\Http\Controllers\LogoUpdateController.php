<?php

namespace App\Http\Controllers;

use App\Models\Settings;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Artisan;

class LogoUpdateController extends Controller
{
    public function updateLogos()
    {
        try {
            // Update logo1 (horizontal logo)
            if (Settings::where('type', 'logo1')->exists()) {
                Settings::where('type', 'logo1')->update([
                    'message' => 'logo/madrasa-logo.png'
                ]);
            } else {
                $setting = new Settings();
                $setting->type = 'logo1';
                $setting->message = 'logo/madrasa-logo.png';
                $setting->save();
            }

            // Update logo2 (vertical logo)
            if (Settings::where('type', 'logo2')->exists()) {
                Settings::where('type', 'logo2')->update([
                    'message' => 'logo/madrasa-logo.png'
                ]);
            } else {
                $setting = new Settings();
                $setting->type = 'logo2';
                $setting->message = 'logo/madrasa-logo.png';
                $setting->save();
            }

            // Update favicon
            if (Settings::where('type', 'favicon')->exists()) {
                Settings::where('type', 'favicon')->update([
                    'message' => 'logo/madrasa-favicon.png'
                ]);
            } else {
                $setting = new Settings();
                $setting->type = 'favicon';
                $setting->message = 'logo/madrasa-favicon.png';
                $setting->save();
            }

            // Update environment variables
            $env_update = $this->changeEnv([
                'LOGO1' => 'logo/madrasa-logo.png',
                'LOGO2' => 'logo/madrasa-logo.png',
                'FAVICON' => 'logo/madrasa-favicon.png',
            ]);

            // Clear cache
            Artisan::call('config:clear');
            Artisan::call('cache:clear');
            Artisan::call('view:clear');

            return "Logo settings updated successfully.";
        } catch (\Exception $e) {
            return "Error updating logo settings: " . $e->getMessage();
        }
    }

    private function changeEnv($data = array())
    {
        if (count($data) > 0) {
            // Read .env file
            $env = file_get_contents(base_path() . '/.env');
            
            // Loop through the data and update the values
            foreach ($data as $key => $value) {
                // Replace or create new environment value
                if (strpos($env, "{$key}=") !== false) {
                    $env = preg_replace("/{$key}=.*/", "{$key}={$value}", $env);
                } else {
                    $env .= "\n{$key}={$value}";
                }
            }
            
            // Write the updated content back to .env
            file_put_contents(base_path() . '/.env', $env);
            return true;
        }
        return false;
    }
}