<?php
// Improved database connection test script

echo "Testing database connection with improved configuration...\n";

$servername = "127.0.0.1";
$username = "root";
$password = "";
$dbname = "madrasa";
$port = 3306;

try {
    // Create connection with improved settings
    $pdo = new PDO("mysql:host=$servername;port=$port;dbname=$dbname;charset=utf8mb4", $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_PERSISTENT => false, // Disable persistent connections
        PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci",
        PDO::ATTR_TIMEOUT => 60,
    ]);
    
    echo "Connected successfully\n";
    
    // Test query
    $stmt = $pdo->query("SELECT VERSION()");
    $version = $stmt->fetch();
    echo "Database Version: " . $version[0] . "\n";
    
    // Check timeout settings
    $stmt = $pdo->query("SHOW VARIABLES LIKE 'wait_timeout'");
    $wait_timeout = $stmt->fetch();
    echo "Wait Timeout: " . $wait_timeout['Value'] . "\n";
    
    $stmt = $pdo->query("SHOW VARIABLES LIKE 'interactive_timeout'");
    $interactive_timeout = $stmt->fetch();
    echo "Interactive Timeout: " . $interactive_timeout['Value'] . "\n";
    
    $stmt = $pdo->query("SHOW VARIABLES LIKE 'max_allowed_packet'");
    $max_allowed_packet = $stmt->fetch();
    echo "Max Allowed Packet: " . $max_allowed_packet['Value'] . " bytes\n";
    
    // Test a simple query
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll();
    echo "Number of tables in database: " . count($tables) . "\n";
    
    echo "Database connection test completed successfully!\n";
    
} catch(PDOException $e) {
    echo "Connection failed: " . $e->getMessage() . "\n";
    
    // Additional error information
    echo "Error Code: " . $e->getCode() . "\n";
    echo "Error Line: " . $e->getLine() . "\n";
    echo "Error File: " . $e->getFile() . "\n";
}
?>