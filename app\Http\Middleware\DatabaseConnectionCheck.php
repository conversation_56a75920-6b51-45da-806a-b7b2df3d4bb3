<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Schema;

class DatabaseConnectionCheck
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        // Skip database check for static assets
        if ($this->isAssetRequest($request)) {
            return $next($request);
        }
        
        try {
            // Test database connection
            DB::connection()->getPdo();
            
            // Additional check to ensure we can actually query the database
            // This helps catch cases where connection exists but is unusable
            Schema::hasTable('users');
        } catch (\Exception $e) {
            Log::error('Database connection failed: ' . $e->getMessage());
            
            // For API requests, return JSON response
            if ($request->wantsJson() || $request->is('api/*')) {
                return response()->json([
                    'error' => 'Service temporarily unavailable',
                    'message' => 'Database connection failed. Please try again later.'
                ], 503);
            }
            
            // For web requests, show maintenance page
            return response()->view('errors.maintenance', [
                'error' => $e->getMessage()
            ], 503);
        }

        return $next($request);
    }
    
    /**
     * Determine if the request is for a static asset.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return bool
     */
    private function isAssetRequest($request)
    {
        $assetExtensions = ['css', 'js', 'png', 'jpg', 'jpeg', 'gif', 'ico', 'svg', 'woff', 'woff2', 'ttf', 'eot'];
        $path = $request->path();
        
        foreach ($assetExtensions as $extension) {
            if (substr($path, -strlen($extension)) === $extension) {
                return true;
            }
        }
        
        return false;
    }
}