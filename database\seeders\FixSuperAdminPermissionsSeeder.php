<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use <PERSON><PERSON>\Permission\Models\Role;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;

class FixSuperAdminPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds to fix missing permissions for Super Admin role.
     *
     * @return void
     */
    public function run()
    {
        // Get the Super Admin role
        $superAdminRole = Role::where('name', 'Super Admin')->first();
        
        if ($superAdminRole) {
            // Get current permissions
            $currentPermissions = $superAdminRole->permissions->pluck('name')->toArray();
            
            // Add missing permissions
            $missingPermissions = [
                'class-teacher',
                'lesson-list',
                'lesson-create',
                'lesson-edit',
                'lesson-delete',
                'topic-list',
                'topic-create',
                'topic-edit',
                'topic-delete',
                'assignment-create',
                'manage-online-exam',
                'role-create',
                'attendance-create',
                'exam-upload-marks',
                'exam-result'
            ];
            
            // Merge current and missing permissions
            $allPermissions = array_unique(array_merge($currentPermissions, $missingPermissions));
            
            // Sync all permissions to the Super Admin role
            $superAdminRole->syncPermissions($allPermissions);
            
            echo "Super Admin permissions have been updated successfully.\n";
        } else {
            echo "Super Admin role not found.\n";
        }
    }
}