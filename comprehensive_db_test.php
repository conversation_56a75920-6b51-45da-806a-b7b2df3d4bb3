<?php
// Comprehensive database connection test script

echo "Comprehensive Database Connection Test\n";
echo "=====================================\n\n";

// Test 1: Check if PDO is available
echo "Test 1: Checking PDO availability...\n";
if (!extension_loaded('pdo')) {
    die("ERROR: PDO extension is not loaded!\n");
}
echo "SUCCESS: PDO extension is available.\n\n";

// Test 2: Check if PDO MySQL driver is available
echo "Test 2: Checking PDO MySQL driver...\n";
$drivers = PDO::getAvailableDrivers();
if (!in_array('mysql', $drivers)) {
    die("ERROR: PDO MySQL driver is not available!\n");
}
echo "SUCCESS: PDO MySQL driver is available.\n\n";

// Test 3: Check MySQL configuration from .env (manual parsing)
echo "Test 3: Loading configuration from .env...\n";
$host = '127.0.0.1';
$port = '3306';
$dbname = 'madrasa';
$username = 'root';
$password = '';
$timeout = '300';

// Manual parsing of .env file
if (file_exists('.env')) {
    $lines = file('.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        // Skip comments
        if (strpos(trim($line), '#') === 0) {
            continue;
        }
        
        // Parse key=value pairs
        if (strpos($line, '=') !== false) {
            list($key, $value) = explode('=', $line, 2);
            $key = trim($key);
            $value = trim($value);
            
            // Remove surrounding quotes if present
            if (preg_match('/^"(.*)"$/', $value, $matches)) {
                $value = $matches[1];
            } elseif (preg_match('/^\'(.*)\'$/', $value, $matches)) {
                $value = $matches[1];
            }
            
            // Set variables based on keys
            switch ($key) {
                case 'DB_HOST':
                    $host = $value;
                    break;
                case 'DB_PORT':
                    $port = $value;
                    break;
                case 'DB_DATABASE':
                    $dbname = $value;
                    break;
                case 'DB_USERNAME':
                    $username = $value;
                    break;
                case 'DB_PASSWORD':
                    $password = $value;
                    break;
                case 'DB_TIMEOUT':
                    $timeout = $value;
                    break;
            }
        }
    }
}

echo "Host: $host\n";
echo "Port: $port\n";
echo "Database: $dbname\n";
echo "Username: $username\n";
echo "Timeout: $timeout seconds\n\n";

// Test 4: Attempt to connect to MySQL
echo "Test 4: Attempting to connect to MySQL...\n";
try {
    $dsn = "mysql:host=$host;port=$port;charset=utf8mb4";
    $options = [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_PERSISTENT => false,
        PDO::ATTR_TIMEOUT => (int)$timeout,
        PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4",
    ];
    
    $pdo = new PDO($dsn, $username, $password, $options);
    echo "SUCCESS: Connected to MySQL server!\n\n";
    
    // Test 5: Check if the specific database exists
    echo "Test 5: Checking if database '$dbname' exists...\n";
    try {
        $stmt = $pdo->query("SHOW DATABASES LIKE '$dbname'");
        $result = $stmt->fetch();
        if ($result) {
            echo "SUCCESS: Database '$dbname' exists.\n\n";
            
            // Test 6: Try to connect to the specific database
            echo "Test 6: Attempting to connect to database '$dbname'...\n";
            $dsn_with_db = "mysql:host=$host;port=$port;dbname=$dbname;charset=utf8mb4";
            $pdo_with_db = new PDO($dsn_with_db, $username, $password, $options);
            echo "SUCCESS: Connected to database '$dbname'!\n\n";
            
            // Test 7: Check MySQL server variables
            echo "Test 7: Checking MySQL server variables...\n";
            try {
                $stmt = $pdo_with_db->query("SELECT VERSION() as version");
                $result = $stmt->fetch();
                echo "MySQL Version: " . $result['version'] . "\n";
                
                $stmt = $pdo_with_db->query("SHOW VARIABLES LIKE 'wait_timeout'");
                $result = $stmt->fetch();
                echo "Wait Timeout: " . $result['Value'] . " seconds\n";
                
                $stmt = $pdo_with_db->query("SHOW VARIABLES LIKE 'interactive_timeout'");
                $result = $stmt->fetch();
                echo "Interactive Timeout: " . $result['Value'] . " seconds\n";
                
                $stmt = $pdo_with_db->query("SHOW VARIABLES LIKE 'max_allowed_packet'");
                $result = $stmt->fetch();
                echo "Max Allowed Packet: " . $result['Value'] . " bytes\n";
                
                echo "SUCCESS: All MySQL variables checked.\n\n";
            } catch (PDOException $e) {
                echo "WARNING: Could not retrieve MySQL variables: " . $e->getMessage() . "\n\n";
            }
            
            // Test 8: Test a simple query
            echo "Test 8: Testing simple query...\n";
            try {
                $stmt = $pdo_with_db->query("SELECT 1 as test");
                $result = $stmt->fetch();
                if ($result['test'] == 1) {
                    echo "SUCCESS: Simple query executed successfully.\n\n";
                } else {
                    echo "WARNING: Unexpected result from simple query.\n\n";
                }
            } catch (PDOException $e) {
                echo "ERROR: Simple query failed: " . $e->getMessage() . "\n\n";
            }
            
            // Test 9: Show tables in the database
            echo "Test 9: Listing tables in database '$dbname'...\n";
            try {
                $stmt = $pdo_with_db->query("SHOW TABLES");
                $tables = $stmt->fetchAll();
                echo "Number of tables found: " . count($tables) . "\n";
                if (count($tables) > 0) {
                    echo "Tables:\n";
                    foreach ($tables as $table) {
                        echo "  - " . reset($table) . "\n";
                    }
                }
                echo "SUCCESS: Tables listed successfully.\n\n";
            } catch (PDOException $e) {
                echo "WARNING: Could not list tables: " . $e->getMessage() . "\n\n";
            }
        } else {
            echo "WARNING: Database '$dbname' does not exist.\n\n";
            
            // Try to create the database
            echo "Attempting to create database '$dbname'...\n";
            try {
                $pdo->exec("CREATE DATABASE `$dbname` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
                echo "SUCCESS: Database '$dbname' created successfully.\n\n";
            } catch (PDOException $e) {
                echo "ERROR: Could not create database '$dbname': " . $e->getMessage() . "\n\n";
            }
        }
    } catch (PDOException $e) {
        echo "ERROR: Could not check databases: " . $e->getMessage() . "\n\n";
    }
    
    echo "All tests completed!\n";
    
} catch (PDOException $e) {
    echo "ERROR: Database connection failed!\n";
    echo "Error Code: " . $e->getCode() . "\n";
    echo "Error Message: " . $e->getMessage() . "\n\n";
    
    // Provide troubleshooting suggestions
    echo "Troubleshooting suggestions:\n";
    echo "1. Make sure MySQL service is running\n";
    echo "2. Check if the database '$dbname' exists\n";
    echo "3. Verify username and password are correct\n";
    echo "4. Check if MySQL is listening on host '$host' and port '$port'\n";
    echo "5. Ensure firewall is not blocking the connection\n";
    echo "6. Check MySQL error logs for more details\n";
}
?>